import React, { useState } from 'react'
import jsPDF from 'jspdf'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import './DataExportModal.css'

const DataExportModal = ({ isOpen, onClose, data, title = "DRDO Data Export" }) => {
  const [exportFormat, setExportFormat] = useState('json')
  const [includeMetadata, setIncludeMetadata] = useState(true)
  const [securityLevel, setSecurityLevel] = useState('PUBLIC')
  const [isExporting, setIsExporting] = useState(false)

  if (!isOpen) return null

  const generateMetadata = () => ({
    title,
    exportDate: new Date().toISOString(),
    exportedBy: 'DRDO User',
    securityLevel,
    dataPoints: Array.isArray(data) ? data.length : Object.keys(data).length,
    source: 'IDX Dashboard v1.2',
    organization: 'Defence Research and Development Organisation'
  })

  const exportToJSON = () => {
    const exportData = {
      ...(includeMetadata && { metadata: generateMetadata() }),
      data
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    saveAs(blob, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.json`)
  }

  const exportToExcel = () => {
    const workbook = XLSX.utils.book_new()
    
    // Add metadata sheet if requested
    if (includeMetadata) {
      const metadata = generateMetadata()
      const metadataSheet = XLSX.utils.json_to_sheet([metadata])
      XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Metadata')
    }
    
    // Add data sheet
    let dataSheet
    if (Array.isArray(data)) {
      dataSheet = XLSX.utils.json_to_sheet(data)
    } else {
      // Convert object to array format
      const dataArray = Object.entries(data).map(([key, value]) => ({
        Property: key,
        Value: typeof value === 'object' ? JSON.stringify(value) : value
      }))
      dataSheet = XLSX.utils.json_to_sheet(dataArray)
    }
    
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Data')
    
    const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`
    XLSX.writeFile(workbook, fileName)
  }

  const exportToPDF = () => {
    const pdf = new jsPDF()
    const pageWidth = pdf.internal.pageSize.getWidth()
    const margin = 20
    let yPosition = margin

    // Header
    pdf.setFontSize(16)
    pdf.setFont(undefined, 'bold')
    pdf.text('Defence Research and Development Organisation', margin, yPosition)
    yPosition += 10

    pdf.setFontSize(14)
    pdf.text(title, margin, yPosition)
    yPosition += 15

    // Metadata
    if (includeMetadata) {
      const metadata = generateMetadata()
      pdf.setFontSize(10)
      pdf.setFont(undefined, 'normal')
      
      Object.entries(metadata).forEach(([key, value]) => {
        if (yPosition > 270) {
          pdf.addPage()
          yPosition = margin
        }
        pdf.text(`${key}: ${value}`, margin, yPosition)
        yPosition += 6
      })
      yPosition += 10
    }

    // Data summary
    pdf.setFontSize(12)
    pdf.setFont(undefined, 'bold')
    pdf.text('Data Summary:', margin, yPosition)
    yPosition += 10

    pdf.setFontSize(10)
    pdf.setFont(undefined, 'normal')
    
    if (Array.isArray(data)) {
      pdf.text(`Total Records: ${data.length}`, margin, yPosition)
      yPosition += 6
      
      if (data.length > 0) {
        pdf.text('Sample Data:', margin, yPosition)
        yPosition += 6
        
        const sampleData = data.slice(0, 5)
        sampleData.forEach((item, index) => {
          if (yPosition > 270) {
            pdf.addPage()
            yPosition = margin
          }
          pdf.text(`${index + 1}. ${JSON.stringify(item)}`, margin, yPosition)
          yPosition += 6
        })
      }
    } else {
      Object.entries(data).forEach(([key, value]) => {
        if (yPosition > 270) {
          pdf.addPage()
          yPosition = margin
        }
        const displayValue = typeof value === 'object' ? JSON.stringify(value) : value
        pdf.text(`${key}: ${displayValue}`, margin, yPosition)
        yPosition += 6
      })
    }

    // Security notice
    yPosition += 10
    pdf.setFontSize(8)
    pdf.setFont(undefined, 'italic')
    pdf.text(`Security Level: ${securityLevel}`, margin, yPosition)
    pdf.text('This document contains information subject to DRDO security protocols.', margin, yPosition + 6)

    const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
    pdf.save(fileName)
  }

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      switch (exportFormat) {
        case 'json':
          exportToJSON()
          break
        case 'excel':
          exportToExcel()
          break
        case 'pdf':
          exportToPDF()
          break
        default:
          throw new Error('Unsupported export format')
      }
      
      // Close modal after successful export
      setTimeout(() => {
        onClose()
      }, 1000)
      
    } catch (error) {
      alert(`Export failed: ${error.message}`)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="export-modal-overlay" onClick={onClose}>
      <div className="export-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Export Data</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          <div className="export-options">
            <div className="option-group">
              <label>Export Format:</label>
              <div className="format-options">
                <label className="radio-option">
                  <input
                    type="radio"
                    value="json"
                    checked={exportFormat === 'json'}
                    onChange={(e) => setExportFormat(e.target.value)}
                  />
                  <span>JSON</span>
                  <small>Machine-readable format</small>
                </label>
                <label className="radio-option">
                  <input
                    type="radio"
                    value="excel"
                    checked={exportFormat === 'excel'}
                    onChange={(e) => setExportFormat(e.target.value)}
                  />
                  <span>Excel</span>
                  <small>Spreadsheet format</small>
                </label>
                <label className="radio-option">
                  <input
                    type="radio"
                    value="pdf"
                    checked={exportFormat === 'pdf'}
                    onChange={(e) => setExportFormat(e.target.value)}
                  />
                  <span>PDF</span>
                  <small>Document format</small>
                </label>
              </div>
            </div>

            <div className="option-group">
              <label className="checkbox-option">
                <input
                  type="checkbox"
                  checked={includeMetadata}
                  onChange={(e) => setIncludeMetadata(e.target.checked)}
                />
                <span>Include metadata and export information</span>
              </label>
            </div>

            <div className="option-group">
              <label>Security Classification:</label>
              <select 
                value={securityLevel}
                onChange={(e) => setSecurityLevel(e.target.value)}
                className="security-select"
              >
                <option value="PUBLIC">PUBLIC</option>
                <option value="RESTRICTED">RESTRICTED</option>
                <option value="CONFIDENTIAL">CONFIDENTIAL</option>
                <option value="SECRET">SECRET</option>
              </select>
            </div>
          </div>

          <div className="data-preview">
            <h4>Data Preview:</h4>
            <div className="preview-content">
              <div className="preview-stats">
                <span>Records: {Array.isArray(data) ? data.length : Object.keys(data).length}</span>
                <span>Type: {Array.isArray(data) ? 'Array' : 'Object'}</span>
                <span>Size: ~{JSON.stringify(data).length} bytes</span>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
          <button 
            className="export-btn"
            onClick={handleExport}
            disabled={isExporting}
          >
            {isExporting ? 'Exporting...' : `Export as ${exportFormat.toUpperCase()}`}
          </button>
        </div>
      </div>
    </div>
  )
}

export default DataExportModal
