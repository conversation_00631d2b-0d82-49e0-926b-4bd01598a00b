.equipment-health-chart {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.chart-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.filter-select {
  padding: 4px 8px;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
  font-size: 12px;
  background: #FFFFFF;
  color: #374151;
}

.health-slider {
  width: 80px;
  height: 4px;
  background: #E5E7EB;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.health-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #4A90E2;
  border-radius: 50%;
  cursor: pointer;
}

.threshold-value {
  font-size: 11px;
  font-weight: 500;
  color: #4A90E2;
  min-width: 30px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background-color: #e9ecef;
}

.filter-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.selected-system {
  font-size: 12px;
  color: #666;
  background-color: #e7f3ff;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #b3d9ff;
}

.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

.chart-info {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* D3 chart styling */
.equipment-health-chart svg {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.equipment-health-chart .axis {
  font-size: 12px;
}

.equipment-health-chart .axis path,
.equipment-health-chart .axis line {
  fill: none;
  stroke: #666;
  shape-rendering: crispEdges;
}

.equipment-health-chart .grid line {
  stroke: #e0e0e0;
  stroke-dasharray: 2,2;
}

.equipment-health-chart .grid path {
  stroke-width: 0;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chart-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .info-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
