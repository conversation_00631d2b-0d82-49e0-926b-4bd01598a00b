{"mappings": ";;;;;AAsBA;IACE,0CAA0C;IAC1C,SAAS,EAAE,aAAa,CAAC;IACzB,wDAAwD;IACxD,UAAU,EAAE,aAAa,CAAC;IAC1B,2DAA2D;IAC3D,gBAAgB,EAAE,aAAa,CAAC;IAChC,6DAA6D;IAC7D,iBAAiB,EAAE,aAAa,CAAA;CACjC;AAED,mCAAmC,CAAC,CAAE,SAAQ,eAAe,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,EAAE,cAAc,EAAE,iBAAiB,EAAE,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC;IAClK,8DAA8D;IAC9D,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,wEAAwE;IACxE,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,yDAAyD;IACzD,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;IACpC,sCAAsC;IACtC,YAAY,CAAC,EAAE,SAAS,CAAA;CACzB;AAED,8BAAqC,CAAC,CAAE,SAAQ,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,UAAU,CAAC;IACpF;;;OAGG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAA;CACpC;AAQD;;;;;;GAMG;AACH,4BAA4B,CAAC,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,WAAW,GAAG,IAAI,CAAC,GAAG,YAAY,CAqDnI;AC5FD,wBAAyB,SAAQ,IAAI,CAAC,oBAAoB,EAAE,WAAW,CAAC;IACtE,qCAAqC;IACrC,QAAQ,EAAE,aAAa,CAAC;IACxB,sCAAsC;IACtC,aAAa,EAAE,aAAa,CAAC;IAC7B,uCAAuC;IACvC,iBAAiB,EAAE,eAAe,CAAC;IACnC,sCAAsC;IACtC,cAAc,EAAE,OAAO,CAAA;CACxB;AAED,8BAA8B,CAAC;IAC7B,mGAAmG;IACnG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;CACd;AAED;;;;;GAKG;AACH,uBAAuB,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,OAAO,CAqEvH", "sources": ["packages/@react-aria/tag/src/packages/@react-aria/tag/src/useTagGroup.ts", "packages/@react-aria/tag/src/packages/@react-aria/tag/src/useTag.ts", "packages/@react-aria/tag/src/packages/@react-aria/tag/src/index.ts", "packages/@react-aria/tag/src/index.ts"], "sourcesContent": [null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {useTag} from './useTag';\nexport {useTagGroup} from './useTagGroup';\n\nexport type {TagGroupAria, AriaTagGroupProps} from './useTagGroup';\nexport type {AriaTagProps, TagAria} from './useTag';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}