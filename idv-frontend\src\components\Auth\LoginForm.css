/* DRDO Login Form Styling */
.login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 60, 94, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: overlayFadeIn 0.3s ease-out;
}

.login-modal {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 480px;
  margin: 20px;
  overflow: hidden;
  animation: modalSlideIn 0.4s ease-out;
}

.login-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2D5A87 100%);
  color: #FFFFFF;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.drdo-branding {
  display: flex;
  align-items: center;
  gap: 16px;
}

.drdo-emblem {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.login-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.3px;
}

.login-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #E2E8F0;
  font-weight: 400;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #FFFFFF;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.login-form {
  padding: 32px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1A3C5E;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #FFFFFF;
  color: #1A3C5E;
}

.form-group input:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input:disabled {
  background: #F9FAFB;
  color: #9CA3AF;
  cursor: not-allowed;
}

.error-message {
  background: #FEE2E2;
  color: #DC2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  border: 1px solid #FECACA;
}

.error-icon {
  font-size: 16px;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #FFFFFF;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.login-btn:disabled {
  background: #9CA3AF;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.demo-section {
  border-top: 1px solid #E5E7EB;
  padding: 24px;
  background: #F8FAFC;
}

.demo-header {
  text-align: center;
  margin-bottom: 16px;
}

.demo-header span {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
}

.demo-header small {
  display: block;
  font-size: 12px;
  color: #6B7280;
  margin-top: 4px;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-btn {
  background: #FFFFFF;
  border: 2px solid #E5E7EB;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.demo-btn:hover:not(:disabled) {
  border-color: #4A90E2;
  background: #F0F8FF;
  transform: translateY(-1px);
}

.demo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.demo-btn small {
  font-size: 11px;
  color: #6B7280;
  font-weight: 400;
  margin-left: auto;
}

.demo-btn.admin:hover:not(:disabled) {
  border-color: #DC2626;
  background: #FEF2F2;
}

.demo-btn.scientist:hover:not(:disabled) {
  border-color: #059669;
  background: #F0FDF4;
}

.demo-btn.engineer:hover:not(:disabled) {
  border-color: #F59E0B;
  background: #FFFBEB;
}

.security-notice {
  background: #F0F8FF;
  border-top: 1px solid #E5E7EB;
  padding: 16px 24px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.security-icon {
  font-size: 16px;
  margin-top: 2px;
}

.security-text strong {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #1A3C5E;
  margin-bottom: 4px;
}

.security-text p {
  margin: 0;
  font-size: 12px;
  color: #6B7280;
  line-height: 1.4;
}

/* Animations */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-modal {
    margin: 10px;
    max-width: none;
  }
  
  .login-header {
    padding: 20px;
  }
  
  .drdo-emblem {
    font-size: 24px;
    padding: 6px;
  }
  
  .login-title h2 {
    font-size: 18px;
  }
  
  .login-form {
    padding: 24px 20px;
  }
  
  .demo-section {
    padding: 20px;
  }
  
  .demo-buttons {
    gap: 6px;
  }
  
  .demo-btn {
    padding: 10px 12px;
    font-size: 13px;
  }
}
