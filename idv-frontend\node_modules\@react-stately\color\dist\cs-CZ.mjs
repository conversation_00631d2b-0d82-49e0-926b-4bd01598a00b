var $807554f8230db0ed$exports = {};
$807554f8230db0ed$exports = {
    "alpha": `Alfa`,
    "black": `\u{10D}ern\xe1`,
    "blue": `<PERSON><PERSON>\xe1`,
    "blue purple": `m<PERSON><PERSON><PERSON><PERSON>\xe1`,
    "brightness": `Jas`,
    "brown": `hn\u{11B}d\xe1`,
    "brown yellow": `hn\u{11B}do\u{17E}lut\xe1`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `tyrkysov\xe1`,
    "cyan blue": `tyrkysovomodr\xe1`,
    "dark": `tmav\xe1`,
    "gray": `\u{161}ed\xe1`,
    "grayish": `na\u{161}edl\xe1`,
    "green": `Zelen\xe1`,
    "green cyan": `zelenotyrkysov\xe1`,
    "hue": `Odst\xedn`,
    "light": `sv\u{11B}tl\xe1`,
    "lightness": `Sv\u{11B}tlost`,
    "magenta": `purpurov\xe1`,
    "magenta pink": `purpurov\u{11B} r\u{16F}\u{17E}ov\xe1`,
    "orange": `oran\u{17E}ov\xe1`,
    "orange yellow": `oran\u{17E}ovo\u{17E}lut\xe1`,
    "pale": `bled\xe1`,
    "pink": `r\u{16F}\u{17E}ov\xe1`,
    "pink red": `r\u{16F}\u{17E}ovo\u{10D}erven\xe1`,
    "purple": `fialov\xe1`,
    "purple magenta": `fialov\u{11B} purpurov\xe1`,
    "red": `\u{10C}erven\xe1`,
    "red orange": `\u{10D}ervenooran\u{17E}ov\xe1`,
    "saturation": `Sytost`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} pr\u{16F}hledn\xe9`,
    "very dark": `velmi tmav\xe1`,
    "very light": `velmi sv\u{11B}tl\xe1`,
    "vibrant": `z\xe1\u{159}iv\xe1`,
    "white": `b\xedl\xe1`,
    "yellow": `\u{17E}lut\xe1`,
    "yellow green": `\u{17E}lutozelen\xe1`
};


export {$807554f8230db0ed$exports as default};
//# sourceMappingURL=cs-CZ.module.js.map
