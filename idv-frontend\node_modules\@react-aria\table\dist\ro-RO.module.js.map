{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,iBAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,2BAAqB,EAAE,KAAK,UAAU,CAAC,+BAAsB,CAAC;IAC1F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,oBAAc,CAAC;IAC9B,kBAAkB,CAAC,OAAS,CAAC,2BAAqB,EAAE,KAAK,UAAU,CAAC,kCAAyB,CAAC;IAC9F,sBAAsB,CAAC,+DAAgD,CAAC;IACxE,UAAU,CAAC,SAAS,CAAC;IACrB,aAAa,CAAC,sBAAgB,CAAC;IAC/B,YAAY,CAAC,6BAAiB,CAAC;AACjC", "sources": ["packages/@react-aria/table/intl/ro-RO.json"], "sourcesContent": ["{\n  \"ascending\": \"crescătoare\",\n  \"ascendingSort\": \"sortate după coloana {columnName} în ordine crescătoare\",\n  \"columnSize\": \"{value} pixeli\",\n  \"descending\": \"descrescătoare\",\n  \"descendingSort\": \"sortate după coloana {columnName} în ordine descrescătoare\",\n  \"resizerDescription\": \"Apăsați pe Enter pentru a începe redimensionarea\",\n  \"select\": \"Selectare\",\n  \"selectAll\": \"Selectare totală\",\n  \"sortable\": \"coloană sortabilă\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.module.js.map"}