/* Login Page - DRDO Official Styling */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 50%, #1A3C5E 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Inter', '<PERSON><PERSON>', sans-serif;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(26, 60, 94, 0.3);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  text-align: center;
}

.login-header {
  margin-bottom: 32px;
}

.drdo-logo {
  font-size: 48px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #FF9933, #FFFFFF, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.login-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.login-header h2 {
  font-size: 18px;
  font-weight: 500;
  color: #4A90E2;
  margin: 0 0 8px 0;
}

.login-header p {
  font-size: 14px;
  color: #64748B;
  margin: 0;
}

.login-form {
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #1A3C5E;
  margin-bottom: 8px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #FFFFFF;
}

.form-group input:focus {
  outline: none;
  border-color: #FF9933;
  box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.1);
}

.error-message {
  background: #FEE2E2;
  color: #DC2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 20px;
  border: 1px solid #FECACA;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #1A3C5E, #2A4C6E);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2A4C6E, #1A3C5E);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(26, 60, 94, 0.3);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.login-help {
  border-top: 1px solid #E2E8F0;
  padding-top: 24px;
  margin-bottom: 24px;
}

.login-help h3 {
  font-size: 16px;
  color: #1A3C5E;
  margin-bottom: 16px;
}

.demo-accounts {
  text-align: left;
  background: #F8FAFC;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #E2E8F0;
}

.demo-account {
  font-size: 13px;
  color: #64748B;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
}

.demo-account:last-child {
  margin-bottom: 0;
}

.demo-account strong {
  color: #1A3C5E;
  font-weight: 600;
}

.login-footer {
  border-top: 1px solid #E2E8F0;
  padding-top: 20px;
  font-size: 12px;
  color: #64748B;
}

.login-footer p {
  margin: 4px 0;
}

/* Development Mode Styles */
.login-dev-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #059669 0%, #10B981 50%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.login-dev-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(5, 150, 105, 0.3);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  text-align: center;
}

.drdo-logo-large {
  font-size: 64px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #FF9933, #FFFFFF, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.login-dev-card h1 {
  font-size: 36px;
  font-weight: 700;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.login-dev-card h2 {
  font-size: 20px;
  font-weight: 600;
  color: #059669;
  margin: 0 0 16px 0;
}

.login-dev-card p {
  font-size: 16px;
  color: #64748B;
  margin-bottom: 32px;
}

.dev-login-options h3 {
  font-size: 18px;
  color: #1A3C5E;
  margin-bottom: 20px;
}

.role-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.role-btn {
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-btn.admin {
  background: #DC2626;
  color: white;
}

.role-btn.admin:hover {
  background: #B91C1C;
  transform: translateY(-2px);
}

.role-btn.scientist {
  background: #059669;
  color: white;
}

.role-btn.scientist:hover {
  background: #047857;
  transform: translateY(-2px);
}

.role-btn.engineer {
  background: #4A90E2;
  color: white;
}

.role-btn.engineer:hover {
  background: #3B82F6;
  transform: translateY(-2px);
}

.role-btn.commander {
  background: #FF9933;
  color: white;
}

.role-btn.commander:hover {
  background: #F59E0B;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container,
  .login-dev-container {
    padding: 16px;
  }
  
  .login-card,
  .login-dev-card {
    padding: 24px;
  }
  
  .drdo-logo {
    font-size: 40px;
  }
  
  .drdo-logo-large {
    font-size: 48px;
  }
  
  .login-header h1,
  .login-dev-card h1 {
    font-size: 24px;
  }
  
  .role-buttons {
    grid-template-columns: 1fr;
  }
}
