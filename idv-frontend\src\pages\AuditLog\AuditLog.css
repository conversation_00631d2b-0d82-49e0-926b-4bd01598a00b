/* Audit Log Page - DRDO v1.7 */
.audit-log-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.audit-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.audit-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.audit-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.audit-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 32px;
}

/* Controls */
.audit-controls {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
}

.search-input:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
  min-width: 140px;
}

.export-section {
  display: flex;
  gap: 12px;
}

.btn-export {
  padding: 12px 20px;
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-export:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

/* Audit Table */
.audit-table-container {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
  margin-bottom: 32px;
}

.audit-table {
  width: 100%;
  border-collapse: collapse;
}

.audit-table th {
  background: #F8FAFC;
  padding: 16px 12px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #1A3C5E;
  text-transform: uppercase;
  border-bottom: 2px solid #E2E8F0;
}

.audit-table td {
  padding: 12px;
  border-bottom: 1px solid #F8FAFC;
  font-size: 13px;
  color: #1A3C5E;
}

.log-row {
  transition: background-color 0.2s ease;
}

.log-row:hover {
  background: #F8FAFC;
}

.log-row.success {
  border-left: 3px solid #059669;
}

.log-row.failure {
  border-left: 3px solid #DC2626;
}

.timestamp-cell {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  color: #64748B;
  min-width: 140px;
}

.action-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.action-icon {
  font-size: 14px;
}

.user-cell {
  font-weight: 500;
  min-width: 100px;
}

.description-cell {
  max-width: 300px;
  word-wrap: break-word;
}

.module-cell {
  font-size: 11px;
  text-transform: uppercase;
  color: #64748B;
  min-width: 80px;
}

.classification-cell {
  min-width: 100px;
}

.classification-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.actions-cell {
  text-align: center;
  min-width: 60px;
}

.btn-view-details {
  background: none;
  border: 1px solid #E2E8F0;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 12px;
  color: #1A3C5E;
  transition: all 0.3s ease;
}

.btn-view-details:hover {
  border-color: #4A90E2;
  background: #F8FAFC;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.log-modal {
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #E2E8F0;
  padding-bottom: 16px;
}

.modal-header h2 {
  font-size: 20px;
  color: #1A3C5E;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #64748B;
  padding: 4px;
}

.log-detail-grid {
  display: grid;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #F8FAFC;
}

.detail-item label {
  font-weight: 500;
  color: #64748B;
  min-width: 120px;
}

.detail-item span {
  color: #1A3C5E;
  font-weight: 500;
  text-align: right;
  word-break: break-word;
}

/* No Logs */
.no-logs {
  text-align: center;
  padding: 80px 32px;
  color: #64748B;
}

.no-logs h3 {
  font-size: 20px;
  margin-bottom: 8px;
}

/* Access Denied */
.access-denied {
  text-align: center;
  padding: 80px 32px;
}

.access-denied h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.access-denied p {
  color: #64748B;
  font-size: 16px;
}

/* Audit Footer */
.audit-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-stats {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .audit-table-container {
    overflow-x: auto;
  }
  
  .audit-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .audit-content {
    padding: 24px 16px;
  }
  
  .audit-controls {
    flex-direction: column;
    align-items: stretch;
    padding: 16px;
  }
  
  .search-section {
    min-width: auto;
  }
  
  .filter-section {
    justify-content: space-between;
  }
  
  .export-section {
    justify-content: center;
  }
  
  .audit-table th,
  .audit-table td {
    padding: 8px 6px;
    font-size: 11px;
  }
  
  .log-modal {
    padding: 24px 16px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
  }
  
  .detail-item span {
    text-align: left;
  }
  
  .footer-stats {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.audit-controls,
.audit-table-container {
  animation: fadeIn 0.5s ease-out;
}
