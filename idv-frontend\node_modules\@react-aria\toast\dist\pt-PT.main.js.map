{"mappings": "AAAA,iBAAiB;IAAG,SAAS,CAAC,MAAM,CAAC;IACnC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAY,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAa,CAAC;QAAA,GAAG,CAAC,CAAC;AACnM", "sources": ["packages/@react-aria/toast/intl/pt-PT.json"], "sourcesContent": ["{\n  \"close\": \"<PERSON><PERSON><PERSON>\",\n  \"notifications\": \"{count, plural, one {# notificação} other {# notificações}}.\"\n}\n"], "names": [], "version": 3, "file": "pt-PT.main.js.map"}