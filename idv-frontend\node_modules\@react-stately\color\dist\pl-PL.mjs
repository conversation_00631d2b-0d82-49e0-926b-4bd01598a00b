var $d9ab5c2611e0bcd3$exports = {};
$d9ab5c2611e0bcd3$exports = {
    "alpha": `Alfa`,
    "black": `czarny`,
    "blue": `<PERSON><PERSON><PERSON><PERSON>`,
    "blue purple": `nieb<PERSON><PERSON>-fioletowy`,
    "brightness": `<PERSON><PERSON><PERSON>\u{15B}\u{107}`,
    "brown": `br\u{105}zowy`,
    "brown yellow": `br\u{105}zowo-\u{17C}\xf3\u{142}ty`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `cyjanowy`,
    "cyan blue": `cyjanowo-niebieski`,
    "dark": `ciemny`,
    "gray": `szary`,
    "grayish": `szarawy`,
    "green": `Zielony`,
    "green cyan": `zielono-cyjanowy`,
    "hue": `O<PERSON>cie\u{144}`,
    "light": `jasny`,
    "lightness": `<PERSON><PERSON><PERSON><PERSON>\u{15B}\u{107}`,
    "magenta": `purpurowy`,
    "magenta pink": `purpurowo-r\xf3\u{17C}owy`,
    "orange": `pomara\u{144}czowy`,
    "orange yellow": `pomara\u{144}czowo-\u{17C}\xf3\u{142}ty`,
    "pale": `blady`,
    "pink": `r\xf3\u{17C}owy`,
    "pink red": `r\xf3\u{17C}owo-czerwony`,
    "purple": `fioletowy`,
    "purple magenta": `fioletowo-purpurowy`,
    "red": `Czerwony`,
    "red orange": `czerwono-pomara\u{144}czowy`,
    "saturation": `Nasycenie`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} przezroczysto\u{15B}ci`,
    "very dark": `bardzo ciemny`,
    "very light": `bardzo jasny`,
    "vibrant": `intensywny`,
    "white": `bia\u{142}y`,
    "yellow": `\u{17C}\xf3\u{142}ty`,
    "yellow green": `\u{17C}\xf3\u{142}to-zielony`
};


export {$d9ab5c2611e0bcd3$exports as default};
//# sourceMappingURL=pl-PL.module.js.map
