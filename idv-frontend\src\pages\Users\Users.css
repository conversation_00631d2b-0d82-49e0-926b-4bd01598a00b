/* Users Page - DRDO v1.7 */
.users-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.users-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.users-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.users-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.users-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 32px;
}

.users-controls {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
}

.search-input:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
  min-width: 120px;
}

.btn-add-user {
  padding: 12px 24px;
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-add-user:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.user-card {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
  transition: all 0.3s ease;
}

.user-card:hover {
  box-shadow: 0 8px 24px rgba(26, 60, 94, 0.15);
  transform: translateY(-2px);
}

.user-card.inactive {
  opacity: 0.7;
  border-color: #F1F5F9;
}

.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.user-basic-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 4px 0;
}

.user-email {
  font-size: 12px;
  color: #64748B;
  margin: 0;
}

.status-indicator {
  font-size: 16px;
}

.user-details {
  margin-bottom: 20px;
}

.user-badges {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.role-badge,
.clearance-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.info-label {
  color: #64748B;
  font-weight: 500;
}

.info-value {
  color: #1A3C5E;
  font-weight: 500;
}

.user-actions {
  display: flex;
  gap: 8px;
  border-top: 1px solid #F1F5F9;
  padding-top: 16px;
}

.btn-view,
.btn-decrypt {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #E2E8F0;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  color: #1A3C5E;
}

.btn-view:hover {
  border-color: #4A90E2;
  background: #F8FAFC;
}

.btn-decrypt:hover {
  border-color: #F59E0B;
  background: #FFFBEB;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.user-modal {
  background: white;
  border-radius: 12px;
  padding: 32px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #E2E8F0;
  padding-bottom: 16px;
}

.modal-header h2 {
  font-size: 20px;
  color: #1A3C5E;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #64748B;
}

.user-detail-grid {
  display: grid;
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #F8FAFC;
}

.detail-item label {
  font-weight: 500;
  color: #64748B;
}

.detail-item span {
  color: #1A3C5E;
  font-weight: 500;
}

.no-users {
  text-align: center;
  padding: 80px 32px;
  color: #64748B;
}

.no-users h3 {
  font-size: 20px;
  margin-bottom: 8px;
}

.access-denied {
  text-align: center;
  padding: 80px 32px;
}

.access-denied h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.access-denied p {
  color: #64748B;
  font-size: 16px;
}

.users-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-stats {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 768px) {
  .users-content {
    padding: 24px 16px;
  }
  
  .users-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-section {
    min-width: auto;
  }
  
  .filter-section {
    justify-content: space-between;
  }
  
  .users-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-stats {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .user-modal {
    padding: 24px 16px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-card {
  animation: fadeIn 0.5s ease-out;
}
