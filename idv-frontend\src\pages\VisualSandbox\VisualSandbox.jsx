import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import './VisualSandbox.css'

const VisualSandbox = () => {
  const { user, hasPermission } = useAuth()
  const [selectedChart, setSelectedChart] = useState('bar')
  const [chartData, setChartData] = useState([])
  const [chartConfig, setChartConfig] = useState({
    title: 'Sample Chart',
    xAxis: 'Category',
    yAxis: 'Value',
    color: '#4A90E2'
  })
  const [livePreview, setLivePreview] = useState(true)

  const chartTypes = [
    { id: 'bar', name: 'Bar Chart', icon: '📊', description: 'Compare values across categories' },
    { id: 'line', name: 'Line Chart', icon: '📈', description: 'Show trends over time' },
    { id: 'pie', name: 'Pie Chart', icon: '🥧', description: 'Show proportions of a whole' },
    { id: 'scatter', name: 'Scatter Plot', icon: '⚪', description: 'Show correlation between variables' },
    { id: 'area', name: 'Area Chart', icon: '🏔️', description: 'Show cumulative values over time' },
    { id: 'radar', name: 'Radar Chart', icon: '🎯', description: 'Compare multiple variables' },
    { id: 'heatmap', name: 'Heatmap', icon: '🔥', description: 'Show data density and patterns' },
    { id: 'sankey', name: 'Sankey Diagram', icon: '🌊', description: 'Show flow between nodes' },
    { id: 'treemap', name: 'Treemap', icon: '🗂️', description: 'Show hierarchical data' },
    { id: '3d', name: '3D Surface', icon: '🏔️', description: 'Three-dimensional data visualization' }
  ]

  const sampleDatasets = [
    {
      name: 'Equipment Status',
      data: [
        { category: 'BrahMos', value: 95, status: 'Operational' },
        { category: 'Agni-V', value: 88, status: 'Operational' },
        { category: 'Tejas', value: 76, status: 'Maintenance' },
        { category: 'Radar-1', value: 92, status: 'Operational' },
        { category: 'Comm-Array', value: 84, status: 'Warning' }
      ]
    },
    {
      name: 'Mission Progress',
      data: [
        { category: 'Operation Thunder', value: 85, status: 'Active' },
        { category: 'Defense Grid Alpha', value: 100, status: 'Complete' },
        { category: 'Surveillance Beta', value: 45, status: 'Planning' },
        { category: 'Training Delta', value: 67, status: 'Active' }
      ]
    },
    {
      name: 'Lab Performance',
      data: [
        { category: 'Delhi Labs', value: 94, status: 'Excellent' },
        { category: 'Bangalore Labs', value: 89, status: 'Good' },
        { category: 'Hyderabad Labs', value: 91, status: 'Excellent' },
        { category: 'Pune Labs', value: 87, status: 'Good' },
        { category: 'Chennai Labs', value: 83, status: 'Average' }
      ]
    }
  ]

  useEffect(() => {
    // Set default data
    setChartData(sampleDatasets[0].data)
    logAuditEvent('VISUAL_SANDBOX_VIEW', 'Visual sandbox accessed', 'VISUAL_SANDBOX', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  const handleDatasetChange = (dataset) => {
    setChartData(dataset.data)
    setChartConfig(prev => ({
      ...prev,
      title: `${dataset.name} Analysis`
    }))
  }

  const generateChart = () => {
    // This would integrate with actual charting library
    logAuditEvent('CHART_GENERATE', `Generated ${selectedChart} chart: ${chartConfig.title}`, 'VISUAL_SANDBOX', 'RESTRICTED', user?.id, user?.name)
    alert(`Chart generated: ${chartConfig.title} (${selectedChart})`)
  }

  const exportChart = (format) => {
    logAuditEvent('CHART_EXPORT', `Exported chart as ${format}: ${chartConfig.title}`, 'VISUAL_SANDBOX', 'RESTRICTED', user?.id, user?.name)
    alert(`Chart exported as ${format}`)
  }

  const renderPreview = () => {
    if (!livePreview || chartData.length === 0) {
      return (
        <div className="preview-placeholder">
          <div className="placeholder-content">
            <span className="placeholder-icon">📊</span>
            <h3>Chart Preview</h3>
            <p>Select data and chart type to see live preview</p>
          </div>
        </div>
      )
    }

    // Simple bar chart preview
    const maxValue = Math.max(...chartData.map(d => d.value))
    
    return (
      <div className="chart-preview">
        <h3 className="chart-title">{chartConfig.title}</h3>
        <div className="simple-bar-chart">
          <div className="chart-area">
            {chartData.map((item, index) => (
              <div key={index} className="bar-container">
                <div 
                  className="bar" 
                  style={{ 
                    height: `${(item.value / maxValue) * 200}px`,
                    backgroundColor: chartConfig.color 
                  }}
                >
                  <span className="bar-value">{item.value}</span>
                </div>
                <span className="bar-label">{item.category}</span>
              </div>
            ))}
          </div>
          <div className="chart-axes">
            <span className="y-axis-label">{chartConfig.yAxis}</span>
            <span className="x-axis-label">{chartConfig.xAxis}</span>
          </div>
        </div>
      </div>
    )
  }

  if (!hasPermission('scientist')) {
    return (
      <div className="visual-sandbox-page">
        <div className="access-denied">
          <h2>🔒 Access Denied</h2>
          <p>You need scientist privileges or higher to access the visual sandbox.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="visual-sandbox-page">
      <div className="sandbox-header">
        <h1 className="sandbox-title">📈 Visual Sandbox</h1>
        <p className="sandbox-subtitle">Interactive chart builder with live preview capabilities</p>
      </div>

      <div className="sandbox-content">
        {/* Chart Type Selection */}
        <div className="chart-types-section">
          <h2 className="section-title">📊 Chart Types</h2>
          <div className="chart-types-grid">
            {chartTypes.map(chart => (
              <div 
                key={chart.id}
                className={`chart-type-card ${selectedChart === chart.id ? 'selected' : ''}`}
                onClick={() => setSelectedChart(chart.id)}
              >
                <div className="chart-icon">{chart.icon}</div>
                <h3 className="chart-name">{chart.name}</h3>
                <p className="chart-description">{chart.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Data Selection */}
        <div className="data-section">
          <h2 className="section-title">📋 Sample Datasets</h2>
          <div className="datasets-grid">
            {sampleDatasets.map((dataset, index) => (
              <div 
                key={index}
                className="dataset-card"
                onClick={() => handleDatasetChange(dataset)}
              >
                <h3 className="dataset-name">{dataset.name}</h3>
                <p className="dataset-info">{dataset.data.length} data points</p>
                <div className="dataset-preview">
                  {dataset.data.slice(0, 3).map((item, idx) => (
                    <span key={idx} className="data-point">
                      {item.category}: {item.value}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Configuration Panel */}
        <div className="config-section">
          <h2 className="section-title">⚙️ Chart Configuration</h2>
          <div className="config-panel">
            <div className="config-group">
              <label>Chart Title</label>
              <input 
                type="text" 
                value={chartConfig.title}
                onChange={(e) => setChartConfig(prev => ({...prev, title: e.target.value}))}
                className="config-input"
              />
            </div>
            <div className="config-group">
              <label>X-Axis Label</label>
              <input 
                type="text" 
                value={chartConfig.xAxis}
                onChange={(e) => setChartConfig(prev => ({...prev, xAxis: e.target.value}))}
                className="config-input"
              />
            </div>
            <div className="config-group">
              <label>Y-Axis Label</label>
              <input 
                type="text" 
                value={chartConfig.yAxis}
                onChange={(e) => setChartConfig(prev => ({...prev, yAxis: e.target.value}))}
                className="config-input"
              />
            </div>
            <div className="config-group">
              <label>Color</label>
              <input 
                type="color" 
                value={chartConfig.color}
                onChange={(e) => setChartConfig(prev => ({...prev, color: e.target.value}))}
                className="config-color"
              />
            </div>
            <div className="config-group">
              <label className="checkbox-label">
                <input 
                  type="checkbox" 
                  checked={livePreview}
                  onChange={(e) => setLivePreview(e.target.checked)}
                />
                Live Preview
              </label>
            </div>
          </div>
        </div>

        {/* Live Preview */}
        <div className="preview-section">
          <h2 className="section-title">👁️ Live Preview</h2>
          <div className="preview-container">
            {renderPreview()}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="actions-section">
          <div className="action-buttons">
            <button onClick={generateChart} className="btn-generate">
              🎨 Generate Chart
            </button>
            <button onClick={() => exportChart('PNG')} className="btn-export">
              📥 Export PNG
            </button>
            <button onClick={() => exportChart('SVG')} className="btn-export">
              📥 Export SVG
            </button>
            <button onClick={() => exportChart('PDF')} className="btn-export">
              📥 Export PDF
            </button>
          </div>
        </div>
      </div>

      {/* Sandbox Footer */}
      <div className="sandbox-footer">
        <div className="footer-info">
          <span>Chart Type: {chartTypes.find(c => c.id === selectedChart)?.name}</span>
          <span>Data Points: {chartData.length}</span>
          <span>User: {user?.name}</span>
        </div>
      </div>
    </div>
  )
}

export default VisualSandbox
