import React, { useEffect, useRef, useState } from 'react'
import Plotly from 'plotly.js/dist/plotly'
import './TrajectoryPlot3D.css'

const TrajectoryPlot3D = ({ data = [], width = 800, height = 500 }) => {
  const plotRef = useRef()
  const [selectedMissile, setSelectedMissile] = useState('all')
  const [viewMode, setViewMode] = useState('3d')

  // Sample trajectory data if none provided
  const sampleData = data.length > 0 ? data : [
    {
      missile: 'BrahMos',
      trajectory: [
        { x: 0, y: 0, z: 0, time: 0 },
        { x: 10, y: 5, z: 2, time: 1 },
        { x: 25, y: 12, z: 8, time: 2 },
        { x: 45, y: 20, z: 15, time: 3 },
        { x: 70, y: 28, z: 20, time: 4 },
        { x: 100, y: 35, z: 18, time: 5 },
        { x: 135, y: 40, z: 12, time: 6 },
        { x: 175, y: 42, z: 5, time: 7 },
        { x: 220, y: 40, z: 0, time: 8 }
      ],
      color: '#ff6b6b',
      status: 'Active'
    },
    {
      missile: 'Agni-V',
      trajectory: [
        { x: 0, y: 0, z: 0, time: 0 },
        { x: 8, y: 8, z: 15, time: 2 },
        { x: 20, y: 18, z: 35, time: 4 },
        { x: 35, y: 30, z: 60, time: 6 },
        { x: 55, y: 45, z: 85, time: 8 },
        { x: 80, y: 62, z: 100, time: 10 },
        { x: 110, y: 80, z: 95, time: 12 },
        { x: 145, y: 95, z: 75, time: 14 },
        { x: 185, y: 105, z: 45, time: 16 },
        { x: 230, y: 110, z: 15, time: 18 },
        { x: 280, y: 108, z: 0, time: 20 }
      ],
      color: '#4ecdc4',
      status: 'Simulation'
    }
  ]

  const filteredData = selectedMissile === 'all' 
    ? sampleData 
    : sampleData.filter(d => d.missile === selectedMissile)

  useEffect(() => {
    if (!plotRef.current || !filteredData.length) return

    const traces = filteredData.map(missile => ({
      x: missile.trajectory.map(point => point.x),
      y: missile.trajectory.map(point => point.y),
      z: missile.trajectory.map(point => point.z),
      mode: 'lines+markers',
      type: 'scatter3d',
      name: missile.missile,
      line: {
        color: missile.color,
        width: 6
      },
      marker: {
        size: 4,
        color: missile.trajectory.map(point => point.time),
        colorscale: 'Viridis',
        showscale: true,
        colorbar: {
          title: 'Time (s)',
          titleside: 'right'
        }
      },
      text: missile.trajectory.map(point => 
        `Time: ${point.time}s<br>Position: (${point.x}, ${point.y}, ${point.z})`
      ),
      hovertemplate: '<b>%{fullData.name}</b><br>' +
                     '%{text}<br>' +
                     '<extra></extra>'
    }))

    // Add launch and target markers
    const launchMarkers = {
      x: filteredData.map(d => d.trajectory[0].x),
      y: filteredData.map(d => d.trajectory[0].y),
      z: filteredData.map(d => d.trajectory[0].z),
      mode: 'markers',
      type: 'scatter3d',
      name: 'Launch Points',
      marker: {
        size: 10,
        color: 'green',
        symbol: 'diamond'
      },
      showlegend: true
    }

    const targetMarkers = {
      x: filteredData.map(d => d.trajectory[d.trajectory.length - 1].x),
      y: filteredData.map(d => d.trajectory[d.trajectory.length - 1].y),
      z: filteredData.map(d => d.trajectory[d.trajectory.length - 1].z),
      mode: 'markers',
      type: 'scatter3d',
      name: 'Target Points',
      marker: {
        size: 10,
        color: 'red',
        symbol: 'x'
      },
      showlegend: true
    }

    const layout = {
      title: {
        text: 'Missile Trajectory Analysis - 3D Visualization',
        font: { size: 16 }
      },
      scene: {
        xaxis: { title: 'Range (km)' },
        yaxis: { title: 'Cross Range (km)' },
        zaxis: { title: 'Altitude (km)' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1.5 }
        }
      },
      margin: { l: 0, r: 0, b: 0, t: 40 },
      legend: {
        x: 0,
        y: 1,
        bgcolor: 'rgba(255,255,255,0.8)'
      }
    }

    const config = {
      displayModeBar: true,
      modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
      displaylogo: false,
      responsive: true
    }

    Plotly.newPlot(plotRef.current, [...traces, launchMarkers, targetMarkers], layout, config)

    return () => {
      if (plotRef.current) {
        Plotly.purge(plotRef.current)
      }
    }
  }, [filteredData, width, height])

  const missiles = Array.from(new Set(sampleData.map(d => d.missile)))

  return (
    <div className="trajectory-plot-3d">
      <div className="plot-header">
        <h3>3D Trajectory Analysis</h3>
        <div className="plot-controls">
          <select 
            value={selectedMissile}
            onChange={(e) => setSelectedMissile(e.target.value)}
            className="missile-selector"
          >
            <option value="all">All Missiles</option>
            {missiles.map(missile => (
              <option key={missile} value={missile}>{missile}</option>
            ))}
          </select>
          
          <div className="view-controls">
            <button 
              className={`view-btn ${viewMode === '3d' ? 'active' : ''}`}
              onClick={() => setViewMode('3d')}
            >
              3D View
            </button>
            <button 
              className={`view-btn ${viewMode === 'top' ? 'active' : ''}`}
              onClick={() => {
                setViewMode('top')
                if (plotRef.current) {
                  Plotly.relayout(plotRef.current, {
                    'scene.camera.eye': { x: 0, y: 0, z: 2.5 }
                  })
                }
              }}
            >
              Top View
            </button>
            <button 
              className={`view-btn ${viewMode === 'side' ? 'active' : ''}`}
              onClick={() => {
                setViewMode('side')
                if (plotRef.current) {
                  Plotly.relayout(plotRef.current, {
                    'scene.camera.eye': { x: 2.5, y: 0, z: 0 }
                  })
                }
              }}
            >
              Side View
            </button>
          </div>
        </div>
      </div>

      <div className="plot-container">
        <div ref={plotRef} style={{ width: '100%', height: height }}></div>
      </div>

      <div className="plot-info">
        <div className="info-grid">
          <div className="info-item">
            <span className="info-label">Active Trajectories:</span>
            <span className="info-value">{filteredData.length}</span>
          </div>
          <div className="info-item">
            <span className="info-label">Max Altitude:</span>
            <span className="info-value">
              {Math.max(...filteredData.flatMap(d => d.trajectory.map(p => p.z))).toFixed(1)} km
            </span>
          </div>
          <div className="info-item">
            <span className="info-label">Max Range:</span>
            <span className="info-value">
              {Math.max(...filteredData.flatMap(d => d.trajectory.map(p => p.x))).toFixed(1)} km
            </span>
          </div>
          <div className="info-item">
            <span className="info-label">Simulation Time:</span>
            <span className="info-value">
              {Math.max(...filteredData.flatMap(d => d.trajectory.map(p => p.time)))} seconds
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TrajectoryPlot3D
