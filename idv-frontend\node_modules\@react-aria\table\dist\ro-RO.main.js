module.exports = {
    "ascending": `cresc\u{103}toare`,
    "ascendingSort": (args)=>`sortate dup\u{103} coloana ${args.columnName} \xeen ordine cresc\u{103}toare`,
    "columnSize": (args)=>`${args.value} pixeli`,
    "descending": `descresc\u{103}toare`,
    "descendingSort": (args)=>`sortate dup\u{103} coloana ${args.columnName} \xeen ordine descresc\u{103}toare`,
    "resizerDescription": `Ap\u{103}sa\u{21B}i pe Enter pentru a \xeencepe redimensionarea`,
    "select": `Selectare`,
    "selectAll": `Selectare total\u{103}`,
    "sortable": `coloan\u{103} sortabil\u{103}`
};


//# sourceMappingURL=ro-RO.main.js.map
