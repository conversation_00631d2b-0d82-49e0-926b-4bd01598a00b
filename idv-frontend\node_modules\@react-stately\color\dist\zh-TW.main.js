module.exports = {
    "alpha": `Alpha`,
    "black": `\u{9ED1}`,
    "blue": `\u{85CD}\u{8272}`,
    "blue purple": `\u{85CD}\u{7D2B}`,
    "brightness": `\u{4EAE}\u{5EA6}`,
    "brown": `\u{68D5}`,
    "brown yellow": `\u{68D5}\u{9EC3}`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `\u{9752}`,
    "cyan blue": `\u{9752}\u{85CD}`,
    "dark": `\u{6697}`,
    "gray": `\u{7070}`,
    "grayish": `\u{504F}\u{7070}`,
    "green": `\u{7DA0}\u{8272}`,
    "green cyan": `\u{9752}\u{7DA0}`,
    "hue": `\u{8272}\u{76F8}`,
    "light": `\u{6DFA}`,
    "lightness": `\u{660E}\u{4EAE}`,
    "magenta": `\u{6D0B}\u{7D05}`,
    "magenta pink": `\u{6DFA}\u{6D0B}\u{7D05}`,
    "orange": `\u{6A59}`,
    "orange yellow": `\u{6A59}\u{9EC3}`,
    "pale": `\u{6DE1}`,
    "pink": `\u{7C89}\u{7D05}`,
    "pink red": `\u{7C89}\u{7D05}`,
    "purple": `\u{7D2B}`,
    "purple magenta": `\u{7D2B}\u{6D0B}\u{7D05}`,
    "red": `\u{7D05}\u{8272}`,
    "red orange": `\u{6A59}\u{7D05}`,
    "saturation": `\u{98FD}\u{548C}\u{5EA6}`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} \u{900F}\u{660E}`,
    "very dark": `\u{5F88}\u{6697}`,
    "very light": `\u{5F88}\u{6DFA}`,
    "vibrant": `\u{9BAE}\u{8C54}`,
    "white": `\u{767D}`,
    "yellow": `\u{9EC3}`,
    "yellow green": `\u{9EC3}\u{7DA0}`
};


//# sourceMappingURL=zh-TW.main.js.map
