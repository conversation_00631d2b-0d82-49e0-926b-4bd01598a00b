/* Layout v1.7 - DRDO Official Styling */
.layout-v17 {
  min-height: 100vh;
  background: #FFFFFF;
  font-family: 'Inter', 'Roboto', sans-serif;
  color: #1A3C5E;
  display: flex;
  flex-direction: column;
}

.layout-minimal {
  min-height: 100vh;
  background: #FFFFFF;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.layout-body {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 80px);
}

.layout-main {
  flex: 1;
  background: #F8FAFC;
  overflow-y: auto;
  position: relative;
  padding: 24px;
  min-height: calc(100vh - 140px);
}

/* Development Mode Indicator */
.dev-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(26, 60, 94, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 500;
  z-index: 1000;
  opacity: 0.7;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Removed pulse animation for professional appearance */

/* Theme Variables */
.theme-formal-white {
  --primary: #1A3C5E;
  --secondary: #4A90E2;
  --accent: #FF9933;
  --background: #FFFFFF;
  --surface: #F8FAFC;
  --text: #1A3C5E;
  --border: #E2E8F0;
  --success: #059669;
  --warning: #F59E0B;
  --error: #DC2626;
}

.theme-minimal {
  --primary: #1F2937;
  --secondary: #6B7280;
  --accent: #3B82F6;
  --background: #F9FAFB;
  --surface: #FFFFFF;
  --text: #1F2937;
  --border: #E5E7EB;
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
}

.theme-dark {
  --primary: #F8FAFC;
  --secondary: #94A3B8;
  --accent: #60A5FA;
  --background: #0F172A;
  --surface: #1E293B;
  --text: #F8FAFC;
  --border: #334155;
  --success: #22C55E;
  --warning: #F59E0B;
  --error: #EF4444;
}

.theme-dracula {
  --primary: #F8F8F2;
  --secondary: #BD93F9;
  --accent: #FF79C6;
  --background: #282A36;
  --surface: #44475A;
  --text: #F8F8F2;
  --border: #6272A4;
  --success: #50FA7B;
  --warning: #F1FA8C;
  --error: #FF5555;
}

/* Legacy styles for backward compatibility */
.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #ffffff;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .layout-body {
    flex-direction: column;
  }

  .layout-main {
    min-height: calc(100vh - 160px);
  }
}

@media (max-width: 768px) {
  .dev-indicator {
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    font-size: 11px;
  }
}
