{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AAcD,MAAM,uCAAiB;IACrB,QAAQ;QACN,KAAK;QACL,KAAK;IACP;IACA,YAAY;QACV,KAAK;QACL,KAAK;IACP;AACF;AAOO,SAAS,0CAAe,KAAsB,EAAE,KAAuC,EAAE,GAAuC;IACrI,IAAI,QAAC,IAAI,iBAAE,aAAa,EAAC,GAAG;IAC5B,IAAI,YAAC,QAAQ,EAAE,GAAG,QAAO,GAAG,CAAA,GAAA,iBAAS,EAAwC,OAAO,OAAO;IAC3F,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAE1B,IAAI,iBAAiB,CAAE,CAAA,CAAA,GAAA,sBAAc,OAAO,kBAAkB,KAAI,GAChE,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,IAAI,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,2BAA2B;SAE5G,OAAO,QAAQ,CAAC,gBAAgB;IAGlC,IAAI,mBAAgD,CAAC;IACrD,IAAI,CAAA,GAAA,sBAAc,OAAO,kBAAkB,OAAO;QAChD,IAAI,WAAW,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG;QACxC,IAAI,YAAY,MAAM;gBACD,iBAAuC,0BAAA,kBAepD,cAAY,mBACZ;YAhBN,IAAI,eAAe,EAAA,kBAAA,SAAS,KAAK,cAAd,sCAAA,gBAAgB,mBAAmB,KAAI,EAAA,mBAAA,SAAS,KAAK,cAAd,wCAAA,2BAAA,iBAAgB,QAAQ,cAAxB,+CAAA,yBAA0B,MAAM,IAAG,MAAM,eAAe;gBAa9F,uBAEF,8BAAb,0BACA;YAfL,mBAAmB;gBACjB,WAAW,CAAC;oBACV,IAAI,AAAC,EAAE,GAAG,KAAK,oCAAc,CAAC,SAAS,CAAC,UAAU,IAAK,MAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,gBAAgB,MAAM,YAAY,KAAK,SAAS,CAAC,MAAM,YAAY,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG;wBAClM,MAAM,SAAS,CAAC,SAAS,GAAG;wBAC5B,EAAE,eAAe;oBACnB,OAAO,IAAI,AAAC,EAAE,GAAG,KAAK,oCAAc,CAAC,WAAW,CAAC,UAAU,IAAK,MAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,gBAAiB,CAAA,MAAM,YAAY,KAAK,SAAS,MAAM,YAAY,CAAC,GAAG,CAAC,SAAS,GAAG,CAAA,GAAI;wBAC5M,MAAM,SAAS,CAAC,SAAS,GAAG;wBAC5B,EAAE,eAAe;oBACnB;gBACF;gBACA,iBAAiB,eAAe,MAAM,YAAY,KAAK,SAAS,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI;gBACnG,cAAc,SAAS,KAAK;gBAC5B,iBAAiB,AAAC,CAAA,CAAA,wBAAA,SAAS,WAAW,cAApB,mCAAA,wBAAwB,CAAA,IAAK;gBAC/C,gBAAgB,SAAS,KAAK,GAAG,IAC/B,AAAC,CAAA,CAAA,4BAAC,eAAA,CAAA,GAAA,kBAAU,EAAE,CAAA,gCAAA,oBAAA,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,SAAS,eAAnC,wCAAA,kBAAuC,UAAU,cAAjD,0CAAA,+BAAqD,EAAE,eAAnE,mCAAD,AAAC,aAAsF,WAAW,cAAlG,sCAAA,2BAAsG,CAAA,IAAK,IAC5G,AAAC,CAAA,CAAA,6BAAC,gBAAA,CAAA,GAAA,kBAAU,EAAE,MAAM,UAAU,CAAC,IAAI,CAAC,UAAU,eAA5C,oCAAD,AAAC,cAA+D,WAAW,cAA3E,uCAAA,4BAA+E,CAAA,IAAK;YACzF;QACF;IACF;IAEA,IAAI,qBAAqB,CAAA,GAAA,4BAAoB,EAAE,KAAK,KAAK;IACzD,IAAI,YAAY,OAAO,SAAS,GAAG,qBAAqB,CAAC;IACzD,OAAO;QACL,UAAU;YACR,GAAG,CAAA,GAAA,iBAAS,EAAE,UAAU,kBAAkB,UAAU;YACpD,mBAAmB,CAAA,GAAA,wCAAe,EAAE,OAAO,KAAK,GAAG;QACrD;QACA,GAAG,MAAM;IACX;AACF", "sources": ["packages/@react-aria/table/src/useTableRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {getLastItem} from '@react-stately/collections';\nimport {getRowLabelledBy} from './utils';\nimport type {GridNode} from '@react-types/grid';\nimport {GridRowAria, GridRowProps, useGridRow} from '@react-aria/grid';\nimport {HTMLAttributes} from 'react';\nimport {mergeProps, useSyntheticLinkProps} from '@react-aria/utils';\nimport {TableCollection} from '@react-types/table';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState, TreeGridState} from '@react-stately/table';\nimport {useLocale} from '@react-aria/i18n';\n\nconst EXPANSION_KEYS = {\n  expand: {\n    ltr: 'ArrowRight',\n    rtl: 'ArrowLeft'\n  },\n  'collapse': {\n    ltr: 'ArrowLeft',\n    rtl: 'ArrowRight'\n  }\n};\n\n/**\n * Provides the behavior and accessibility implementation for a row in a table.\n * @param props - Props for the row.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableRow<T>(props: GridRowProps<T>, state: TableState<T> | TreeGridState<T>, ref: RefObject<FocusableElement | null>): GridRowAria {\n  let {node, isVirtualized} = props;\n  let {rowProps, ...states} = useGridRow<T, TableCollection<T>, TableState<T>>(props, state, ref);\n  let {direction} = useLocale();\n\n  if (isVirtualized && !(tableNestedRows() && 'expandedKeys' in state)) {\n    rowProps['aria-rowindex'] = node.index + 1 + state.collection.headerRows.length; // aria-rowindex is 1 based\n  } else {\n    delete rowProps['aria-rowindex'];\n  }\n\n  let treeGridRowProps: HTMLAttributes<HTMLElement> = {};\n  if (tableNestedRows() && 'expandedKeys' in state) {\n    let treeNode = state.keyMap.get(node.key);\n    if (treeNode != null) {\n      let hasChildRows = treeNode.props?.UNSTABLE_childItems || treeNode.props?.children?.length > state.userColumnCount;\n      treeGridRowProps = {\n        onKeyDown: (e) => {\n          if ((e.key === EXPANSION_KEYS['expand'][direction]) && state.selectionManager.focusedKey === treeNode.key && hasChildRows && state.expandedKeys !== 'all' && !state.expandedKeys.has(treeNode.key)) {\n            state.toggleKey(treeNode.key);\n            e.stopPropagation();\n          } else if ((e.key === EXPANSION_KEYS['collapse'][direction]) && state.selectionManager.focusedKey === treeNode.key && hasChildRows && (state.expandedKeys === 'all' || state.expandedKeys.has(treeNode.key))) {\n            state.toggleKey(treeNode.key);\n            e.stopPropagation();\n          }\n        },\n        'aria-expanded': hasChildRows ? state.expandedKeys === 'all' || state.expandedKeys.has(node.key) : undefined,\n        'aria-level': treeNode.level,\n        'aria-posinset': (treeNode.indexOfType ?? 0) + 1,\n        'aria-setsize': treeNode.level > 1 ?\n          ((getLastItem(state.keyMap.get(treeNode.parentKey!)?.childNodes ?? []) as GridNode<T>)?.indexOfType ?? 0) + 1 :\n          ((getLastItem(state.collection.body.childNodes) as GridNode<T>)?.indexOfType ?? 0) + 1\n      };\n    }\n  }\n\n  let syntheticLinkProps = useSyntheticLinkProps(node.props);\n  let linkProps = states.hasAction ? syntheticLinkProps : {};\n  return {\n    rowProps: {\n      ...mergeProps(rowProps, treeGridRowProps, linkProps),\n      'aria-labelledby': getRowLabelledBy(state, node.key)\n    },\n    ...states\n  };\n}\n"], "names": [], "version": 3, "file": "useTableRow.module.js.map"}