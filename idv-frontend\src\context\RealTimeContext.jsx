import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import websocketService from '../services/websocket'
import { logAuditEvent } from '../utils/audit'

const RealTimeContext = createContext()

export const useRealTime = () => {
  const context = useContext(RealTimeContext)
  if (!context) {
    throw new Error('useRealTime must be used within a RealTimeProvider')
  }
  return context
}

export const RealTimeProvider = ({ children }) => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [equipmentData, setEquipmentData] = useState(new Map())
  const [missionData, setMissionData] = useState(new Map())
  const [systemAlerts, setSystemAlerts] = useState([])
  const [userActivity, setUserActivity] = useState([])
  const [latency, setLatency] = useState(0)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(5000) // 5 seconds default

  // Initialize WebSocket connection
  useEffect(() => {
    const unsubscribeConnection = websocketService.subscribe('connection', (data) => {
      setConnectionStatus(data.status)
      logAuditEvent('REALTIME_CONNECTION', `Connection status: ${data.status}`, 'REALTIME', 'PUBLIC')
    })

    const unsubscribeEquipment = websocketService.subscribe('equipment', (data) => {
      setEquipmentData(prev => {
        const newMap = new Map(prev)
        newMap.set(data.id, {
          ...data,
          lastUpdate: new Date().toISOString()
        })
        return newMap
      })
    })

    const unsubscribeMission = websocketService.subscribe('mission', (data) => {
      setMissionData(prev => {
        const newMap = new Map(prev)
        newMap.set(data.id, {
          ...data,
          lastUpdate: new Date().toISOString()
        })
        return newMap
      })
    })

    const unsubscribeAlert = websocketService.subscribe('alert', (data) => {
      setSystemAlerts(prev => {
        const newAlert = {
          id: `alert_${Date.now()}`,
          ...data,
          timestamp: new Date().toISOString()
        }
        // Keep only last 50 alerts
        return [newAlert, ...prev.slice(0, 49)]
      })
    })

    const unsubscribeActivity = websocketService.subscribe('activity', (data) => {
      setUserActivity(prev => {
        const newActivity = {
          id: `activity_${Date.now()}`,
          ...data,
          timestamp: new Date().toISOString()
        }
        // Keep only last 100 activities
        return [newActivity, ...prev.slice(0, 99)]
      })
    })

    const unsubscribeHeartbeat = websocketService.subscribe('heartbeat', (data) => {
      setLatency(data.latency)
    })

    // Start simulation in development mode
    if (process.env.NODE_ENV === 'development') {
      setConnectionStatus('connected')
    }

    return () => {
      unsubscribeConnection()
      unsubscribeEquipment()
      unsubscribeMission()
      unsubscribeAlert()
      unsubscribeActivity()
      unsubscribeHeartbeat()
    }
  }, [])

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      // Trigger data refresh
      refreshData()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval])

  // Refresh all data
  const refreshData = useCallback(() => {
    if (websocketService.isConnected) {
      websocketService.send('request_update', {
        types: ['equipment', 'mission', 'alerts']
      })
    }
    logAuditEvent('REALTIME_REFRESH', 'Data refresh requested', 'REALTIME', 'PUBLIC')
  }, [])

  // Send real-time message
  const sendMessage = useCallback((type, payload) => {
    websocketService.send(type, payload)
  }, [])

  // Add system alert
  const addAlert = useCallback((level, message, source = 'System') => {
    const alert = {
      id: `alert_${Date.now()}`,
      level,
      message,
      source,
      timestamp: new Date().toISOString()
    }
    
    setSystemAlerts(prev => [alert, ...prev.slice(0, 49)])
    
    // Send to WebSocket if connected
    if (websocketService.isConnected) {
      websocketService.send('alert', alert)
    }
    
    logAuditEvent('REALTIME_ALERT', `${level.toUpperCase()}: ${message}`, 'REALTIME', 'RESTRICTED')
  }, [])

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setSystemAlerts([])
    logAuditEvent('REALTIME_CLEAR_ALERTS', 'System alerts cleared', 'REALTIME', 'PUBLIC')
  }, [])

  // Update equipment data
  const updateEquipment = useCallback((id, data) => {
    setEquipmentData(prev => {
      const newMap = new Map(prev)
      newMap.set(id, {
        ...newMap.get(id),
        ...data,
        lastUpdate: new Date().toISOString()
      })
      return newMap
    })
  }, [])

  // Update mission data
  const updateMission = useCallback((id, data) => {
    setMissionData(prev => {
      const newMap = new Map(prev)
      newMap.set(id, {
        ...newMap.get(id),
        ...data,
        lastUpdate: new Date().toISOString()
      })
      return newMap
    })
  }, [])

  // Get connection info
  const getConnectionInfo = useCallback(() => {
    return {
      status: connectionStatus,
      latency,
      autoRefresh,
      refreshInterval,
      ...websocketService.getStatus()
    }
  }, [connectionStatus, latency, autoRefresh, refreshInterval])

  // Toggle auto-refresh
  const toggleAutoRefresh = useCallback(() => {
    setAutoRefresh(prev => !prev)
    logAuditEvent('REALTIME_TOGGLE_REFRESH', `Auto-refresh ${!autoRefresh ? 'enabled' : 'disabled'}`, 'REALTIME', 'PUBLIC')
  }, [autoRefresh])

  // Set refresh interval
  const setRefreshRate = useCallback((interval) => {
    setRefreshInterval(interval)
    logAuditEvent('REALTIME_SET_INTERVAL', `Refresh interval set to ${interval}ms`, 'REALTIME', 'PUBLIC')
  }, [])

  // Get real-time statistics
  const getStatistics = useCallback(() => {
    return {
      equipmentCount: equipmentData.size,
      missionCount: missionData.size,
      alertCount: systemAlerts.length,
      activityCount: userActivity.length,
      connectionUptime: connectionStatus === 'connected' ? 'Active' : 'Disconnected',
      lastUpdate: new Date().toISOString()
    }
  }, [equipmentData.size, missionData.size, systemAlerts.length, userActivity.length, connectionStatus])

  const value = {
    // Connection
    connectionStatus,
    latency,
    getConnectionInfo,
    
    // Data
    equipmentData: Array.from(equipmentData.values()),
    missionData: Array.from(missionData.values()),
    systemAlerts,
    userActivity,
    
    // Controls
    autoRefresh,
    refreshInterval,
    toggleAutoRefresh,
    setRefreshRate,
    refreshData,
    
    // Actions
    sendMessage,
    addAlert,
    clearAlerts,
    updateEquipment,
    updateMission,
    
    // Utilities
    getStatistics
  }

  return (
    <RealTimeContext.Provider value={value}>
      {children}
    </RealTimeContext.Provider>
  )
}
