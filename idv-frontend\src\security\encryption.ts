import CryptoJS from 'crypto-js';

class DefenseEncryption {
  private readonly secretKey = 'DRDO_IDX_SECRET_KEY_2025';

  encrypt(data: any, level: 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET'): string {
    return CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey, {
      mode: CryptoJS.mode.GCM,
      keySize: level === 'SECRET' ? 256 : 128,
    }).toString();
  }

  decrypt(encryptedData: string): any {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.secretKey);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  }
}

export default new DefenseEncryption();
