{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAOM,MAAM,kDAAiC,CAAA,GAAA,yCAAmB;IAErD,OAAO,IAAa,EAAW;QACvC,OAAO,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK;IAC5E;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;QAGT,sEAAsE;QACtE,mDAAmD;QACnD,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,IAAI,QAAQ,CAAA,GAAA,2CAAW,EAAE,CAAA,GAAA,4CAAY,EAAE,WAAW,IAAI,CAAC,UAAU;YACjE,IAAI,OACF,OAAO,MAAM,GAAG;YAGlB,IAAI,WAAW,IAAI,CAAC,WAAW;YAC/B,IAAI,YAAY,MACd,OAAO;YAGT,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACxC,IAAI,CAAC,WACH,OAAO;YAGT,OAAO,KAAK,CAAC,0BAA0B,UAAU,UAAU,KAAK;QAClE;QAEA,OAAO,KAAK,CAAC,YAAY;IAC3B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;QAGT,2DAA2D;QAC3D,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,IAAI,SAAS,UAAU,SAAS,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,IAAI;YAC1F,IAAI,UAAU,OAAO,IAAI,KAAK,UAC5B,OAAO,OAAO,GAAG;YAGnB,OAAO;QACT;QAEA,8CAA8C;QAC9C,IAAI,WAAW,KAAK,CAAC,YAAY;QACjC,IAAI,YAAY,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY;QACvE,IAAI,aAAa,UAAU,IAAI,KAAK,aAClC,OAAO;QAGT,gEAAgE;QAChE,+BAA+B;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG;QAGrD,6DAA6D;QAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;IACvC;IAEQ,kBAAkB,MAAe,EAAc;QACrD,2BAA2B;QAC3B,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;QAC7D,IAAI,OAAO,MACT,OAAO;QAGT,kCAAkC;QAClC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC;QAClD,KAAK,IAAI,QAAQ,CAAA,GAAA,4CAAY,EAAE,KAAK,IAAI,CAAC,UAAU,EAAG;YACpD,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,KAAK,GAAG;QAEnB;QAEA,OAAO;IACT;IAEQ,sBAAsB,MAAe,EAAc;QACzD,0BAA0B;QAC1B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,EAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;QACjE,IAAI,OAAO,MACT,OAAO;QAGT,iCAAiC;QACjC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC;QAClD,IAAI,aAAa;eAAI,CAAA,GAAA,4CAAY,EAAE,KAAK,IAAI,CAAC,UAAU;SAAE;QACzD,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC/C,IAAI,OAAO,UAAU,CAAC,EAAE;YACxB,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,KAAK,GAAG;QAEnB;QAEA,OAAO;IACT;IAEA,cAAc,GAAQ,EAAc;QAClC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,uDAAuD;QACvD,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,IAAI,CAAC,SAAS,KAAK,QACtB,IAAI,CAAC,qBAAqB,CAAC,QAC3B,IAAI,CAAC,iBAAiB,CAAC;QAG7B,OAAO,KAAK,CAAC,cAAc;IAC7B;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,2DAA2D;QAC3D,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,IAAI,CAAC,SAAS,KAAK,QACtB,IAAI,CAAC,iBAAiB,CAAC,QACvB,IAAI,CAAC,qBAAqB,CAAC;QAGjC,OAAO,KAAK,CAAC,aAAa;IAC5B;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,MAAM,oBAAA,qBAAA,UAAW,IAAI,CAAC,WAAW;QACrC,IAAI,OAAO,MACT,OAAO;QAGT,6DAA6D;QAC7D,IAAI,YAAY,WAAW,OAAO,CAAC;YAE3B;QADR,IAAI,CAAA,sBAAA,gCAAA,UAAW,IAAI,MAAK,QACtB,MAAM,CAAA,uBAAA,UAAU,SAAS,cAAnB,kCAAA,uBAAuB;QAG/B,IAAI,aAAa;QACjB,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAGT,IAAI,KAAK,SAAS,EAAE;gBAClB,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;gBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAC/C,OAAO,KAAK,GAAG;YAEnB;YAEA,6DAA6D;YAC7D,KAAK,IAAI,QAAQ,CAAA,GAAA,4CAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAAG;gBACrD,IAAI,SAAS,WAAW,OAAO,CAAC,KAAK,KAAK,CAAC;gBAC3C,IAAI,WAAW,mBAAmB,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,KAAK,SAAS,EAAE;oBACpE,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;oBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAAG;wBAClD,gFAAgF;wBAChF,IAAI,WAAW,WAAW,OAAO,WAAW,OAAO,CAAC,WAAW;wBAC/D,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,MAAK,SACtB,KAAK,GAAG,GACR,KAAK,GAAG;oBACd;gBACF;YACF;YAEA,MAAM,IAAI,CAAC,WAAW,CAAC;YAEvB,sDAAsD;YACtD,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,MAAM,IAAI,CAAC,WAAW;gBACtB,aAAa;YACf;QACF;QAEA,OAAO;IACT;AACF", "sources": ["packages/@react-aria/table/src/TableKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getChildNodes, getFirstItem} from '@react-stately/collections';\nimport {GridKeyboardDelegate} from '@react-aria/grid';\nimport {Key, Node} from '@react-types/shared';\nimport {TableCollection} from '@react-types/table';\n\nexport class TableKeyboardDelegate<T> extends GridKeyboardDelegate<T, TableCollection<T>> {\n\n  protected isCell(node: Node<T>): boolean {\n    return node.type === 'cell' || node.type === 'rowheader' || node.type === 'column';\n  }\n\n  getKeyBelow(key: Key): Key | null {\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the first child column if any,\n    // or find the corresponding cell in the first row.\n    if (startItem.type === 'column') {\n      let child = getFirstItem(getChildNodes(startItem, this.collection));\n      if (child) {\n        return child.key;\n      }\n\n      let firstKey = this.getFirstKey();\n      if (firstKey == null) {\n        return null;\n      }\n\n      let firstItem = this.collection.getItem(firstKey);\n      if (!firstItem) {\n        return null;\n      }\n\n      return super.getKeyForItemInRowByIndex(firstKey, startItem.index);\n    }\n\n    return super.getKeyBelow(key);\n  }\n\n  getKeyAbove(key: Key): Key | null {\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a column, focus the parent column if any\n    if (startItem.type === 'column') {\n      let parent = startItem.parentKey != null ? this.collection.getItem(startItem.parentKey) : null;\n      if (parent && parent.type === 'column') {\n        return parent.key;\n      }\n\n      return null;\n    }\n\n    // only return above row key if not header row\n    let superKey = super.getKeyAbove(key);\n    let superItem = superKey != null ? this.collection.getItem(superKey) : null;\n    if (superItem && superItem.type !== 'headerrow') {\n      return superKey;\n    }\n\n    // If no item was found, and focus was on a cell, then focus the\n    // corresponding column header.\n    if (this.isCell(startItem)) {\n      return this.collection.columns[startItem.index].key;\n    }\n\n    // If focus was on a row, then focus the first column header.\n    return this.collection.columns[0].key;\n  }\n\n  private findNextColumnKey(column: Node<T>): Key | null {\n    // Search following columns\n    let key = this.findNextKey(column.key, item => item.type === 'column');\n    if (key != null) {\n      return key;\n    }\n\n    // Wrap around to the first column\n    let row = this.collection.headerRows[column.level];\n    for (let item of getChildNodes(row, this.collection)) {\n      if (item.type === 'column') {\n        return item.key;\n      }\n    }\n\n    return null;\n  }\n\n  private findPreviousColumnKey(column: Node<T>): Key | null {\n    // Search previous columns\n    let key = this.findPreviousKey(column.key, item => item.type === 'column');\n    if (key != null) {\n      return key;\n    }\n\n    // Wrap around to the last column\n    let row = this.collection.headerRows[column.level];\n    let childNodes = [...getChildNodes(row, this.collection)];\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      let item = childNodes[i];\n      if (item.type === 'column') {\n        return item.key;\n      }\n    }\n\n    return null;\n  }\n\n  getKeyRightOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the next column\n    if (item.type === 'column') {\n      return this.direction === 'rtl'\n        ? this.findPreviousColumnKey(item)\n        : this.findNextColumnKey(item);\n    }\n\n    return super.getKeyRightOf(key);\n  }\n\n  getKeyLeftOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the previous column\n    if (item.type === 'column') {\n      return this.direction === 'rtl'\n        ? this.findNextColumnKey(item)\n        : this.findPreviousColumnKey(item);\n    }\n\n    return super.getKeyLeftOf(key);\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    let key = fromKey ?? this.getFirstKey();\n    if (key == null) {\n      return null;\n    }\n\n    // If the starting key is a cell, search from its parent row.\n    let startItem = collection.getItem(key);\n    if (startItem?.type === 'cell') {\n      key = startItem.parentKey ?? null;\n    }\n\n    let hasWrapped = false;\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      if (item.textValue) {\n        let substring = item.textValue.slice(0, search.length);\n        if (this.collator.compare(substring, search) === 0) {\n          return item.key;\n        }\n      }\n\n      // Check each of the row header cells in this row for a match\n      for (let cell of getChildNodes(item, this.collection)) {\n        let column = collection.columns[cell.index];\n        if (collection.rowHeaderColumnKeys.has(column.key) && cell.textValue) {\n          let substring = cell.textValue.slice(0, search.length);\n          if (this.collator.compare(substring, search) === 0) {\n            // If we started on a cell, end on the matching cell. Otherwise, end on the row.\n            let fromItem = fromKey != null ? collection.getItem(fromKey) : startItem;\n            return fromItem?.type === 'cell'\n              ? cell.key\n              : item.key;\n          }\n        }\n      }\n\n      key = this.getKeyBelow(key);\n\n      // Wrap around when reaching the end of the collection\n      if (key == null && !hasWrapped) {\n        key = this.getFirstKey();\n        hasWrapped = true;\n      }\n    }\n\n    return null;\n  }\n}\n"], "names": [], "version": 3, "file": "TableKeyboardDelegate.main.js.map"}