module.exports = {
    "alpha": `Alfa`,
    "black": `svart`,
    "blue": `Bl\xe5tt`,
    "blue purple": `bl\xe5lila`,
    "brightness": `Ljusstyrka`,
    "brown": `brun`,
    "brown yellow": `brungul`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `cyan`,
    "cyan blue": `cyanbl\xe5`,
    "dark": `m\xf6rk`,
    "gray": `gr\xe5`,
    "grayish": `gr\xe5aktig`,
    "green": `Gr\xf6nt`,
    "green cyan": `gr\xf6n cyan`,
    "hue": `Nyans`,
    "light": `ljus`,
    "lightness": `Ljushet`,
    "magenta": `magenta`,
    "magenta pink": `magentarosa`,
    "orange": `orange`,
    "orange yellow": `orangegul`,
    "pale": `blek`,
    "pink": `rosa`,
    "pink red": `rosar\xf6d`,
    "purple": `lila`,
    "purple magenta": `lila magenta`,
    "red": `R\xf6tt`,
    "red orange": `r\xf6dorange`,
    "saturation": `M\xe4ttnad`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} genomskinlig`,
    "very dark": `mycket m\xf6rk`,
    "very light": `mycket ljus`,
    "vibrant": `livfull`,
    "white": `vit`,
    "yellow": `gul`,
    "yellow green": `gulgr\xf6n`
};


//# sourceMappingURL=sv-SE.main.js.map
