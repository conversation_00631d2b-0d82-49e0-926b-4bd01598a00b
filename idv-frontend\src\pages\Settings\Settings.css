/* Settings Page - DRDO v1.7 */
.settings-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.settings-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.settings-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.settings-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.settings-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 32px;
}

.settings-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.settings-section.development {
  border-color: #F59E0B;
  background: #FFFBEB;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
  margin-bottom: 12px;
}

.setting-select,
.setting-input {
  width: 100%;
  max-width: 300px;
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
  transition: border-color 0.3s ease;
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.setting-checkbox {
  margin-right: 8px;
  transform: scale(1.2);
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.theme-card {
  border: 2px solid #E2E8F0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.theme-card:hover {
  border-color: #4A90E2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
}

.theme-card.active {
  border-color: #FF9933;
  background: #FFF7ED;
  box-shadow: 0 4px 12px rgba(255, 153, 51, 0.2);
}

.theme-preview {
  height: 60px;
  border-radius: 8px;
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;
}

.theme-accent {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30%;
  height: 30%;
  border-radius: 8px 0 8px 0;
}

.theme-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 4px 0;
}

.theme-info p {
  font-size: 12px;
  color: #64748B;
  margin: 0;
}

/* Language Grid */
.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.language-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.language-card:hover {
  border-color: #4A90E2;
}

.language-card.active {
  border-color: #FF9933;
  background: #FFF7ED;
}

.language-flag {
  font-size: 24px;
}

.language-name {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

/* Development Section */
.dev-info {
  background: #FEF3C7;
  border: 1px solid #F59E0B;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.dev-info p {
  margin: 0;
  color: #92400E;
  font-size: 14px;
}

.role-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.role-btn {
  padding: 12px 20px;
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  background: white;
  color: #1A3C5E;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-btn:hover {
  border-color: #4A90E2;
  background: #F8FAFC;
}

.role-btn.scientist {
  border-color: #059669;
  color: #059669;
}

.role-btn.commander {
  border-color: #DC2626;
  color: #DC2626;
}

.role-btn.admin {
  border-color: #7C3AED;
  color: #7C3AED;
}

/* Action Buttons */
.settings-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #E2E8F0;
}

.btn-primary,
.btn-secondary {
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

.btn-secondary {
  background: white;
  color: #1A3C5E;
  border: 2px solid #E2E8F0;
}

.btn-secondary:hover {
  border-color: #4A90E2;
  background: #F8FAFC;
}

/* Settings Footer */
.settings-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-content {
    padding: 24px 16px;
  }
  
  .settings-section {
    padding: 24px 16px;
  }
  
  .theme-grid {
    grid-template-columns: 1fr;
  }
  
  .language-grid {
    grid-template-columns: 1fr;
  }
  
  .role-buttons {
    flex-direction: column;
  }
  
  .settings-actions {
    flex-direction: column;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-section {
  animation: fadeIn 0.5s ease-out;
}
