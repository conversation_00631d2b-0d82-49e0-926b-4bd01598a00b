import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent, getAuditLogs } from '../../utils/audit'
import { encryptData, decryptData } from '../../utils/encryption'
import './AuditLog.css'

const AuditLog = () => {
  const { user, hasPermission } = useAuth()
  const [auditLogs, setAuditLogs] = useState([])
  const [filteredLogs, setFilteredLogs] = useState([])
  const [filters, setFilters] = useState({
    action: 'all',
    classification: 'all',
    user: 'all',
    dateRange: 'today'
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLog, setSelectedLog] = useState(null)

  const actionTypes = [
    'USER_LOGIN', 'USER_LOGOUT', 'DATA_VIEW', 'DATA_EXPORT', 'FILE_UPLOAD',
    'SETTINGS_UPDATE', 'PROFILE_UPDATE', 'CHART_GENERATE', 'SYSTEM_ACCESS'
  ]

  const classificationLevels = ['PUBLIC', 'RESTRICTED', 'CONFIDENTIAL', 'SECRET', 'TOP_SECRET']

  useEffect(() => {
    // Get audit logs and add some mock data
    const logs = getAuditLogs()
    const mockLogs = [
      {
        id: 'audit_001',
        timestamp: new Date().toISOString(),
        action: 'AUDIT_LOG_VIEW',
        description: 'Audit log page accessed',
        module: 'AUDIT_LOG',
        classification: 'RESTRICTED',
        userId: user?.id,
        userName: user?.name,
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        success: true
      },
      ...logs
    ]
    
    setAuditLogs(mockLogs)
    setFilteredLogs(mockLogs)
    
    logAuditEvent('AUDIT_LOG_VIEW', 'Audit log page accessed', 'AUDIT_LOG', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  useEffect(() => {
    let filtered = auditLogs

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(log => 
        log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.module.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Action filter
    if (filters.action !== 'all') {
      filtered = filtered.filter(log => log.action === filters.action)
    }

    // Classification filter
    if (filters.classification !== 'all') {
      filtered = filtered.filter(log => log.classification === filters.classification)
    }

    // User filter
    if (filters.user !== 'all') {
      filtered = filtered.filter(log => log.userId === filters.user)
    }

    // Date range filter
    const now = new Date()
    if (filters.dateRange !== 'all') {
      filtered = filtered.filter(log => {
        const logDate = new Date(log.timestamp)
        switch (filters.dateRange) {
          case 'today':
            return logDate.toDateString() === now.toDateString()
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            return logDate >= weekAgo
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            return logDate >= monthAgo
          default:
            return true
        }
      })
    }

    setFilteredLogs(filtered)
  }, [auditLogs, filters, searchTerm])

  const getClassificationColor = (classification) => {
    const colors = {
      'PUBLIC': '#64748B',
      'RESTRICTED': '#059669',
      'CONFIDENTIAL': '#3B82F6',
      'SECRET': '#F59E0B',
      'TOP_SECRET': '#DC2626'
    }
    return colors[classification] || '#64748B'
  }

  const getActionIcon = (action) => {
    const icons = {
      'USER_LOGIN': '🔐',
      'USER_LOGOUT': '🚪',
      'DATA_VIEW': '👁️',
      'DATA_EXPORT': '📤',
      'FILE_UPLOAD': '📁',
      'SETTINGS_UPDATE': '⚙️',
      'PROFILE_UPDATE': '👤',
      'CHART_GENERATE': '📊',
      'SYSTEM_ACCESS': '🖥️',
      'AUDIT_LOG_VIEW': '📋'
    }
    return icons[action] || '📝'
  }

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleString()
  }

  const exportLogs = (format) => {
    const exportData = filteredLogs.map(log => ({
      timestamp: log.timestamp,
      action: log.action,
      user: log.userName,
      description: log.description,
      classification: log.classification,
      module: log.module
    }))

    if (format === 'CSV') {
      const csv = [
        Object.keys(exportData[0]).join(','),
        ...exportData.map(row => Object.values(row).join(','))
      ].join('\n')
      
      const blob = new Blob([csv], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
    }

    logAuditEvent('AUDIT_EXPORT', `Audit logs exported as ${format}`, 'AUDIT_LOG', 'CONFIDENTIAL', user?.id, user?.name)
  }

  const viewLogDetails = (log) => {
    setSelectedLog(log)
    logAuditEvent('AUDIT_DETAIL_VIEW', `Viewed audit log details: ${log.id}`, 'AUDIT_LOG', 'RESTRICTED', user?.id, user?.name)
  }

  if (!hasPermission('admin')) {
    return (
      <div className="audit-log-page">
        <div className="access-denied">
          <h2>🔒 Access Denied</h2>
          <p>You need administrator privileges to access audit logs.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="audit-log-page">
      <div className="audit-header">
        <h1 className="audit-title">📋 Audit Log</h1>
        <p className="audit-subtitle">Encrypted audit trail with filtering and export functionality</p>
      </div>

      <div className="audit-content">
        {/* Filters and Search */}
        <div className="audit-controls">
          <div className="search-section">
            <input
              type="text"
              placeholder="Search logs by description, user, or module..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="filter-section">
            <select 
              value={filters.action} 
              onChange={(e) => setFilters(prev => ({...prev, action: e.target.value}))}
              className="filter-select"
            >
              <option value="all">All Actions</option>
              {actionTypes.map(action => (
                <option key={action} value={action}>{action.replace('_', ' ')}</option>
              ))}
            </select>
            
            <select 
              value={filters.classification} 
              onChange={(e) => setFilters(prev => ({...prev, classification: e.target.value}))}
              className="filter-select"
            >
              <option value="all">All Classifications</option>
              {classificationLevels.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
            
            <select 
              value={filters.dateRange} 
              onChange={(e) => setFilters(prev => ({...prev, dateRange: e.target.value}))}
              className="filter-select"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
            </select>
          </div>

          <div className="export-section">
            <button onClick={() => exportLogs('CSV')} className="btn-export">
              📥 Export CSV
            </button>
          </div>
        </div>

        {/* Audit Logs Table */}
        <div className="audit-table-container">
          <table className="audit-table">
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Action</th>
                <th>User</th>
                <th>Description</th>
                <th>Module</th>
                <th>Classification</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredLogs.map(log => (
                <tr key={log.id} className={`log-row ${log.success ? 'success' : 'failure'}`}>
                  <td className="timestamp-cell">
                    {formatTimestamp(log.timestamp)}
                  </td>
                  <td className="action-cell">
                    <span className="action-icon">{getActionIcon(log.action)}</span>
                    {log.action.replace('_', ' ')}
                  </td>
                  <td className="user-cell">
                    {log.userName || 'System'}
                  </td>
                  <td className="description-cell">
                    {log.description}
                  </td>
                  <td className="module-cell">
                    {log.module}
                  </td>
                  <td className="classification-cell">
                    <span 
                      className="classification-badge"
                      style={{ backgroundColor: getClassificationColor(log.classification) }}
                    >
                      {log.classification}
                    </span>
                  </td>
                  <td className="actions-cell">
                    <button 
                      onClick={() => viewLogDetails(log)}
                      className="btn-view-details"
                    >
                      👁️
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredLogs.length === 0 && (
          <div className="no-logs">
            <h3>No audit logs found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>

      {/* Log Details Modal */}
      {selectedLog && (
        <div className="modal-overlay" onClick={() => setSelectedLog(null)}>
          <div className="log-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Audit Log Details</h2>
              <button onClick={() => setSelectedLog(null)} className="modal-close">✕</button>
            </div>
            <div className="modal-content">
              <div className="log-detail-grid">
                <div className="detail-item">
                  <label>ID:</label>
                  <span>{selectedLog.id}</span>
                </div>
                <div className="detail-item">
                  <label>Timestamp:</label>
                  <span>{formatTimestamp(selectedLog.timestamp)}</span>
                </div>
                <div className="detail-item">
                  <label>Action:</label>
                  <span>{selectedLog.action}</span>
                </div>
                <div className="detail-item">
                  <label>User:</label>
                  <span>{selectedLog.userName} ({selectedLog.userId})</span>
                </div>
                <div className="detail-item">
                  <label>Description:</label>
                  <span>{selectedLog.description}</span>
                </div>
                <div className="detail-item">
                  <label>Module:</label>
                  <span>{selectedLog.module}</span>
                </div>
                <div className="detail-item">
                  <label>Classification:</label>
                  <span>{selectedLog.classification}</span>
                </div>
                <div className="detail-item">
                  <label>IP Address:</label>
                  <span>{selectedLog.ipAddress || 'N/A'}</span>
                </div>
                <div className="detail-item">
                  <label>Success:</label>
                  <span>{selectedLog.success ? '✅ Yes' : '❌ No'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Audit Footer */}
      <div className="audit-footer">
        <div className="footer-stats">
          <span>Total Logs: {auditLogs.length}</span>
          <span>Filtered Results: {filteredLogs.length}</span>
          <span>Current User: {user?.name}</span>
        </div>
      </div>
    </div>
  )
}

export default AuditLog
