{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AASD,IAAI,mCAAa,IAAI,CAAA,GAAA,gCAAwB,EAAE,CAAA,GAAA,oDAAW;AAGnD,SAAS,yCAAW,KAAa;IACtC,IAAI,MAAM,+BAAS,KAAK,CAAC,UAAU,+BAAS,KAAK,CAAC,UAAU,+BAAS,KAAK,CAAC;IAC3E,IAAI,KACF,OAAO;IAGT,MAAM,IAAI,MAAM,0BAA0B;AAC5C;AAEO,SAAS,0CAAe,CAAkB;IAC/C,IAAI,OAAO,MAAM,UACf,OAAO,yCAAW;SAElB,OAAO;AAEX;AAGO,SAAS,0CAAiB,UAAsB;IACrD,OAAQ;QACN,KAAK;YACH,OAAO,+BAAS,aAAa;QAC/B,KAAK;YACH,OAAO,+BAAS,aAAa;QAC/B,KAAK;YACH,OAAO,+BAAS,aAAa;IACjC;AACF;AAKO,SAAS,0CAAa,GAAW;IACtC,IAAI,QAAQ,KACV,OAAO;IAGT,OAAO,AAAC,CAAA,AAAC,MAAM,MAAO,GAAE,IAAK;AAC/B;AAEA,gDAAgD;AAChD,MAAM,mDAA6B;AACnC,8DAA8D;AAC9D,MAAM,yDAAmC;AACzC,iDAAiD;AACjD,MAAM,2CAAqB;AAC3B,+CAA+C;AAC/C,MAAM,uCAAiB;AACvB,MAAM,mCAAiC;IACrC;QAAC;QAAG;KAAO;IACX;QAAC;QAAI;KAAM;IACX;QAAC;QAAI;KAAS;IACd;QAAC;QAAI;KAAS;IACd;QAAC;QAAK;KAAQ;IACd;QAAC;QAAK;KAAO;IACb;QAAC;QAAK;KAAO;IACb;QAAC;QAAK;KAAS;IACf;QAAC;QAAK;KAAU;IAChB;QAAC;QAAK;KAAO;CACd;AAED,MAAe;IAQb,WAAmB;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,QAAQ;IACtC;IAEA,gBAAgB,OAAqB,EAAU;QAC7C,IAAI,WAAW,IAAI,EACjB,OAAO,IAAI,CAAC,QAAQ;QAGtB,MAAM,IAAI,MAAM,gCAAgC;IAClD;IAEA,iBAAiB,OAAqB,EAAE,KAAa,EAAU;QAC7D,IAAI,WAAW,IAAI,EAAE;YACnB,IAAI,IAAI,IAAI,CAAC,KAAK;YAClB,CAAC,CAAC,QAAQ,GAAG;YACb,OAAO;QACT;QAEA,MAAM,IAAI,MAAM,gCAAgC;IAClD;IAEA,eAAe,OAAqB,EAAE,MAAc,EAAE;QACpD,IAAI,UAAU,CAAA,GAAA,gCAAwB,EAAE,6BAA6B,CAAC,2BAA2B;QACjG,OAAO,QAAQ,kBAAkB,CAAC,SAAS;IAC7C;IAIA,kBAAkB,UAA8D,EAAa;QAC3F,IAAI,YAAC,QAAQ,YAAE,QAAQ,EAAC,GAAG;QAC3B,IAAI,MAAM,YAAY,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA,IAAK,MAAM;QAC9D,IAAI,MAAM,YAAY,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA,IAAK,MAAM;QAC9D,IAAI,MAAM,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA,IAAK,MAAM,OAAO,MAAM;QAE/D,OAAO;YAAC,UAAU;YAAK,UAAU;YAAK,UAAU;QAAG;IACrD;IAIA,aAAa,MAAc,EAAU;QACnC,0FAA0F;QAC1F,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,8BAAQ,IAAI;QAE5B,IAAI,UAAU,CAAA,GAAA,gCAAwB,EAAE,6BAA6B,CAAC,2BAA2B;QACjG,IAAI,IAAI,OACN,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QAG7C,IAAI,IAAI,OACN,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QAG7C,IAAI;QACJ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG;QAErC,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,KAAK,OAAO,KAAK;YACnB,IAAI,KAAK,KACP,SAAS;iBAET,SAAS;eAEN,IAAI,KAAK,MACd,SAAS;QAGX,IAAI,IAAI,KACN,YAAY;aACP,IAAI,IAAI,0CACb,YAAY;aACP,IAAI,IAAI;aAER,IAAI,IAAI,MACb,YAAY;aAEZ,YAAY;QAGd,IAAI,QACF,SAAS,QAAQ,kBAAkB,CAAC,QAAQ;QAG9C,IAAI,WACF,YAAY,QAAQ,kBAAkB,CAAC,WAAW;QAGpD,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC;QACjC,IAAI,YAAY,IAAI,CAAA,GAAA,+BAAuB,EAAE,QAAQ;QACrD,IAAI,QAAQ,GAAG;YACb,IAAI,qBAAqB,IAAI,CAAA,GAAA,sBAAc,EAAE,QAAQ;gBAAC,OAAO;YAAS,GAAG,MAAM,CAAC,IAAI;YACpF,OAAO,UAAU,MAAM,CAAC,wBAAwB;2BAC9C;wBACA;qBACA;oCACA;YACF,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI;QAC9B,OACE,OAAO,UAAU,MAAM,CAAC,aAAa;uBACnC;oBACA;iBACA;QACF,GAAG,OAAO,CAAC,QAAQ,KAAK,IAAI;IAEhC;IAEQ,YAAY,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAc,EAAoB;QACrF,IAAI,UAAU,CAAA,GAAA,gCAAwB,EAAE,6BAA6B,CAAC,2BAA2B;QACjG,IAAI,IAAI,sCACN,OAAO;YAAC,QAAQ,kBAAkB,CAAC,QAAQ;YAAS;SAAE;QAGxD,IAAK,IAAI,IAAI,GAAG,IAAI,iCAAW,MAAM,EAAE,IAAK;YAC1C,IAAI,CAAC,KAAK,QAAQ,GAAG,gCAAU,CAAC,EAAE;YAClC,IAAI,CAAC,SAAS,YAAY,GAAG,gCAAU,CAAC,IAAI,EAAE,IAAI;gBAAC;gBAAK;aAAO;YAC/D,IAAI,KAAK,OAAO,IAAI,SAAS;gBAC3B,6DAA6D;gBAC7D,IAAI,YAAY;oBACd,IAAI,IAAI,kDACN,UAAU;yBAEV,oBAAoB;oBACpB,IAAI,AAAC,IAAI,mDAA8B;;gBAI3C,iFAAiF;gBACjF,IAAI,IAAI,MAAM,AAAC,CAAA,UAAU,GAAE,IAAK,KAAK,YAAY,aAC/C,UAAU,GAAG,QAAQ,CAAC,EAAE,aAAa;qBAChC,IAAI,YAAY,YAAY,IAAI,wDACrC,mDAAmD;gBACnD,UAAU;gBAGZ,IAAI,OAAO,QAAQ,kBAAkB,CAAC,SAAS,QAAQ,iBAAiB,CAAC;gBACzE,OAAO;oBAAC;oBAAM;iBAAE;YAClB;QACF;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,WAAW,MAAc,EAAU;QACjC,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,8BAAQ,IAAI;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG;QACvC,OAAO;IACT;AACF;AAEA,MAAM,uCAAiB;IAKrB,OAAO,MAAM,KAAa,EAAE;QAC1B,IAAI,SAAoC,EAAE;QAC1C,2CAA2C;QAC3C,IAAI,eAAe,IAAI,CAAC,UAAU;YAAC;YAAG;YAAG;YAAG;SAAE,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;YACrE,MAAM,SAAS,AAAC,CAAA,MAAM,MAAM,GAAG,IAAI,MAAM,OAAO,CAAC,UAAU,UAAU,KAAI,EAAG,KAAK,CAAC,GAAG,KAAK,CAAC;YAC3F,MAAO,OAAO,MAAM,GAAG,EACrB,OAAO,IAAI,CAAC,SAAS,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK;YAErD,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,KAAK,YAAY,MAAM,CAAC,EAAE,GAAG,MAAM;QAC1D;QAEA,wDAAwD;QACxD,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,kBAAA,4BAAA,KAAO,CAAC,EAAE,EAAE;YACd,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,QAAS,OAAO,MAAM,IAAI;YAC3D,SAAS,OAAO,GAAG,CAAC,CAAC,KAAK;gBACxB,OAAO,CAAA,GAAA,YAAI,EAAE,gBAAA,iBAAA,MAAO,GAAG,GAAG,IAAI,IAAI,MAAM;YAC1C;QACF;QACA,IAAI,MAAM,CAAC,EAAE,KAAK,aAAa,MAAM,CAAC,EAAE,KAAK,aAAa,MAAM,CAAC,EAAE,KAAK,WACtE,OAAO;YAG4E;QAArF,OAAO,OAAO,MAAM,GAAG,IAAI,YAAY,IAAI,+BAAS,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,CAAA,WAAA,MAAM,CAAC,EAAE,cAAT,sBAAA,WAAa;IACpG;IAEA,SAAS,SAA8B,KAAK,EAAE;QAC5C,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,AAAC,CAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAG,EAAG,WAAW;YACxJ,KAAK;gBACH,OAAO,MAAM,AAAC,CAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAG,EAAG,WAAW;YACrN,KAAK;gBACH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACxD,KAAK;YACL,KAAK;gBACH,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACxE;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,QAAQ,CAAC;QAC1C;IACF;IAEA,SAAS,MAAmB,EAAU;QACpC,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,IAAI;YACb,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB;gBACE,MAAM,IAAI,MAAM,0CAA0C;QAC9D;IACF;IAEA,WAAmB;QACjB,OAAO,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI;IACrD;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG;QACvB,MAAM,QAAQ,IAAI,CAAC,KAAK,GAAG;QAC3B,MAAM,OAAO,IAAI,CAAC,IAAI,GAAG;QACzB,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO;QACjC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,OAAO;QACxC,MAAM,SAAS,aAAa;QAC5B,MAAM,aAAa,eAAe,IAAI,IAAI,SAAS;QACnD,IAAI,MAAM,GAAG,aAAa;QAE1B,IAAI,WAAW,GAAG;YAChB,OAAQ;gBACN,KAAK;oBACH,MAAM,AAAC,CAAA,QAAQ,IAAG,IAAK,SAAU,CAAA,QAAQ,OAAO,IAAI,CAAA;oBACpD;gBACF,KAAK;oBACH,MAAM,AAAC,CAAA,OAAO,GAAE,IAAK,SAAS;oBAC9B;gBACF,KAAK;oBACH,MAAM,AAAC,CAAA,MAAM,KAAI,IAAK,SAAS;oBAC/B;YACJ;YAEA,OAAO;QACT;QAEA,OAAO,IAAI,+BACT,CAAA,GAAA,oBAAY,EAAE,MAAM,KAAK,IACzB,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAChC,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAChC,IAAI,CAAC,KAAK;IAEd;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG;QACvB,MAAM,QAAQ,IAAI,CAAC,KAAK,GAAG;QAC3B,MAAM,OAAO,IAAI,CAAC,IAAI,GAAG;QACzB,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO;QACjC,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO;QACjC,MAAM,YAAY,AAAC,CAAA,MAAM,GAAE,IAAK;QAChC,MAAM,SAAS,MAAM;QACrB,IAAI;QACJ,IAAI;QAEJ,IAAI,WAAW,GACb,MAAM,aAAa,GAAG,aAAa;aAC9B;YACL,aAAa,SAAU,CAAA,YAAY,KAAK,MAAM,MAAM,IAAI,MAAM,GAAE;YAEhE,OAAQ;gBACN,KAAK;oBACH,MAAM,AAAC,CAAA,QAAQ,IAAG,IAAK,SAAU,CAAA,QAAQ,OAAO,IAAI,CAAA;oBACpD;gBACF,KAAK;oBACH,MAAM,AAAC,CAAA,OAAO,GAAE,IAAK,SAAS;oBAC9B;gBACF,KAAK;gBACL;oBACE,MAAM,AAAC,CAAA,MAAM,KAAI,IAAK,SAAS;oBAC/B;YACJ;YAEA,OAAO;QACT;QAEA,OAAO,IAAI,+BACT,CAAA,GAAA,oBAAY,EAAE,MAAM,KAAK,IACzB,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAChC,CAAA,GAAA,oBAAY,EAAE,YAAY,KAAK,IAC/B,IAAI,CAAC,KAAK;IACd;IAEA,QAAgB;QACd,OAAO,IAAI,+BAAS,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK;IACjE;IAEA,gBAAgB,OAAqB,EAAqB;QACxD,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAK,UAAU;oBAAM,MAAM;oBAAK,UAAU;gBAAI;YAClE,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAG,MAAM;oBAAM,UAAU;gBAAG;YAC7D;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,wBAAwB,OAAqB,EAA4B;QACvE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,OAAO;gBAAS;YAC1B,KAAK;gBACH,OAAO;oBAAC,OAAO;gBAAS;YAC1B;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,mBAAmB,OAAqB,EAAE,MAAc,EAAE;QACxD,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC;QAC3C,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC;QACjC,OAAO,IAAI,CAAA,GAAA,sBAAc,EAAE,QAAQ,SAAS,MAAM,CAAC;IACrD;IAEA,gBAA4B;QAC1B,OAAO;IACT;IAGA,mBAA+D;QAC7D,OAAO,+BAAS,aAAa;IAC/B;IAlMA,YAAY,AAAQ,GAAW,EAAE,AAAQ,KAAa,EAAE,AAAQ,IAAY,EAAE,AAAQ,KAAa,CAAE;QACnG,KAAK,SADa,MAAA,UAAqB,QAAA,YAAuB,OAAA,WAAsB,QAAA;IAEtF;AAiMF;AApMM,+BAgMG,gBAA4D;IAAC;IAAO;IAAS;CAAO;AAM7F,6DAA6D;AAC7D,0DAA0D;AAC1D,mBAAmB;AACnB,uBAAuB;AACvB,MAAM,kCAAY;AAElB,MAAM,uCAAiB;IAKrB,OAAO,MAAM,KAAa,EAAmB;QAC3C,IAAI;QACJ,IAAK,IAAI,MAAM,KAAK,CAAC,kCAAa;gBACV;YAAtB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,AAAC,CAAA,CAAA,MAAA,CAAC,CAAC,EAAE,cAAJ,iBAAA,MAAQ,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,KAAK;YACrF,OAAO,IAAI,+BAAS,0CAAa,IAAI,CAAA,GAAA,YAAI,EAAE,GAAG,GAAG,MAAM,CAAA,GAAA,YAAI,EAAE,GAAG,GAAG,MAAM,CAAA,GAAA,YAAI,EAAE,cAAA,eAAA,IAAK,GAAG,GAAG;QAC5F;IACF;IAEA,SAAS,SAA8B,KAAK,EAAE;QAC5C,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK;gBACH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC;YACzG,KAAK;gBACH,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACzH;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,QAAQ,CAAC;QAC1C;IACF;IAEA,SAAS,MAAmB,EAAU;QACpC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAI;YACb,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB;gBACE,MAAM,IAAI,MAAM,0CAA0C;QAC9D;IACF;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,YAAY,aAAc,CAAA,IAAI,aAAa,CAAA;QAC/C,aAAa,cAAc,KAAK,cAAc,IAAI,IAAI,AAAC,CAAA,aAAa,SAAQ,IAAK,KAAK,GAAG,CAAC,WAAW,IAAI;QAEzG,OAAO,IAAI,+BACT,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,GAAG,EAAE,IACxB,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAC9B,CAAA,GAAA,oBAAY,EAAE,YAAY,KAAK,IACjC,IAAI,CAAC,KAAK;IAEd;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,KAAK,CAAC,GAAW,IAAI,AAAC,CAAA,IAAI,MAAM,EAAC,IAAK,CAAC,GAAK,aAAa,aAAa,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;QACvH,OAAO,IAAI,+BACT,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,IAAI,CAAC,KAAK;IAEd;IAEA,QAAgB;QACd,OAAO,IAAI,+BAAS,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK;IAC5E;IAEA,gBAAgB,OAAqB,EAAqB;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAK,MAAM;oBAAG,UAAU;gBAAE;YAC3D,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAK,MAAM;oBAAG,UAAU;gBAAE;YAC3D,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAG,MAAM;oBAAM,UAAU;gBAAG;YAC7D;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,wBAAwB,OAAqB,EAA4B;QACvE,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC,OAAO;oBAAQ,MAAM;oBAAU,aAAa;gBAAQ;YAC9D,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,OAAO;gBAAS;YAC1B;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,mBAAmB,OAAqB,EAAE,MAAc,EAAE;QACxD,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC;QAC3C,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC;QACjC,IAAI,YAAY,gBAAgB,YAAY,cAC1C,SAAS;QAEX,OAAO,IAAI,CAAA,GAAA,sBAAc,EAAE,QAAQ,SAAS,MAAM,CAAC;IACrD;IAEA,gBAA4B;QAC1B,OAAO;IACT;IAGA,mBAA+D;QAC7D,OAAO,+BAAS,aAAa;IAC/B;IAjIA,YAAY,AAAQ,GAAW,EAAE,AAAQ,UAAkB,EAAE,AAAQ,UAAkB,EAAE,AAAQ,KAAa,CAAE;QAC9G,KAAK,SADa,MAAA,UAAqB,aAAA,iBAA4B,aAAA,iBAA4B,QAAA;IAEjG;AAgIF;AAnIM,+BA+HG,gBAA4D;IAAC;IAAO;IAAc;CAAa;AAMxG,6DAA6D;AAC7D,0DAA0D;AAC1D,mBAAmB;AACnB,uBAAuB;AACvB,MAAM,kCAAY;AAElB,MAAM,uCAAiB;IAKrB,OAAO,MAAM,KAAa,EAAmB;QAC3C,IAAI;QACJ,IAAK,IAAI,MAAM,KAAK,CAAC,kCAAa;gBACV;YAAtB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,AAAC,CAAA,CAAA,MAAA,CAAC,CAAC,EAAE,cAAJ,iBAAA,MAAQ,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,KAAK;YACrF,OAAO,IAAI,+BAAS,0CAAa,IAAI,CAAA,GAAA,YAAI,EAAE,GAAG,GAAG,MAAM,CAAA,GAAA,YAAI,EAAE,GAAG,GAAG,MAAM,CAAA,GAAA,YAAI,EAAE,cAAA,eAAA,IAAK,GAAG,GAAG;QAC5F;IACF;IAEA,SAAS,SAA8B,KAAK,EAAE;QAC5C,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC/B,KAAK;gBACH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC;YACxG,KAAK;YACL,KAAK;gBACH,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACxH;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,QAAQ,CAAC;QAC1C;IACF;IACA,SAAS,MAAmB,EAAU;QACpC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAI;YACb,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC,KAAK;YACnB;gBACE,MAAM,IAAI,MAAM,0CAA0C;QAC9D;IACF;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,YAAY,IAAI,CAAC,SAAS,GAAG;QACjC,IAAI,aAAa,YAAY,aAAa,KAAK,GAAG,CAAC,WAAW,IAAI;QAClE,aAAa,eAAe,IAAI,IAAI,IAAK,CAAA,IAAI,YAAY,UAAS;QAClE,OAAO,IAAI,+BACT,CAAA,GAAA,oBAAY,EAAE,IAAI,CAAC,GAAG,EAAE,IACxB,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAChC,CAAA,GAAA,oBAAY,EAAE,aAAa,KAAK,IAChC,IAAI,CAAC,KAAK;IAEd;IAEA;;;;GAIC,GACD,AAAQ,QAAgB;QACtB,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG;QACnC,IAAI,YAAY,IAAI,CAAC,SAAS,GAAG;QACjC,IAAI,IAAI,aAAa,KAAK,GAAG,CAAC,WAAW,IAAI;QAC7C,IAAI,KAAK,CAAC,GAAW,IAAI,AAAC,CAAA,IAAI,MAAM,EAAC,IAAK,EAAE,GAAK,YAAY,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI;QACrG,OAAO,IAAI,+BACT,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,KAAK,KAAK,CAAC,GAAG,KAAK,MACnB,IAAI,CAAC,KAAK;IAEd;IAEA,QAAgB;QACd,OAAO,IAAI,+BAAS,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK;IAC3E;IAEA,gBAAgB,OAAqB,EAAqB;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAK,MAAM;oBAAG,UAAU;gBAAE;YAC3D,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAK,MAAM;oBAAG,UAAU;gBAAE;YAC3D,KAAK;gBACH,OAAO;oBAAC,UAAU;oBAAG,UAAU;oBAAG,MAAM;oBAAM,UAAU;gBAAG;YAC7D;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,wBAAwB,OAAqB,EAA4B;QACvE,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAC,OAAO;oBAAQ,MAAM;oBAAU,aAAa;gBAAQ;YAC9D,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBAAC,OAAO;gBAAS;YAC1B;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,mBAAmB,OAAqB,EAAE,MAAc,EAAE;QACxD,IAAI,UAAU,IAAI,CAAC,uBAAuB,CAAC;QAC3C,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC;QACjC,IAAI,YAAY,gBAAgB,YAAY,aAC1C,SAAS;QAEX,OAAO,IAAI,CAAA,GAAA,sBAAc,EAAE,QAAQ,SAAS,MAAM,CAAC;IACrD;IAEA,gBAA4B;QAC1B,OAAO;IACT;IAGA,mBAA+D;QAC7D,OAAO,+BAAS,aAAa;IAC/B;IA/HA,YAAY,AAAQ,GAAW,EAAE,AAAQ,UAAkB,EAAE,AAAQ,SAAiB,EAAE,AAAQ,KAAa,CAAE;QAC7G,KAAK,SADa,MAAA,UAAqB,aAAA,iBAA4B,YAAA,gBAA2B,QAAA;IAEhG;AA8HF;AAjIM,+BA6HG,gBAA4D;IAAC;IAAO;IAAc;CAAY;AAMvG,2DAA2D;AAC3D,SAAS,8BAAQ,KAAY;IAC3B,IAAI,MAAM,MAAM,QAAQ,CAAC;IACzB,IAAI,MAAM,IAAI,eAAe,CAAC,SAAS;IACvC,IAAI,QAAQ,IAAI,eAAe,CAAC,WAAW;IAC3C,IAAI,OAAO,IAAI,eAAe,CAAC,UAAU;IACzC,CAAC,KAAK,OAAO,KAAK,GAAG,+BAAS,KAAK,OAAO;IAC1C,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,sCAAgB,KAAK,OAAO;IAC5C,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,mCAAa,GAAG,GAAG;IACnC,OAAO,qCAAe,GAAG,GAAG;AAC9B;AAEA,SAAS,qCAAe,CAAS,EAAE,CAAS,EAAE,CAAS;IACrD,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;IAC1C,OAAO;QACL;QACA,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;QACxB,OAAO,IAAI,MAAM,MAAM,IAAI,6BAA6B;KACzD;AACH;AAEA,SAAS,+BAAS,CAAS,EAAE,CAAS,EAAE,CAAS;IAC/C,kCAAkC;IAClC,iDAAiD;IACjD,uCAAuC;IACvC,qCAAqC;IACrC,8BAA8B;IAC9B,0EAA0E;IAC1E,yCAAyC;IACzC,OAAO;QAAC,yCAAmB;QAAI,yCAAmB;QAAI,yCAAmB;KAAG;AAC9E;AAEA,SAAS,yCAAmB,GAAW;IACrC,IAAI,OAAO,MAAM,IAAI,KAAK;IAC1B,IAAI,MAAM,KAAK,GAAG,CAAC;IAEnB,IAAI,OAAO,SACT,OAAO,MAAM;IAGf,OAAO,OAAQ,KAAK,GAAG,CAAC,AAAC,CAAA,MAAM,KAAI,IAAK,OAAO;AACjD;AAEA,SAAS,sCAAgB,CAAS,EAAE,CAAS,EAAE,CAAS;IACtD,0DAA0D;IAC1D,wDAAwD;IACxD,MAAM,IAAI;QACR,SAAS;QAAS,QAAQ;QAAS,QAAQ;QAC3C,QAAQ;QAAU,SAAS;QAAQ,QAAQ;QAC3C,OAAO;QAAW,QAAQ;QAAS,UAAU;KAC9C;IACD,OAAO,qCAAe,GAAG,GAAG,GAAG;AACjC;AAEA,SAAS,mCAAa,CAAS,EAAE,CAAS,EAAE,CAAS;IACnD,8CAA8C;IAC9C,MAAM,WAAW;QACf;QAAoB;QAAoB;QACxC;QAAoB;QAAqB;QACzC;QAAoB;QAAqB;KAC1C;IACD,MAAM,aAAa;QACjB;QAAqB;QAAoB;QACzC;QAAoB;QAAsB;QAC1C;QAAqB;QAAoB;KAC1C;IAED,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,qCAAe,UAAU,GAAG,GAAG;IAC/C,OAAO,qCAAe,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAC1E;AAEA,SAAS,qCAAe,CAAW,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;IAClE,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG;IACrC,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG;IACrC,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG;IACrC,OAAO;QAAC;QAAG;QAAG;KAAE;AAClB", "sources": ["packages/@react-stately/color/src/Color.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {clamp, toFixedNumber} from '@react-stately/utils';\nimport {ColorAxes, ColorChannel, ColorChannelRange, ColorFormat, ColorSpace, Color as IColor} from '@react-types/color';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {LocalizedStringDictionary, LocalizedStringFormatter} from '@internationalized/string';\nimport {NumberFormatter} from '@internationalized/number';\n\nlet dictionary = new LocalizedStringDictionary(intlMessages);\n\n/** Parses a color from a string value. Throws an error if the string could not be parsed. */\nexport function parseColor(value: string): IColor {\n  let res = RGBColor.parse(value) || HSBColor.parse(value) || HSLColor.parse(value);\n  if (res) {\n    return res;\n  }\n\n  throw new Error('Invalid color value: ' + value);\n}\n\nexport function normalizeColor(v: string | IColor): IColor {\n  if (typeof v === 'string') {\n    return parseColor(v);\n  } else {\n    return v;\n  }\n}\n\n/** Returns a list of color channels for a given color space. */\nexport function getColorChannels(colorSpace: ColorSpace): [ColorChannel, ColorChannel, ColorChannel] {\n  switch (colorSpace) {\n    case 'rgb':\n      return RGBColor.colorChannels;\n    case 'hsl':\n      return HSLColor.colorChannels;\n    case 'hsb':\n      return HSBColor.colorChannels;\n  }\n}\n\n/**\n * Returns the hue value normalized to the range of 0 to 360.\n */\nexport function normalizeHue(hue: number): number {\n  if (hue === 360) {\n    return hue;\n  }\n\n  return ((hue % 360) + 360) % 360;\n}\n\n// Lightness threshold between orange and brown.\nconst ORANGE_LIGHTNESS_THRESHOLD = 0.68;\n// Lightness threshold between pure yellow and \"yellow green\".\nconst YELLOW_GREEN_LIGHTNESS_THRESHOLD = 0.85;\n// The maximum lightness considered to be \"dark\".\nconst MAX_DARK_LIGHTNESS = 0.55;\n// The chroma threshold between gray and color.\nconst GRAY_THRESHOLD = 0.001;\nconst OKLCH_HUES: [number, string][] = [\n  [0, 'pink'],\n  [15, 'red'],\n  [48, 'orange'],\n  [94, 'yellow'],\n  [135, 'green'],\n  [175, 'cyan'],\n  [264, 'blue'],\n  [284, 'purple'],\n  [320, 'magenta'],\n  [349, 'pink']\n];\n\nabstract class Color implements IColor {\n  abstract toFormat(format: ColorFormat): IColor;\n  abstract toString(format: ColorFormat | 'css'): string;\n  abstract clone(): IColor;\n  abstract getChannelRange(channel: ColorChannel): ColorChannelRange;\n  abstract getChannelFormatOptions(channel: ColorChannel): Intl.NumberFormatOptions;\n  abstract formatChannelValue(channel: ColorChannel, locale: string): string;\n\n  toHexInt(): number {\n    return this.toFormat('rgb').toHexInt();\n  }\n\n  getChannelValue(channel: ColorChannel): number {\n    if (channel in this) {\n      return this[channel];\n    }\n\n    throw new Error('Unsupported color channel: ' + channel);\n  }\n\n  withChannelValue(channel: ColorChannel, value: number): IColor {\n    if (channel in this) {\n      let x = this.clone();\n      x[channel] = value;\n      return x;\n    }\n\n    throw new Error('Unsupported color channel: ' + channel);\n  }\n\n  getChannelName(channel: ColorChannel, locale: string) {\n    let strings = LocalizedStringDictionary.getGlobalDictionaryForPackage('@react-stately/color') || dictionary;\n    return strings.getStringForLocale(channel, locale);\n  }\n\n  abstract getColorSpace(): ColorSpace;\n\n  getColorSpaceAxes(xyChannels: {xChannel?: ColorChannel, yChannel?: ColorChannel}): ColorAxes {\n    let {xChannel, yChannel} = xyChannels;\n    let xCh = xChannel || this.getColorChannels().find(c => c !== yChannel)!;\n    let yCh = yChannel || this.getColorChannels().find(c => c !== xCh)!;\n    let zCh = this.getColorChannels().find(c => c !== xCh && c !== yCh)!;\n\n    return {xChannel: xCh, yChannel: yCh, zChannel: zCh};\n  }\n\n  abstract getColorChannels(): [ColorChannel, ColorChannel, ColorChannel]\n\n  getColorName(locale: string): string {\n    // Convert to oklch color space, which has perceptually uniform lightness across all hues.\n    let [l, c, h] = toOKLCH(this);\n\n    let strings = LocalizedStringDictionary.getGlobalDictionaryForPackage('@react-stately/color') || dictionary;\n    if (l > 0.999) {\n      return strings.getStringForLocale('white', locale);\n    }\n\n    if (l < 0.001) {\n      return strings.getStringForLocale('black', locale);\n    }\n\n    let hue: string;\n    [hue, l] = this.getOklchHue(l, c, h, locale);\n\n    let lightness = '';\n    let chroma = '';\n    if (c <= 0.1 && c >= GRAY_THRESHOLD) {\n      if (l >= 0.7) {\n        chroma = 'pale';\n      } else {\n        chroma = 'grayish';\n      }\n    } else if (c >= 0.15) {\n      chroma = 'vibrant';\n    }\n\n    if (l < 0.3) {\n      lightness = 'very dark';\n    } else if (l < MAX_DARK_LIGHTNESS) {\n      lightness = 'dark';\n    } else if (l < 0.7) {\n      // none\n    } else if (l < 0.85) {\n      lightness = 'light';\n    } else {\n      lightness = 'very light';\n    }\n\n    if (chroma) {\n      chroma = strings.getStringForLocale(chroma, locale);\n    }\n\n    if (lightness) {\n      lightness = strings.getStringForLocale(lightness, locale);\n    }\n\n    let alpha = this.getChannelValue('alpha');\n    let formatter = new LocalizedStringFormatter(locale, strings);\n    if (alpha < 1) {\n      let percentTransparent = new NumberFormatter(locale, {style: 'percent'}).format(1 - alpha);\n      return formatter.format('transparentColorName', {\n        lightness,\n        chroma,\n        hue,\n        percentTransparent\n      }).replace(/\\s+/g, ' ').trim();\n    } else {\n      return formatter.format('colorName', {\n        lightness,\n        chroma,\n        hue\n      }).replace(/\\s+/g, ' ').trim();\n    }\n  }\n\n  private getOklchHue(l: number, c: number, h: number, locale: string): [string, number] {\n    let strings = LocalizedStringDictionary.getGlobalDictionaryForPackage('@react-stately/color') || dictionary;\n    if (c < GRAY_THRESHOLD) {\n      return [strings.getStringForLocale('gray', locale), l];\n    }\n\n    for (let i = 0; i < OKLCH_HUES.length; i++) {\n      let [hue, hueName] = OKLCH_HUES[i];\n      let [nextHue, nextHueName] = OKLCH_HUES[i + 1] || [360, 'pink'];\n      if (h >= hue && h < nextHue) {\n        // Split orange hue into brown/orange depending on lightness.\n        if (hueName === 'orange') {\n          if (l < ORANGE_LIGHTNESS_THRESHOLD) {\n            hueName = 'brown';\n          } else {\n            // Adjust lightness.\n            l = (l - ORANGE_LIGHTNESS_THRESHOLD) + MAX_DARK_LIGHTNESS;\n          }\n        }\n\n        // If the hue is at least halfway to the next hue, add the next hue name as well.\n        if (h > hue + (nextHue - hue) / 2 && hueName !== nextHueName) {\n          hueName = `${hueName} ${nextHueName}`;\n        } else if (hueName === 'yellow' && l < YELLOW_GREEN_LIGHTNESS_THRESHOLD) {\n          // Yellow shifts toward green at lower lightnesses.\n          hueName = 'yellow green';\n        }\n\n        let name = strings.getStringForLocale(hueName, locale).toLocaleLowerCase(locale);\n        return [name, l];\n      }\n    }\n\n    throw new Error('Unexpected hue');\n  }\n\n  getHueName(locale: string): string {\n    let [l, c, h] = toOKLCH(this);\n    let [name] = this.getOklchHue(l, c, h, locale);\n    return name;\n  }\n}\n\nclass RGBColor extends Color {\n  constructor(private red: number, private green: number, private blue: number, private alpha: number) {\n    super();\n  }\n\n  static parse(value: string) {\n    let colors: Array<number | undefined> = [];\n    // matching #rgb, #rgba, #rrggbb, #rrggbbaa\n    if (/^#[\\da-f]+$/i.test(value) && [4, 5, 7, 9].includes(value.length)) {\n      const values = (value.length < 6 ? value.replace(/[^#]/gi, '$&$&') : value).slice(1).split('');\n      while (values.length > 0) {\n        colors.push(parseInt(values.splice(0, 2).join(''), 16));\n      }\n      colors[3] = colors[3] !== undefined ? colors[3] / 255 : undefined;\n    }\n\n    // matching rgb(rrr, ggg, bbb), rgba(rrr, ggg, bbb, 0.a)\n    const match = value.match(/^rgba?\\((.*)\\)$/);\n    if (match?.[1]) {\n      colors = match[1].split(',').map(value => Number(value.trim()));\n      colors = colors.map((num, i) => {\n        return clamp(num ?? 0, 0, i < 3 ? 255 : 1);\n      });\n    }\n    if (colors[0] === undefined || colors[1] === undefined || colors[2] === undefined) {\n      return undefined;\n    }\n\n    return colors.length < 3 ? undefined : new RGBColor(colors[0], colors[1], colors[2], colors[3] ?? 1);\n  }\n\n  toString(format: ColorFormat | 'css' = 'css') {\n    switch (format) {\n      case 'hex':\n        return '#' + (this.red.toString(16).padStart(2, '0') + this.green.toString(16).padStart(2, '0') + this.blue.toString(16).padStart(2, '0')).toUpperCase();\n      case 'hexa':\n        return '#' + (this.red.toString(16).padStart(2, '0') + this.green.toString(16).padStart(2, '0') + this.blue.toString(16).padStart(2, '0') + Math.round(this.alpha * 255).toString(16).padStart(2, '0')).toUpperCase();\n      case 'rgb':\n        return `rgb(${this.red}, ${this.green}, ${this.blue})`;\n      case 'css':\n      case 'rgba':\n        return `rgba(${this.red}, ${this.green}, ${this.blue}, ${this.alpha})`;\n      default:\n        return this.toFormat(format).toString(format);\n    }\n  }\n\n  toFormat(format: ColorFormat): IColor {\n    switch (format) {\n      case 'hex':\n      case 'hexa':\n      case 'rgb':\n      case 'rgba':\n        return this;\n      case 'hsb':\n      case 'hsba':\n        return this.toHSB();\n      case 'hsl':\n      case 'hsla':\n        return this.toHSL();\n      default:\n        throw new Error('Unsupported color conversion: rgb -> ' + format);\n    }\n  }\n\n  toHexInt(): number {\n    return this.red << 16 | this.green << 8 | this.blue;\n  }\n\n  /**\n   * Converts an RGB color value to HSB.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#From_RGB.\n   * @returns An HSBColor object.\n   */\n  private toHSB(): IColor {\n    const red = this.red / 255;\n    const green = this.green / 255;\n    const blue = this.blue / 255;\n    const min = Math.min(red, green, blue);\n    const brightness = Math.max(red, green, blue);\n    const chroma = brightness - min;\n    const saturation = brightness === 0 ? 0 : chroma / brightness;\n    let hue = 0; // achromatic\n\n    if (chroma !== 0) {\n      switch (brightness) {\n        case red:\n          hue = (green - blue) / chroma + (green < blue ? 6 : 0);\n          break;\n        case green:\n          hue = (blue - red) / chroma + 2;\n          break;\n        case blue:\n          hue = (red - green) / chroma + 4;\n          break;\n      }\n\n      hue /= 6;\n    }\n\n    return new HSBColor(\n      toFixedNumber(hue * 360, 2),\n      toFixedNumber(saturation * 100, 2),\n      toFixedNumber(brightness * 100, 2),\n      this.alpha\n    );\n  }\n\n  /**\n   * Converts an RGB color value to HSL.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#From_RGB.\n   * @returns An HSLColor object.\n   */\n  private toHSL(): IColor {\n    const red = this.red / 255;\n    const green = this.green / 255;\n    const blue = this.blue / 255;\n    const min = Math.min(red, green, blue);\n    const max = Math.max(red, green, blue);\n    const lightness = (max + min) / 2;\n    const chroma = max - min;\n    let hue: number;\n    let saturation: number;\n\n    if (chroma === 0) {\n      hue = saturation = 0; // achromatic\n    } else {\n      saturation = chroma / (lightness < .5 ? max + min : 2 - max - min);\n\n      switch (max) {\n        case red:\n          hue = (green - blue) / chroma + (green < blue ? 6 : 0);\n          break;\n        case green:\n          hue = (blue - red) / chroma + 2;\n          break;\n        case blue:\n        default:\n          hue = (red - green) / chroma + 4;\n          break;\n      }\n\n      hue /= 6;\n    }\n\n    return new HSLColor(\n      toFixedNumber(hue * 360, 2),\n      toFixedNumber(saturation * 100, 2),\n      toFixedNumber(lightness * 100, 2),\n      this.alpha);\n  }\n\n  clone(): IColor {\n    return new RGBColor(this.red, this.green, this.blue, this.alpha);\n  }\n\n  getChannelRange(channel: ColorChannel): ColorChannelRange {\n    switch (channel) {\n      case 'red':\n      case 'green':\n      case 'blue':\n        return {minValue: 0x0, maxValue: 0xFF, step: 0x1, pageSize: 0x11};\n      case 'alpha':\n        return {minValue: 0, maxValue: 1, step: 0.01, pageSize: 0.1};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  getChannelFormatOptions(channel: ColorChannel): Intl.NumberFormatOptions {\n    switch (channel) {\n      case 'red':\n      case 'green':\n      case 'blue':\n        return {style: 'decimal'};\n      case 'alpha':\n        return {style: 'percent'};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  formatChannelValue(channel: ColorChannel, locale: string) {\n    let options = this.getChannelFormatOptions(channel);\n    let value = this.getChannelValue(channel);\n    return new NumberFormatter(locale, options).format(value);\n  }\n\n  getColorSpace(): ColorSpace {\n    return 'rgb';\n  }\n\n  static colorChannels: [ColorChannel, ColorChannel, ColorChannel] = ['red', 'green', 'blue'];\n  getColorChannels(): [ColorChannel, ColorChannel, ColorChannel] {\n    return RGBColor.colorChannels;\n  }\n}\n\n// X = <negative/positive number with/without decimal places>\n// before/after a comma, 0 or more whitespaces are allowed\n// - hsb(X, X%, X%)\n// - hsba(X, X%, X%, X)\nconst HSB_REGEX = /hsb\\(([-+]?\\d+(?:.\\d+)?\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d+(?:.\\d+)?%)\\)|hsba\\(([-+]?\\d+(?:.\\d+)?\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d(.\\d+)?)\\)/;\n\nclass HSBColor extends Color {\n  constructor(private hue: number, private saturation: number, private brightness: number, private alpha: number) {\n    super();\n  }\n\n  static parse(value: string): HSBColor | void {\n    let m: RegExpMatchArray | null;\n    if ((m = value.match(HSB_REGEX))) {\n      const [h, s, b, a] = (m[1] ?? m[2]).split(',').map(n => Number(n.trim().replace('%', '')));\n      return new HSBColor(normalizeHue(h), clamp(s, 0, 100), clamp(b, 0, 100), clamp(a ?? 1, 0, 1));\n    }\n  }\n\n  toString(format: ColorFormat | 'css' = 'css') {\n    switch (format) {\n      case 'css':\n        return this.toHSL().toString('css');\n      case 'hex':\n        return this.toRGB().toString('hex');\n      case 'hexa':\n        return this.toRGB().toString('hexa');\n      case 'hsb':\n        return `hsb(${this.hue}, ${toFixedNumber(this.saturation, 2)}%, ${toFixedNumber(this.brightness, 2)}%)`;\n      case 'hsba':\n        return `hsba(${this.hue}, ${toFixedNumber(this.saturation, 2)}%, ${toFixedNumber(this.brightness, 2)}%, ${this.alpha})`;\n      default:\n        return this.toFormat(format).toString(format);\n    }\n  }\n\n  toFormat(format: ColorFormat): IColor {\n    switch (format) {\n      case 'hsb':\n      case 'hsba':\n        return this;\n      case 'hsl':\n      case 'hsla':\n        return this.toHSL();\n      case 'rgb':\n      case 'rgba':\n        return this.toRGB();\n      default:\n        throw new Error('Unsupported color conversion: hsb -> ' + format);\n    }\n  }\n\n  /**\n   * Converts a HSB color to HSL.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSV_to_HSL.\n   * @returns An HSLColor object.\n   */\n  private toHSL(): IColor {\n    let saturation = this.saturation / 100;\n    let brightness = this.brightness / 100;\n    let lightness = brightness * (1 - saturation / 2);\n    saturation = lightness === 0 || lightness === 1 ? 0 : (brightness - lightness) / Math.min(lightness, 1 - lightness);\n\n    return new HSLColor(\n      toFixedNumber(this.hue, 2),\n      toFixedNumber(saturation * 100, 2),\n        toFixedNumber(lightness * 100, 2),\n      this.alpha\n    );\n  }\n\n  /**\n   * Converts a HSV color value to RGB.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSV_to_RGB_alternative.\n   * @returns An RGBColor object.\n   */\n  private toRGB(): IColor {\n    let hue = this.hue;\n    let saturation = this.saturation / 100;\n    let brightness = this.brightness / 100;\n    let fn = (n: number, k = (n + hue / 60) % 6) => brightness - saturation * brightness * Math.max(Math.min(k, 4 - k, 1), 0);\n    return new RGBColor(\n      Math.round(fn(5) * 255),\n      Math.round(fn(3) * 255),\n      Math.round(fn(1) * 255),\n      this.alpha\n    );\n  }\n\n  clone(): IColor {\n    return new HSBColor(this.hue, this.saturation, this.brightness, this.alpha);\n  }\n\n  getChannelRange(channel: ColorChannel): ColorChannelRange {\n    switch (channel) {\n      case 'hue':\n        return {minValue: 0, maxValue: 360, step: 1, pageSize: 15};\n      case 'saturation':\n      case 'brightness':\n        return {minValue: 0, maxValue: 100, step: 1, pageSize: 10};\n      case 'alpha':\n        return {minValue: 0, maxValue: 1, step: 0.01, pageSize: 0.1};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  getChannelFormatOptions(channel: ColorChannel): Intl.NumberFormatOptions {\n    switch (channel) {\n      case 'hue':\n        return {style: 'unit', unit: 'degree', unitDisplay: 'narrow'};\n      case 'saturation':\n      case 'brightness':\n      case 'alpha':\n        return {style: 'percent'};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  formatChannelValue(channel: ColorChannel, locale: string) {\n    let options = this.getChannelFormatOptions(channel);\n    let value = this.getChannelValue(channel);\n    if (channel === 'saturation' || channel === 'brightness') {\n      value /= 100;\n    }\n    return new NumberFormatter(locale, options).format(value);\n  }\n\n  getColorSpace(): ColorSpace {\n    return 'hsb';\n  }\n\n  static colorChannels: [ColorChannel, ColorChannel, ColorChannel] = ['hue', 'saturation', 'brightness'];\n  getColorChannels(): [ColorChannel, ColorChannel, ColorChannel] {\n    return HSBColor.colorChannels;\n  }\n}\n\n// X = <negative/positive number with/without decimal places>\n// before/after a comma, 0 or more whitespaces are allowed\n// - hsl(X, X%, X%)\n// - hsla(X, X%, X%, X)\nconst HSL_REGEX = /hsl\\(([-+]?\\d+(?:.\\d+)?\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d+(?:.\\d+)?%)\\)|hsla\\(([-+]?\\d+(?:.\\d+)?\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d+(?:.\\d+)?%\\s*,\\s*[-+]?\\d(.\\d+)?)\\)/;\n\nclass HSLColor extends Color {\n  constructor(private hue: number, private saturation: number, private lightness: number, private alpha: number) {\n    super();\n  }\n\n  static parse(value: string): HSLColor | void {\n    let m: RegExpMatchArray | null;\n    if ((m = value.match(HSL_REGEX))) {\n      const [h, s, l, a] = (m[1] ?? m[2]).split(',').map(n => Number(n.trim().replace('%', '')));\n      return new HSLColor(normalizeHue(h), clamp(s, 0, 100), clamp(l, 0, 100), clamp(a ?? 1, 0, 1));\n    }\n  }\n\n  toString(format: ColorFormat | 'css' = 'css') {\n    switch (format) {\n      case 'hex':\n        return this.toRGB().toString('hex');\n      case 'hexa':\n        return this.toRGB().toString('hexa');\n      case 'hsl':\n        return `hsl(${this.hue}, ${toFixedNumber(this.saturation, 2)}%, ${toFixedNumber(this.lightness, 2)}%)`;\n      case 'css':\n      case 'hsla':\n        return `hsla(${this.hue}, ${toFixedNumber(this.saturation, 2)}%, ${toFixedNumber(this.lightness, 2)}%, ${this.alpha})`;\n      default:\n        return this.toFormat(format).toString(format);\n    }\n  }\n  toFormat(format: ColorFormat): IColor {\n    switch (format) {\n      case 'hsl':\n      case 'hsla':\n        return this;\n      case 'hsb':\n      case 'hsba':\n        return this.toHSB();\n      case 'rgb':\n      case 'rgba':\n        return this.toRGB();\n      default:\n        throw new Error('Unsupported color conversion: hsl -> ' + format);\n    }\n  }\n\n  /**\n   * Converts a HSL color to HSB.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_HSV.\n   * @returns An HSBColor object.\n   */\n  private toHSB(): IColor {\n    let saturation = this.saturation / 100;\n    let lightness = this.lightness / 100;\n    let brightness = lightness + saturation * Math.min(lightness, 1 - lightness);\n    saturation = brightness === 0 ? 0 : 2 * (1 - lightness / brightness);\n    return new HSBColor(\n      toFixedNumber(this.hue, 2),\n      toFixedNumber(saturation * 100, 2),\n      toFixedNumber(brightness * 100, 2),\n      this.alpha\n    );\n  }\n\n  /**\n   * Converts a HSL color to RGB.\n   * Conversion formula adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative.\n   * @returns An RGBColor object.\n   */\n  private toRGB(): IColor {\n    let hue = this.hue;\n    let saturation = this.saturation / 100;\n    let lightness = this.lightness / 100;\n    let a = saturation * Math.min(lightness, 1 - lightness);\n    let fn = (n: number, k = (n + hue / 30) % 12) => lightness - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n    return new RGBColor(\n      Math.round(fn(0) * 255),\n      Math.round(fn(8) * 255),\n      Math.round(fn(4) * 255),\n      this.alpha\n    );\n  }\n\n  clone(): IColor {\n    return new HSLColor(this.hue, this.saturation, this.lightness, this.alpha);\n  }\n\n  getChannelRange(channel: ColorChannel): ColorChannelRange {\n    switch (channel) {\n      case 'hue':\n        return {minValue: 0, maxValue: 360, step: 1, pageSize: 15};\n      case 'saturation':\n      case 'lightness':\n        return {minValue: 0, maxValue: 100, step: 1, pageSize: 10};\n      case 'alpha':\n        return {minValue: 0, maxValue: 1, step: 0.01, pageSize: 0.1};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  getChannelFormatOptions(channel: ColorChannel): Intl.NumberFormatOptions {\n    switch (channel) {\n      case 'hue':\n        return {style: 'unit', unit: 'degree', unitDisplay: 'narrow'};\n      case 'saturation':\n      case 'lightness':\n      case 'alpha':\n        return {style: 'percent'};\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  }\n\n  formatChannelValue(channel: ColorChannel, locale: string) {\n    let options = this.getChannelFormatOptions(channel);\n    let value = this.getChannelValue(channel);\n    if (channel === 'saturation' || channel === 'lightness') {\n      value /= 100;\n    }\n    return new NumberFormatter(locale, options).format(value);\n  }\n\n  getColorSpace(): ColorSpace {\n    return 'hsl';\n  }\n\n  static colorChannels: [ColorChannel, ColorChannel, ColorChannel] = ['hue', 'saturation', 'lightness'];\n  getColorChannels(): [ColorChannel, ColorChannel, ColorChannel] {\n    return HSLColor.colorChannels;\n  }\n}\n\n// https://www.w3.org/TR/css-color-4/#color-conversion-code\nfunction toOKLCH(color: Color) {\n  let rgb = color.toFormat('rgb');\n  let red = rgb.getChannelValue('red') / 255;\n  let green = rgb.getChannelValue('green') / 255;\n  let blue = rgb.getChannelValue('blue') / 255;\n  [red, green, blue] = lin_sRGB(red, green, blue);\n  let [x, y, z] = lin_sRGB_to_XYZ(red, green, blue);\n  let [l, a, b] = XYZ_to_OKLab(x, y, z);\n  return OKLab_to_OKLCH(l, a, b);\n}\n\nfunction OKLab_to_OKLCH(l: number, a: number, b: number): [number, number, number] {\n  var hue = Math.atan2(b, a) * 180 / Math.PI;\n  return [\n    l,\n    Math.sqrt(a ** 2 + b ** 2), // Chroma\n    hue >= 0 ? hue : hue + 360 // Hue, in degrees [0 to 360)\n  ];\n}\n\nfunction lin_sRGB(r: number, g: number, b: number): [number, number, number] {\n  // convert an array of sRGB values\n  // where in-gamut values are in the range [0 - 1]\n  // to linear light (un-companded) form.\n  // https://en.wikipedia.org/wiki/SRGB\n  // Extended transfer function:\n  // for negative values,  linear portion is extended on reflection of axis,\n  // then reflected power function is used.\n  return [lin_sRGB_component(r), lin_sRGB_component(g), lin_sRGB_component(b)];\n}\n\nfunction lin_sRGB_component(val: number) {\n  let sign = val < 0 ? -1 : 1;\n  let abs = Math.abs(val);\n\n  if (abs <= 0.04045) {\n    return val / 12.92;\n  }\n\n  return sign * (Math.pow((abs + 0.055) / 1.055, 2.4));\n}\n\nfunction lin_sRGB_to_XYZ(r: number, g: number, b: number) {\n  // convert an array of linear-light sRGB values to CIE XYZ\n  // using sRGB's own white, D65 (no chromatic adaptation)\n  const M = [\n    506752 / 1228815, 87881 / 245763,  12673 / 70218,\n    87098 / 409605,   175762 / 245763, 12673 / 175545,\n    7918 / 409605,    87881 / 737289,  1001167 / 1053270\n  ];\n  return multiplyMatrix(M, r, g, b);\n}\n\nfunction XYZ_to_OKLab(x: number, y: number, z: number) {\n  // Given XYZ relative to D65, convert to OKLab\n  const XYZtoLMS = [\n    0.8190224379967030, 0.3619062600528904, -0.1288737815209879,\n    0.0329836539323885, 0.9292868615863434,  0.0361446663506424,\n    0.0481771893596242, 0.2642395317527308,  0.6335478284694309\n  ];\n  const LMStoOKLab = [\n    0.2104542683093140,  0.7936177747023054, -0.0040720430116193,\n    1.9779985324311684, -2.4285922420485799,  0.4505937096174110,\n    0.0259040424655478,  0.7827717124575296, -0.8086757549230774\n  ];\n\n  let [a, b, c] = multiplyMatrix(XYZtoLMS, x, y, z);\n  return multiplyMatrix(LMStoOKLab, Math.cbrt(a), Math.cbrt(b), Math.cbrt(c));\n}\n\nfunction multiplyMatrix(m: number[], x: number, y: number, z: number): [number, number, number] {\n  let a = m[0] * x + m[1] * y + m[2] * z;\n  let b = m[3] * x + m[4] * y + m[5] * z;\n  let c = m[6] * x + m[7] * y + m[8] * z;\n  return [a, b, c];\n}\n"], "names": [], "version": 3, "file": "Color.module.js.map"}