/* DRDO IDX v1.7 - Professional Government Design System */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: #1A3C5E;
  background: #FFFFFF;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* DRDO Color Palette */
:root {
  /* Primary Colors */
  --drdo-navy: #1A3C5E;
  --drdo-navy-light: #2A4C6E;
  --drdo-navy-dark: #0F2A3E;

  /* Accent Colors */
  --drdo-saffron: #FF9933;
  --drdo-saffron-light: #FFB366;
  --drdo-saffron-dark: #E8890B;

  /* Status Colors */
  --drdo-success: #059669;
  --drdo-warning: #F59E0B;
  --drdo-error: #DC2626;
  --drdo-info: #4A90E2;

  /* Neutral Colors */
  --drdo-white: #FFFFFF;
  --drdo-gray-50: #F8FAFC;
  --drdo-gray-100: #F1F5F9;
  --drdo-gray-200: #E2E8F0;
  --drdo-gray-300: #CBD5E1;
  --drdo-gray-400: #94A3B8;
  --drdo-gray-500: #64748B;
  --drdo-gray-600: #475569;
  --drdo-gray-700: #334155;
  --drdo-gray-800: #1E293B;
  --drdo-gray-900: #0F172A;

  /* Typography */
  --font-family-primary: 'Inter', 'Roboto', sans-serif;
  --font-family-mono: 'Courier New', monospace;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(26, 60, 94, 0.05);
  --shadow-md: 0 4px 6px rgba(26, 60, 94, 0.1);
  --shadow-lg: 0 8px 16px rgba(26, 60, 94, 0.15);
  --shadow-xl: 0 20px 40px rgba(26, 60, 94, 0.2);
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--drdo-navy);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--drdo-gray-700);
}

/* Professional Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 40px;
}

.btn-primary {
  background: var(--drdo-navy);
  color: white;
  border-color: var(--drdo-navy);
}

.btn-primary:hover {
  background: var(--drdo-navy-light);
  border-color: var(--drdo-navy-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--drdo-saffron);
  color: white;
  border-color: var(--drdo-saffron);
}

.btn-secondary:hover {
  background: var(--drdo-saffron-dark);
  border-color: var(--drdo-saffron-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: transparent;
  color: var(--drdo-navy);
  border-color: var(--drdo-navy);
}

.btn-outline:hover {
  background: var(--drdo-navy);
  color: white;
}

.btn-success {
  background: var(--drdo-success);
  color: white;
  border-color: var(--drdo-success);
}

.btn-warning {
  background: var(--drdo-warning);
  color: white;
  border-color: var(--drdo-warning);
}

.btn-error {
  background: var(--drdo-error);
  color: white;
  border-color: var(--drdo-error);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Professional Form Elements */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--drdo-navy);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--drdo-gray-200);
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--drdo-saffron);
  box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.1);
}

/* Professional Cards */
.card {
  background: white;
  border: 1px solid var(--drdo-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--drdo-gray-200);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--drdo-gray-200);
  background: var(--drdo-gray-50);
}

/* Status Indicators */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-operational {
  background: rgba(5, 150, 105, 0.1);
  color: var(--drdo-success);
}

.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--drdo-warning);
}

.status-error {
  background: rgba(220, 38, 38, 0.1);
  color: var(--drdo-error);
}

.status-maintenance {
  background: rgba(74, 144, 226, 0.1);
  color: var(--drdo-info);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Spacing Utilities */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-sm { font-size: 14px; }
.text-md { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Color Utilities */
.text-navy { color: var(--drdo-navy); }
.text-saffron { color: var(--drdo-saffron); }
.text-success { color: var(--drdo-success); }
.text-warning { color: var(--drdo-warning); }
.text-error { color: var(--drdo-error); }
.text-gray { color: var(--drdo-gray-500); }

.bg-navy { background-color: var(--drdo-navy); }
.bg-saffron { background-color: var(--drdo-saffron); }
.bg-success { background-color: var(--drdo-success); }
.bg-warning { background-color: var(--drdo-warning); }
.bg-error { background-color: var(--drdo-error); }
.bg-gray-50 { background-color: var(--drdo-gray-50); }
.bg-white { background-color: var(--drdo-white); }

/* Professional Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--drdo-gray-200);
}

.table th {
  background: var(--drdo-gray-50);
  font-weight: 600;
  color: var(--drdo-navy);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tr:hover {
  background: var(--drdo-gray-50);
}

/* Loading States */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--drdo-gray-200);
  border-radius: 50%;
  border-top-color: var(--drdo-saffron);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.25rem; }

  .grid-cols-4,
  .grid-cols-3,
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-md);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid var(--drdo-gray-300);
  }

  .btn {
    border: 1px solid var(--drdo-gray-300);
  }
}
