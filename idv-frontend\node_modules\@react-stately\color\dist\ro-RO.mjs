var $dc46bea2f2597899$exports = {};
$dc46bea2f2597899$exports = {
    "alpha": `Alfa`,
    "black": `negru`,
    "blue": `<PERSON><PERSON>ru`,
    "blue purple": `albastru-violet`,
    "brightness": `Luminozitate`,
    "brown": `maro`,
    "brown yellow": `galben maro`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `bleu`,
    "cyan blue": `albastru-bleu`,
    "dark": `\xeenchis`,
    "gray": `gri`,
    "grayish": `cenu\u{219}iu`,
    "green": `Verde`,
    "green cyan": `verde bleu`,
    "hue": `Nuan\u{21B}\u{103}`,
    "light": `deschis`,
    "lightness": `Luminozitate`,
    "magenta": `fucsia`,
    "magenta pink": `roz-fucsia`,
    "orange": `portocaliu`,
    "orange yellow": `galben-portocaliu`,
    "pale": `pal`,
    "pink": `roz`,
    "pink red": `roz-ro\u{219}u`,
    "purple": `violet`,
    "purple magenta": `violet-fucsia`,
    "red": `Ro\u{219}u`,
    "red orange": `portocaliu-ro\u{219}u`,
    "saturation": `Satura\u{21B}ie`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} transparent`,
    "very dark": `foarte \xeenchis`,
    "very light": `foarte deschis`,
    "vibrant": `plin de via\u{21B}\u{103}`,
    "white": `alb`,
    "yellow": `galben`,
    "yellow green": `galben-verde`
};


export {$dc46bea2f2597899$exports as default};
//# sourceMappingURL=ro-RO.module.js.map
