{"mappings": ";AAAA,4BAAiB;IAAG,SAAS,CAAC,mCAAK,CAAC;IAClC,SAAS,CAAC,0CAAM,CAAC;IACjB,QAAQ,CAAC,mCAAK,CAAC;IACf,eAAe,CAAC,mGAAe,CAAC;IAChC,cAAc,CAAC,iDAAO,CAAC;IACvB,SAAS,CAAC,sEAAU,CAAC;IACrB,gBAAgB,CAAC,0GAAgB,CAAC;IAClC,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;IACrE,QAAQ,CAAC,iDAAO,CAAC;IACjB,aAAa,CAAC,kHAAkB,CAAC;IACjC,QAAQ,CAAC,0CAAM,CAAC;IAChB,QAAQ,CAAC,mCAAK,CAAC;IACf,WAAW,CAAC,+DAAS,CAAC;IACtB,SAAS,CAAC,iDAAO,CAAC;IAClB,cAAc,CAAC,8EAAY,CAAC;IAC5B,OAAO,CAAC,iDAAO,CAAC;IAChB,SAAS,CAAC,iDAAO,CAAC;IAClB,aAAa,CAAC,oFAAY,CAAC;IAC3B,WAAW,CAAC,+DAAS,CAAC;IACtB,gBAAgB,CAAC,0GAAgB,CAAC;IAClC,UAAU,CAAC,+DAAS,CAAC;IACrB,iBAAiB,CAAC,mGAAe,CAAC;IAClC,QAAQ,CAAC,iDAAO,CAAC;IACjB,QAAQ,CAAC,iDAAO,CAAC;IACjB,YAAY,CAAC,4FAAc,CAAC;IAC5B,UAAU,CAAC,sEAAU,CAAC;IACtB,kBAAkB,CAAC,+HAAmB,CAAC;IACvC,OAAO,CAAC,iDAAO,CAAC;IAChB,cAAc,CAAC,0GAAgB,CAAC;IAChC,cAAc,CAAC,oFAAY,CAAC;IAC5B,wBAAwB,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,wFAAgB,EAAE,KAAK,kBAAkB,EAAE;IAC1H,aAAa,CAAC,8EAAY,CAAC;IAC3B,cAAc,CAAC,qFAAa,CAAC;IAC7B,WAAW,CAAC,mCAAK,CAAC;IAClB,SAAS,CAAC,mCAAK,CAAC;IAChB,UAAU,CAAC,0CAAM,CAAC;IAClB,gBAAgB,CAAC,qFAAa,CAAC;AACjC", "sources": ["packages/@react-stately/color/intl/ru-RU.json"], "sourcesContent": ["{\n  \"alpha\": \"Альфа\",\n  \"black\": \"черный\",\n  \"blue\": \"Синий\",\n  \"blue purple\": \"сине-фиолетовый\",\n  \"brightness\": \"Яркость\",\n  \"brown\": \"коричневый\",\n  \"brown yellow\": \"коричнево-желтый\",\n  \"colorName\": \"{lightness} {chroma} {hue}\",\n  \"cyan\": \"голубой\",\n  \"cyan blue\": \"цвет морской волны\",\n  \"dark\": \"темный\",\n  \"gray\": \"серый\",\n  \"grayish\": \"сероватый\",\n  \"green\": \"Зеленый\",\n  \"green cyan\": \"сине-зеленый\",\n  \"hue\": \"Оттенок\",\n  \"light\": \"светлый\",\n  \"lightness\": \"Освещенность\",\n  \"magenta\": \"пурпурный\",\n  \"magenta pink\": \"пурпурно-розовый\",\n  \"orange\": \"оранжевый\",\n  \"orange yellow\": \"оранжево-желтый\",\n  \"pale\": \"бледный\",\n  \"pink\": \"розовый\",\n  \"pink red\": \"розово-красный\",\n  \"purple\": \"фиолетовый\",\n  \"purple magenta\": \"фиолетово-пурпурный\",\n  \"red\": \"Красный\",\n  \"red orange\": \"красно-оранжевый\",\n  \"saturation\": \"Насыщенность\",\n  \"transparentColorName\": \"{lightness} {chroma} {hue}, прозрачный на {percentTransparent}\",\n  \"very dark\": \"очень темный\",\n  \"very light\": \"очень светлый\",\n  \"vibrant\": \"яркий\",\n  \"white\": \"белый\",\n  \"yellow\": \"желтый\",\n  \"yellow green\": \"желто-зеленый\"\n}\n"], "names": [], "version": 3, "file": "ru-RU.module.js.map"}