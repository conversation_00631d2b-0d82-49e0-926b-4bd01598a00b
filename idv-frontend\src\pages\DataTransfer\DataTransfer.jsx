import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import { encryptData } from '../../utils/encryption'
import './DataTransfer.css'

const DataTransfer = () => {
  const { user, hasPermission } = useAuth()
  const [selectedFiles, setSelectedFiles] = useState([])
  const [encryptionLevel, setEncryptionLevel] = useState('STANDARD')
  const [uploadProgress, setUploadProgress] = useState({})
  const [transferHistory, setTransferHistory] = useState([])
  const [dragActive, setDragActive] = useState(false)
  const [previewData, setPreviewData] = useState(null)

  const encryptionLevels = [
    { value: 'BASIC', label: 'Basic (AES-128)', description: 'Standard encryption for general data' },
    { value: 'STANDARD', label: 'Standard (AES-256)', description: 'Enhanced encryption for sensitive data' },
    { value: 'ADVANCED', label: 'Advanced (AES-256 + RSA)', description: 'Military-grade encryption for classified data' },
    { value: 'QUANTUM', label: 'Quantum-Safe', description: 'Future-proof encryption for top secret data' }
  ]

  useEffect(() => {
    // Mock transfer history
    const mockHistory = [
      {
        id: '1',
        filename: 'mission_data_2025.json',
        size: '2.4 MB',
        encryption: 'ADVANCED',
        status: 'completed',
        timestamp: '2025-01-08T10:30:00Z',
        recipient: 'Strategic Command'
      },
      {
        id: '2',
        filename: 'equipment_status.csv',
        size: '1.8 MB',
        encryption: 'STANDARD',
        status: 'completed',
        timestamp: '2025-01-08T09:15:00Z',
        recipient: 'Engineering Division'
      },
      {
        id: '3',
        filename: 'classified_report.pdf',
        size: '5.2 MB',
        encryption: 'QUANTUM',
        status: 'in_progress',
        timestamp: '2025-01-08T11:00:00Z',
        recipient: 'Intelligence Bureau'
      }
    ]
    
    setTransferHistory(mockHistory)
    logAuditEvent('DATA_TRANSFER_VIEW', 'Data transfer page accessed', 'DATA_TRANSFER', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  const handleDrag = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files))
    }
  }

  const handleFileSelect = (e) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files))
    }
  }

  const handleFiles = (files) => {
    const validFiles = files.filter(file => {
      // Validate file types and sizes
      const maxSize = 100 * 1024 * 1024 // 100MB
      const allowedTypes = ['.json', '.csv', '.pdf', '.txt', '.xml', '.xlsx']
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
      
      return file.size <= maxSize && allowedTypes.includes(fileExtension)
    })

    setSelectedFiles(prev => [...prev, ...validFiles])
    
    // Generate preview for first file
    if (validFiles.length > 0) {
      generatePreview(validFiles[0])
    }
  }

  const generatePreview = (file) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target.result
      let preview = ''
      
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        try {
          const jsonData = JSON.parse(content)
          preview = JSON.stringify(jsonData, null, 2).substring(0, 500) + '...'
        } catch {
          preview = content.substring(0, 500) + '...'
        }
      } else {
        preview = content.substring(0, 500) + '...'
      }
      
      setPreviewData({
        filename: file.name,
        size: formatFileSize(file.size),
        type: file.type || 'Unknown',
        preview: preview
      })
    }
    reader.readAsText(file)
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
    if (index === 0 && selectedFiles.length > 1) {
      generatePreview(selectedFiles[1])
    } else if (selectedFiles.length === 1) {
      setPreviewData(null)
    }
  }

  const startTransfer = () => {
    if (selectedFiles.length === 0) {
      alert('Please select files to transfer')
      return
    }

    selectedFiles.forEach((file, index) => {
      // Simulate upload progress
      setUploadProgress(prev => ({ ...prev, [index]: 0 }))
      
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[index] || 0
          const newProgress = Math.min(currentProgress + Math.random() * 20, 100)
          
          if (newProgress >= 100) {
            clearInterval(interval)
            
            // Add to transfer history
            const newTransfer = {
              id: Date.now().toString(),
              filename: file.name,
              size: formatFileSize(file.size),
              encryption: encryptionLevel,
              status: 'completed',
              timestamp: new Date().toISOString(),
              recipient: 'DRDO Network'
            }
            
            setTransferHistory(prev => [newTransfer, ...prev])
            
            // Log the transfer
            logAuditEvent('FILE_TRANSFER', `File transferred: ${file.name} with ${encryptionLevel} encryption`, 'DATA_TRANSFER', 'CONFIDENTIAL', user?.id, user?.name)
          }
          
          return { ...prev, [index]: newProgress }
        })
      }, 200)
    })
  }

  const getStatusColor = (status) => {
    const colors = {
      completed: '#059669',
      in_progress: '#F59E0B',
      failed: '#DC2626',
      pending: '#64748B'
    }
    return colors[status] || '#64748B'
  }

  const getEncryptionColor = (level) => {
    const colors = {
      BASIC: '#64748B',
      STANDARD: '#3B82F6',
      ADVANCED: '#F59E0B',
      QUANTUM: '#7C3AED'
    }
    return colors[level] || '#64748B'
  }

  if (!hasPermission('engineer')) {
    return (
      <div className="data-transfer-page">
        <div className="access-denied">
          <h2>🔒 Access Denied</h2>
          <p>You need engineer privileges or higher to access data transfer.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="data-transfer-page">
      <div className="transfer-header">
        <h1 className="transfer-title">📤 Secure Data Transfer</h1>
        <p className="transfer-subtitle">Upload and transfer files with military-grade encryption</p>
      </div>

      <div className="transfer-content">
        {/* File Upload Section */}
        <div className="upload-section">
          <h2 className="section-title">📁 File Upload</h2>
          
          <div 
            className={`upload-zone ${dragActive ? 'drag-active' : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="upload-content">
              <div className="upload-icon">📁</div>
              <h3>Drag & Drop Files Here</h3>
              <p>or click to browse files</p>
              <input
                type="file"
                multiple
                onChange={handleFileSelect}
                className="file-input"
                accept=".json,.csv,.pdf,.txt,.xml,.xlsx"
              />
              <div className="upload-info">
                <p>Supported formats: JSON, CSV, PDF, TXT, XML, XLSX</p>
                <p>Maximum file size: 100MB</p>
              </div>
            </div>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="selected-files">
              <h3>Selected Files ({selectedFiles.length})</h3>
              <div className="files-list">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="file-item">
                    <div className="file-info">
                      <span className="file-name">{file.name}</span>
                      <span className="file-size">{formatFileSize(file.size)}</span>
                    </div>
                    <div className="file-actions">
                      {uploadProgress[index] !== undefined && (
                        <div className="progress-bar">
                          <div 
                            className="progress-fill" 
                            style={{ width: `${uploadProgress[index]}%` }}
                          ></div>
                        </div>
                      )}
                      <button 
                        onClick={() => removeFile(index)}
                        className="remove-file"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Encryption Settings */}
        <div className="encryption-section">
          <h2 className="section-title">🔐 Encryption Settings</h2>
          <div className="encryption-options">
            {encryptionLevels.map(level => (
              <div 
                key={level.value}
                className={`encryption-option ${encryptionLevel === level.value ? 'selected' : ''}`}
                onClick={() => setEncryptionLevel(level.value)}
              >
                <div className="option-header">
                  <span 
                    className="encryption-badge"
                    style={{ backgroundColor: getEncryptionColor(level.value) }}
                  >
                    {level.label}
                  </span>
                </div>
                <p className="option-description">{level.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Data Preview */}
        {previewData && (
          <div className="preview-section">
            <h2 className="section-title">👁️ Data Preview</h2>
            <div className="preview-card">
              <div className="preview-header">
                <h3>{previewData.filename}</h3>
                <div className="preview-meta">
                  <span>Size: {previewData.size}</span>
                  <span>Type: {previewData.type}</span>
                </div>
              </div>
              <div className="preview-content">
                <pre>{previewData.preview}</pre>
              </div>
            </div>
          </div>
        )}

        {/* Transfer Controls */}
        <div className="transfer-controls">
          <button 
            onClick={startTransfer}
            className="btn-transfer"
            disabled={selectedFiles.length === 0}
          >
            🚀 Start Secure Transfer
          </button>
        </div>

        {/* Transfer History */}
        <div className="history-section">
          <h2 className="section-title">📋 Transfer History</h2>
          <div className="history-list">
            {transferHistory.map(transfer => (
              <div key={transfer.id} className="history-item">
                <div className="history-info">
                  <h4 className="history-filename">{transfer.filename}</h4>
                  <div className="history-meta">
                    <span>Size: {transfer.size}</span>
                    <span>To: {transfer.recipient}</span>
                    <span>{new Date(transfer.timestamp).toLocaleString()}</span>
                  </div>
                </div>
                <div className="history-badges">
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(transfer.status) }}
                  >
                    {transfer.status.replace('_', ' ').toUpperCase()}
                  </span>
                  <span 
                    className="encryption-badge"
                    style={{ backgroundColor: getEncryptionColor(transfer.encryption) }}
                  >
                    {transfer.encryption}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Transfer Footer */}
      <div className="transfer-footer">
        <div className="footer-info">
          <span>Total Transfers: {transferHistory.length}</span>
          <span>Current User: {user?.name}</span>
          <span>Security Level: {user?.clearanceLevel}</span>
        </div>
      </div>
    </div>
  )
}

export default DataTransfer
