/* Modern Equipment Status Dashboard */
.advanced-equipment-status {
  background: #F5F7FA;
  min-height: 100vh;
  padding: 20px;
  font-family: 'Inter', 'Roboto', sans-serif;
}

/* Header Section */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header-left {
  flex: 1;
}

.module-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1A3C5E;
  letter-spacing: -0.5px;
}

.module-subtitle {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
  font-weight: 400;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-select {
  padding: 8px 16px;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  background: #FFFFFF;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-select:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.time-tabs {
  display: flex;
  gap: 4px;
  background: #F3F4F6;
  padding: 4px;
  border-radius: 8px;
}

.time-tab {
  padding: 6px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-tab.active {
  background: #FFFFFF;
  color: #4A90E2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  background: #4A90E2;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-btn:hover {
  background: #3A7BC8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.metric-card.primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #FFFFFF;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon {
  font-size: 20px;
}

.metric-title {
  font-size: 14px;
  font-weight: 600;
  color: inherit;
  opacity: 0.9;
}

.info-icon {
  font-size: 14px;
  opacity: 0.6;
  cursor: pointer;
}

.metric-value {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 8px;
  color: inherit;
}

.metric-comparison {
  font-size: 14px;
  color: inherit;
  opacity: 0.8;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-change {
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.metric-change.positive {
  background: rgba(5, 150, 105, 0.2);
  color: #059669;
}

.metric-change.negative {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.metric-change.neutral {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.metric-card.primary .metric-change.positive {
  background: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
}

.progress-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #FFFFFF;
  border-radius: 3px;
  animation: progressAnimation 2s ease-out;
}

@keyframes progressAnimation {
  from { width: 0; }
  to { width: 100%; }
}

.metric-mini-chart {
  height: 40px;
  margin-top: 12px;
}

.maintenance-timeline {
  margin-top: 12px;
}

.timeline-bar {
  height: 6px;
  background: #E5E7EB;
  border-radius: 3px;
  overflow: hidden;
}

.timeline-progress {
  height: 100%;
  background: linear-gradient(90deg, #059669 0%, #F5A623 100%);
  border-radius: 3px;
  animation: progressAnimation 2s ease-out;
}

/* Content Layout */
.content-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.chart-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.chart-card {
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #E5E7EB;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
}

.chart-tabs {
  display: flex;
  gap: 4px;
  background: #F3F4F6;
  padding: 4px;
  border-radius: 8px;
}

.chart-tab {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-tab.active {
  background: #FFFFFF;
  color: #4A90E2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-content {
  padding: 24px;
}

/* Stats Section */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.radar-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.radar-card h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
}

.stats-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1A3C5E;
  margin-bottom: 4px;
}

.stat-period {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

.stat-bars {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-bar {
  height: 8px;
  border-radius: 4px;
  font-size: 10px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-weight: 500;
}

/* Data Table Section */
.data-table-section {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #E5E7EB;
}

.table-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
}

.table-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  background: #FFFFFF;
  font-size: 12px;
  color: #374151;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #4A90E2;
  border-radius: 6px;
  background: #4A90E2;
  color: #FFFFFF;
  font-size: 12px;
  cursor: pointer;
}

.data-table {
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  padding: 16px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #E5E7EB;
  background: #F9FAFB;
}

.data-table td {
  padding: 16px 24px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #F3F4F6;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.operational {
  background: #059669;
}

.status-dot.warning {
  background: #F5A623;
}

.status-dot.critical {
  background: #ef4444;
}

.criticality {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.criticality.high {
  background: rgba(74, 144, 226, 0.1);
  color: #4A90E2;
}

.criticality.medium {
  background: rgba(245, 166, 35, 0.1);
  color: #F5A623;
}

.criticality.critical {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  background: transparent;
  color: #4A90E2;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(74, 144, 226, 0.1);
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #E5E7EB;
  background: #F9FAFB;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.pagination-controls button {
  padding: 6px 12px;
  border: 1px solid #D1D5DB;
  background: #FFFFFF;
  color: #374151;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-controls button.active {
  background: #4A90E2;
  color: #FFFFFF;
  border-color: #4A90E2;
}

.pagination-controls button:hover:not(.active) {
  background: #F3F4F6;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .content-layout {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-filters {
    justify-content: space-between;
  }
  
  .table-pagination {
    flex-direction: column;
    gap: 12px;
  }
}
