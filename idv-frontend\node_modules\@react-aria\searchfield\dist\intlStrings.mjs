import $03K6r$arAEmodulejs from "./ar-AE.mjs";
import $03K6r$bgBGmodulejs from "./bg-BG.mjs";
import $03K6r$csCZmodulejs from "./cs-CZ.mjs";
import $03K6r$daDKmodulejs from "./da-DK.mjs";
import $03K6r$deDEmodulejs from "./de-DE.mjs";
import $03K6r$elGRmodulejs from "./el-GR.mjs";
import $03K6r$enUSmodulejs from "./en-US.mjs";
import $03K6r$esESmodulejs from "./es-ES.mjs";
import $03K6r$etEEmodulejs from "./et-EE.mjs";
import $03K6r$fiFImodulejs from "./fi-FI.mjs";
import $03K6r$frFRmodulejs from "./fr-FR.mjs";
import $03K6r$heILmodulejs from "./he-IL.mjs";
import $03K6r$hrHRmodulejs from "./hr-HR.mjs";
import $03K6r$huHUmodulejs from "./hu-HU.mjs";
import $03K6r$itITmodulejs from "./it-IT.mjs";
import $03K6r$jaJPmodulejs from "./ja-JP.mjs";
import $03K6r$koKRmodulejs from "./ko-KR.mjs";
import $03K6r$ltLTmodulejs from "./lt-LT.mjs";
import $03K6r$lvLVmodulejs from "./lv-LV.mjs";
import $03K6r$nbNOmodulejs from "./nb-NO.mjs";
import $03K6r$nlNLmodulejs from "./nl-NL.mjs";
import $03K6r$plPLmodulejs from "./pl-PL.mjs";
import $03K6r$ptBRmodulejs from "./pt-BR.mjs";
import $03K6r$ptPTmodulejs from "./pt-PT.mjs";
import $03K6r$roROmodulejs from "./ro-RO.mjs";
import $03K6r$ruRUmodulejs from "./ru-RU.mjs";
import $03K6r$skSKmodulejs from "./sk-SK.mjs";
import $03K6r$slSImodulejs from "./sl-SI.mjs";
import $03K6r$srSPmodulejs from "./sr-SP.mjs";
import $03K6r$svSEmodulejs from "./sv-SE.mjs";
import $03K6r$trTRmodulejs from "./tr-TR.mjs";
import $03K6r$ukUAmodulejs from "./uk-UA.mjs";
import $03K6r$zhCNmodulejs from "./zh-CN.mjs";
import $03K6r$zhTWmodulejs from "./zh-TW.mjs";

var $8112f8b883c0272d$exports = {};


































$8112f8b883c0272d$exports = {
    "ar-AE": $03K6r$arAEmodulejs,
    "bg-BG": $03K6r$bgBGmodulejs,
    "cs-CZ": $03K6r$csCZmodulejs,
    "da-DK": $03K6r$daDKmodulejs,
    "de-DE": $03K6r$deDEmodulejs,
    "el-GR": $03K6r$elGRmodulejs,
    "en-US": $03K6r$enUSmodulejs,
    "es-ES": $03K6r$esESmodulejs,
    "et-EE": $03K6r$etEEmodulejs,
    "fi-FI": $03K6r$fiFImodulejs,
    "fr-FR": $03K6r$frFRmodulejs,
    "he-IL": $03K6r$heILmodulejs,
    "hr-HR": $03K6r$hrHRmodulejs,
    "hu-HU": $03K6r$huHUmodulejs,
    "it-IT": $03K6r$itITmodulejs,
    "ja-JP": $03K6r$jaJPmodulejs,
    "ko-KR": $03K6r$koKRmodulejs,
    "lt-LT": $03K6r$ltLTmodulejs,
    "lv-LV": $03K6r$lvLVmodulejs,
    "nb-NO": $03K6r$nbNOmodulejs,
    "nl-NL": $03K6r$nlNLmodulejs,
    "pl-PL": $03K6r$plPLmodulejs,
    "pt-BR": $03K6r$ptBRmodulejs,
    "pt-PT": $03K6r$ptPTmodulejs,
    "ro-RO": $03K6r$roROmodulejs,
    "ru-RU": $03K6r$ruRUmodulejs,
    "sk-SK": $03K6r$skSKmodulejs,
    "sl-SI": $03K6r$slSImodulejs,
    "sr-SP": $03K6r$srSPmodulejs,
    "sv-SE": $03K6r$svSEmodulejs,
    "tr-TR": $03K6r$trTRmodulejs,
    "uk-UA": $03K6r$ukUAmodulejs,
    "zh-CN": $03K6r$zhCNmodulejs,
    "zh-TW": $03K6r$zhTWmodulejs
};


export {$8112f8b883c0272d$exports as default};
//# sourceMappingURL=intlStrings.module.js.map
