{"mappings": "AAAA,iBAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;IACjC,SAAS,CAAC,KAAK,CAAC;IAChB,QAAQ,CAAC,IAAI,CAAC;IACd,eAAe,CAAC,YAAY,CAAC;IAC7B,cAAc,CAAC,MAAM,CAAC;IACtB,SAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,gBAAgB,CAAC;IAClC,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;IACrE,QAAQ,CAAC,KAAK,CAAC;IACf,aAAa,CAAC,UAAU,CAAC;IACzB,QAAQ,CAAC,MAAM,CAAC;IAChB,QAAQ,CAAC,KAAK,CAAC;IACf,WAAW,CAAC,WAAW,CAAC;IACxB,SAAS,CAAC,KAAK,CAAC;IAChB,cAAc,CAAC,WAAW,CAAC;IAC3B,OAAO,CAAC,KAAK,CAAC;IACd,SAAS,CAAC,KAAK,CAAC;IAChB,aAAa,CAAC,YAAY,CAAC;IAC3B,WAAW,CAAC,OAAO,CAAC;IACpB,gBAAgB,CAAC,YAAY,CAAC;IAC9B,UAAU,CAAC,OAAO,CAAC;IACnB,iBAAiB,CAAC,kBAAkB,CAAC;IACrC,QAAQ,CAAC,SAAM,CAAC;IAChB,QAAQ,CAAC,IAAI,CAAC;IACd,YAAY,CAAC,gBAAgB,CAAC;IAC9B,UAAU,CAAC,IAAI,CAAC;IAChB,kBAAkB,CAAC,YAAY,CAAC;IAChC,OAAO,CAAC,QAAQ,CAAC;IACjB,cAAc,CAAC,mBAAmB,CAAC;IACnC,cAAc,CAAC,eAAS,CAAC;IACzB,wBAAwB,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,kBAAkB,CAAC,aAAa,CAAC;IACzH,aAAa,CAAC,YAAY,CAAC;IAC3B,cAAc,CAAC,WAAW,CAAC;IAC3B,WAAW,CAAC,QAAQ,CAAC;IACrB,SAAS,CAAC,MAAM,CAAC;IACjB,UAAU,CAAC,OAAO,CAAC;IACnB,gBAAgB,CAAC,eAAe,CAAC;AACnC", "sources": ["packages/@react-stately/color/intl/pt-BR.json"], "sourcesContent": ["{\n  \"alpha\": \"Alfa\",\n  \"black\": \"preto\",\n  \"blue\": \"Azul\",\n  \"blue purple\": \"roxo azulado\",\n  \"brightness\": \"<PERSON><PERSON>ho\",\n  \"brown\": \"marrom\",\n  \"brown yellow\": \"marrom amarelado\",\n  \"colorName\": \"{lightness} {chroma} {hue}\",\n  \"cyan\": \"ciano\",\n  \"cyan blue\": \"azul-ciano\",\n  \"dark\": \"escuro\",\n  \"gray\": \"cinza\",\n  \"grayish\": \"acinzentado\",\n  \"green\": \"Verde\",\n  \"green cyan\": \"verde-ciano\",\n  \"hue\": \"Matiz\",\n  \"light\": \"claro\",\n  \"lightness\": \"Luminosidade\",\n  \"magenta\": \"magenta\",\n  \"magenta pink\": \"rosa-magenta\",\n  \"orange\": \"laranja\",\n  \"orange yellow\": \"amarelo alaranjado\",\n  \"pale\": \"pálido\",\n  \"pink\": \"rosa\",\n  \"pink red\": \"rosa avermelhado\",\n  \"purple\": \"roxo\",\n  \"purple magenta\": \"roxo-magenta\",\n  \"red\": \"Vermel<PERSON>\",\n  \"red orange\": \"laranja avermelhado\",\n  \"saturation\": \"Saturação\",\n  \"transparentColorName\": \"{lightness} {chroma} {hue}, {percentTransparent} transparente\",\n  \"very dark\": \"muito escuro\",\n  \"very light\": \"muito claro\",\n  \"vibrant\": \"vibrante\",\n  \"white\": \"branco\",\n  \"yellow\": \"amarelo\",\n  \"yellow green\": \"verde amarelado\"\n}\n"], "names": [], "version": 3, "file": "pt-BR.main.js.map"}