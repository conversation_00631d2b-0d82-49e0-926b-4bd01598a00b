import DefenseEncryption from './encryption';

interface AuditEvent {
  userId: string;
  action: string;
  resource: string;
  timestamp: string;
  securityLevel: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';
}

class AuditLogger {
  log(event: AuditEvent): void {
    const encryptedLog = DefenseEncryption.encrypt(event, 'SECRET');
    try {
      localStorage.setItem(`audit_${event.timestamp}`, encryptedLog);
    } catch (error) {
      console.error('Failed to write audit log to localStorage:', error);
    }
  }
}

export default new AuditLogger();
