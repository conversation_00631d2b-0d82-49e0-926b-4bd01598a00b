import React, { useState } from 'react'
// import { useAuth } from '../../context/AuthContext'
// import { useTheme } from '../../context/ThemeContext'
// import { i18n, t } from '../../utils/i18n'
// import { logAuditEvent } from '../../utils/audit'
// import LoginForm from '../Auth/LoginForm'
import './Header-v15.css'

const Header = () => {
  // const { user, logout, sessionTimeLeft } = useAuth()
  // const { theme, setTheme } = useTheme()
  const [searchQuery, setSearchQuery] = useState('')
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showLanguageMenu, setShowLanguageMenu] = useState(false)
  const [showLoginForm, setShowLoginForm] = useState(false)

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // logAuditEvent('SEARCH_PERFORM', `Search query: ${searchQuery}`, 'HEADER')
      console.log('Search:', searchQuery)
    }
  }

  return (
    <header className="drdo-header">
      <div className="header-top">
        <div className="header-brand">
          <div className="drdo-logo">
            🇮🇳
          </div>
          <div className="brand-text">
            <h1 className="org-title">Defence Research and Development Organisation</h1>
            <div className="system-info">
              <span className="system-name">IDX | Interactive Dashboard</span>
              <span className="version">v1.5</span>
            </div>
          </div>
        </div>

        <div className="header-search">
          <form onSubmit={handleSearch} className="search-form">
            <input
              type="text"
              placeholder="Search systems, data, reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <button type="submit" className="search-btn">
              🔍
            </button>
          </form>
        </div>

        <div className="header-controls">
          <div className="control-item">
            <button
              className="login-btn-header"
              onClick={() => setShowLoginForm(true)}
            >
              🔐 Secure Login
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
