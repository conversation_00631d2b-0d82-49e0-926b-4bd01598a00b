import React, { useState } from 'react'
import { useAuth } from '../../context/AuthContext'
import { useTheme } from '../../context/ThemeContext'
import { logAuditEvent } from '../../utils/audit'
import './Header-v17.css'

const Header = () => {
  const { user, logout, sessionTimeLeft, isDevelopmentMode } = useAuth()
  const { theme, setTheme, themeConfig } = useTheme()
  const [searchQuery, setSearchQuery] = useState('')
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showThemeMenu, setShowThemeMenu] = useState(false)

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      logAuditEvent('SEARCH_PERFORM', `Search query: ${searchQuery}`, 'HEADER', 'PUBLIC', user?.id, user?.name)
      console.log('Search:', searchQuery)
    }
  }

  const formatSessionTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <header className="header-v17">
      <div className="header-container">
        <div className="header-left">
          <div className="drdo-brand">
            <div className="drdo-logo">🇮🇳</div>
            <div className="brand-text">
              <h1 className="brand-title">DRDO IDX</h1>
              <span className="brand-subtitle">Defence Command System</span>
            </div>
          </div>
        </div>

        <div className="header-center">
          <form onSubmit={handleSearch} className="search-form">
            <input
              type="text"
              placeholder="Search systems, data, reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <button type="submit" className="search-btn">
              🔍
            </button>
          </form>
        </div>

        <div className="header-right">
          {user ? (
            <>
              <div className="status-indicators">
                <div className="status-item">
                  <span className="status-label">Security</span>
                  <span className="status-value secure">{user.clearanceLevel}</span>
                </div>
                <div className="status-item">
                  <span className="status-label">Session</span>
                  <span className="status-value">{formatSessionTime(sessionTimeLeft)}</span>
                </div>
              </div>

              <div className="header-controls">
                <button
                  className="theme-toggle"
                  onClick={() => setShowThemeMenu(!showThemeMenu)}
                >
                  🎨
                </button>

                {showThemeMenu && (
                  <div className="theme-menu">
                    {Object.entries(themeConfig).map(([key, config]) => (
                      <button
                        key={key}
                        onClick={() => {
                          setTheme(key)
                          setShowThemeMenu(false)
                        }}
                        className={`theme-option ${theme === key ? 'active' : ''}`}
                      >
                        {config.name}
                      </button>
                    ))}
                  </div>
                )}

                <div className="user-menu-container">
                  <button
                    className="user-menu-trigger"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                  >
                    <span className="user-avatar">{user.avatar}</span>
                    <span className="user-name">{user.name}</span>
                    <span className="user-role">{user.role}</span>
                  </button>

                  {showUserMenu && (
                    <div className="user-menu">
                      <div className="user-info">
                        <div className="user-details">
                          <span className="user-full-name">{user.name}</span>
                          <span className="user-email">{user.email}</span>
                          <span className="user-department">{user.department}</span>
                        </div>
                      </div>
                      <div className="menu-divider"></div>
                      <button className="menu-item" onClick={() => setShowUserMenu(false)}>
                        👤 Profile
                      </button>
                      <button className="menu-item" onClick={() => setShowUserMenu(false)}>
                        ⚙️ Settings
                      </button>
                      <div className="menu-divider"></div>
                      <button className="menu-item logout" onClick={logout}>
                        🚪 Logout
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="login-prompt">
              <span>Please login to access the system</span>
            </div>
          )}

          {isDevelopmentMode && (
            <div className="dev-badge">DEV</div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header
