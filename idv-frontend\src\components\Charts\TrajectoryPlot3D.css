.trajectory-plot-3d {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.plot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.plot-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.plot-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.missile-selector {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  background-color: white;
  cursor: pointer;
}

.missile-selector:focus {
  outline: none;
  border-color: #007bff;
}

.view-controls {
  display: flex;
  gap: 5px;
}

.view-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background-color: #e9ecef;
}

.view-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.plot-container {
  position: relative;
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.plot-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.info-label {
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* Plotly.js customizations */
.trajectory-plot-3d .plotly {
  border-radius: 4px;
}

.trajectory-plot-3d .modebar {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.trajectory-plot-3d .modebar-btn {
  color: #666 !important;
}

.trajectory-plot-3d .modebar-btn:hover {
  background-color: #e9ecef !important;
}

@media (max-width: 768px) {
  .plot-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .plot-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    width: 100%;
  }
  
  .view-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
}
