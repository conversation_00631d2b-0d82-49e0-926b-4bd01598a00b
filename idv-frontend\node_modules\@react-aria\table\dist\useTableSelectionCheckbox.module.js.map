{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AA+BM,SAAS,0CAA6B,KAAsC,EAAE,KAAoB;IACvG,IAAI,OAAC,GAAG,EAAC,GAAG;IACZ,MAAM,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,+BAAuB,EAAE,OAAO;IAExD,OAAO;QACL,eAAe;YACb,GAAG,aAAa;YAChB,mBAAmB,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE,CAAA,GAAA,wCAAe,EAAE,OAAO,MAAM;QAC1E;IACF;AACF;AAOO,SAAS,0CAA6B,KAAoB;IAC/D,IAAI,WAAC,OAAO,eAAE,WAAW,iBAAE,aAAa,EAAC,GAAG,MAAM,gBAAgB;IAClE,MAAM,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAElE,OAAO;QACL,eAAe;YACb,cAAc,gBAAgB,MAAM,CAAC,kBAAkB,WAAW,WAAW;YAC7E,YAAY;YACZ,YAAY,kBAAkB,cAAe,MAAM,UAAU,CAAC,IAAI,KAAK,KAAM,MAAM,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK;YACrJ,iBAAiB,CAAC,WAAW,CAAC;YAC9B,UAAU,IAAM,MAAM,gBAAgB,CAAC,eAAe;QACxD;IACF;AACF", "sources": ["packages/@react-aria/table/src/useTableSelectionCheckbox.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaCheckboxProps} from '@react-types/checkbox';\nimport {getRowLabelledBy} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key} from '@react-types/shared';\nimport {TableState} from '@react-stately/table';\nimport {useGridSelectionCheckbox} from '@react-aria/grid';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaTableSelectionCheckboxProps {\n  /** A unique key for the checkbox. */\n  key: Key\n}\n\nexport interface TableSelectionCheckboxAria {\n  /** Props for the row selection checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\nexport interface TableSelectAllCheckboxAria {\n  /** Props for the select all checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\n/**\n * Provides the behavior and accessibility implementation for a selection checkbox in a table.\n * @param props - Props for the selection checkbox.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableSelectionCheckbox<T>(props: AriaTableSelectionCheckboxProps, state: TableState<T>): TableSelectionCheckboxAria {\n  let {key} = props;\n  const {checkboxProps} = useGridSelectionCheckbox(props, state);\n\n  return {\n    checkboxProps: {\n      ...checkboxProps,\n      'aria-labelledby': `${checkboxProps.id} ${getRowLabelledBy(state, key)}`\n    }\n  };\n}\n\n/**\n * Provides the behavior and accessibility implementation for the select all checkbox in a table.\n * @param props - Props for the select all checkbox.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableSelectAllCheckbox<T>(state: TableState<T>): TableSelectAllCheckboxAria {\n  let {isEmpty, isSelectAll, selectionMode} = state.selectionManager;\n  const stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n\n  return {\n    checkboxProps: {\n      'aria-label': stringFormatter.format(selectionMode === 'single' ? 'select' : 'selectAll'),\n      isSelected: isSelectAll,\n      isDisabled: selectionMode !== 'multiple' || (state.collection.size === 0 || (state.collection.rows.length === 1 && state.collection.rows[0].type === 'loader')),\n      isIndeterminate: !isEmpty && !isSelectAll,\n      onChange: () => state.selectionManager.toggleSelectAll()\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useTableSelectionCheckbox.module.js.map"}