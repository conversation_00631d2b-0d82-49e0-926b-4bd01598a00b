.data-upload-container {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.upload-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.security-badge {
  background-color: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #c3e6cb;
}

.dropzone {
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.dropzone:hover {
  border-color: #4A90E2;
  background-color: #F0F8FF;
}

.dropzone:focus {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
  border-color: #4A90E2;
}

.dropzone.active {
  border-color: #4A90E2;
  background-color: #E7F3FF;
  transform: scale(1.02);
}

.dropzone.loading {
  pointer-events: none;
  opacity: 0.7;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-icon {
  font-size: 48px;
  opacity: 0.6;
}

.dropzone p {
  margin: 0;
  color: #666;
}

.file-types {
  font-size: 12px;
  color: #888;
}

.file-limit {
  font-size: 11px;
  color: #999;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 15px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-status {
  padding: 10px 15px;
  border-radius: 4px;
  margin-top: 15px;
  font-size: 14px;
}

.upload-status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.upload-status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.uploaded-files {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.uploaded-files h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  font-size: 13px;
}

.file-type {
  background-color: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.file-records {
  font-size: 12px;
  color: #666;
}

.remove-file {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.remove-file:hover {
  background-color: #f8d7da;
}
