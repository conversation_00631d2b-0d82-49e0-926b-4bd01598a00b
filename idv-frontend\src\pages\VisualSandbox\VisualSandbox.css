/* Visual Sandbox Page - DRDO v1.7 */
.visual-sandbox-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.sandbox-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.sandbox-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.sandbox-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.sandbox-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 24px 0;
}

/* Chart Types Section */
.chart-types-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.chart-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.chart-type-card {
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.chart-type-card:hover {
  border-color: #4A90E2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
}

.chart-type-card.selected {
  border-color: #FF9933;
  background: #FFF7ED;
  box-shadow: 0 4px 12px rgba(255, 153, 51, 0.2);
}

.chart-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.chart-name {
  font-size: 14px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.chart-description {
  font-size: 12px;
  color: #64748B;
  margin: 0;
  line-height: 1.4;
}

/* Data Section */
.data-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.datasets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.dataset-card {
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.dataset-card:hover {
  border-color: #4A90E2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
}

.dataset-name {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.dataset-info {
  font-size: 12px;
  color: #64748B;
  margin: 0 0 12px 0;
}

.dataset-preview {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.data-point {
  font-size: 11px;
  color: #1A3C5E;
  background: #F8FAFC;
  padding: 4px 8px;
  border-radius: 4px;
}

/* Configuration Section */
.config-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.config-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-group label {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

.config-input {
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1A3C5E;
}

.config-input:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.config-color {
  padding: 8px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  height: 44px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  transform: scale(1.2);
}

/* Preview Section */
.preview-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.preview-container {
  border: 1px solid #F1F5F9;
  border-radius: 8px;
  padding: 24px;
  background: #FAFBFC;
  min-height: 300px;
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.placeholder-icon {
  font-size: 48px;
  color: #64748B;
}

.placeholder-content h3 {
  font-size: 18px;
  color: #1A3C5E;
  margin: 0;
}

.placeholder-content p {
  color: #64748B;
  margin: 0;
}

/* Chart Preview */
.chart-preview {
  text-align: center;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 24px 0;
}

.simple-bar-chart {
  position: relative;
}

.chart-area {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 16px;
  height: 220px;
  margin-bottom: 16px;
}

.bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bar {
  position: relative;
  min-width: 40px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 8px;
}

.bar-value {
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.bar-label {
  font-size: 11px;
  color: #64748B;
  text-align: center;
  max-width: 60px;
  word-wrap: break-word;
}

.chart-axes {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  font-size: 12px;
  color: #64748B;
}

/* Actions Section */
.actions-section {
  text-align: center;
  margin-bottom: 32px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-generate,
.btn-export {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-generate {
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
}

.btn-generate:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

.btn-export {
  background: white;
  color: #1A3C5E;
  border: 2px solid #E2E8F0;
}

.btn-export:hover {
  border-color: #4A90E2;
  background: #F8FAFC;
}

/* Access Denied */
.access-denied {
  text-align: center;
  padding: 80px 32px;
}

.access-denied h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.access-denied p {
  color: #64748B;
  font-size: 16px;
}

/* Sandbox Footer */
.sandbox-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-info {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sandbox-content {
    padding: 24px 16px;
  }
  
  .chart-types-section,
  .data-section,
  .config-section,
  .preview-section {
    padding: 24px 16px;
  }
  
  .chart-types-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .datasets-grid {
    grid-template-columns: 1fr;
  }
  
  .config-panel {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .chart-area {
    gap: 8px;
  }
  
  .bar {
    min-width: 30px;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-types-section,
.data-section,
.config-section,
.preview-section {
  animation: fadeIn 0.5s ease-out;
}
