import React, { useState } from 'react'
import './ManualDataEntry.css'

const ManualDataEntry = ({ onDataSubmit, dataType = 'equipment' }) => {
  const [formData, setFormData] = useState({
    system: '',
    status: '',
    value: '',
    timestamp: new Date().toISOString().slice(0, 16),
    location: '',
    securityLevel: 'PUBLIC',
    notes: ''
  })
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const systemOptions = {
    equipment: ['BrahMos', 'Agni-V', 'Tejas', 'Arjun', 'Akash', 'Prithvi'],
    mission: ['Operation Alpha', 'Operation Beta', 'Operation Gamma', 'Training Exercise'],
    simulation: ['Trajectory Analysis', 'Performance Test', 'Stress Test', 'Integration Test']
  }

  const statusOptions = ['Active', 'Standby', 'Maintenance', 'Offline', 'Testing']
  const locationOptions = ['Chandipur', 'Wheeler Island', 'Pokhran', 'ITR', 'DRDO Lab', 'Field Station']
  const securityLevels = ['PUBLIC', 'RESTRICTED', 'CONFIDENTIAL', 'SECRET']

  const validateForm = () => {
    const newErrors = {}

    if (!formData.system.trim()) {
      newErrors.system = 'System selection is required'
    }
    if (!formData.status.trim()) {
      newErrors.status = 'Status is required'
    }
    if (!formData.value.trim()) {
      newErrors.value = 'Value is required'
    } else if (isNaN(formData.value)) {
      newErrors.value = 'Value must be a number'
    }
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const dataEntry = {
        ...formData,
        id: Date.now(),
        dataType,
        value: parseFloat(formData.value),
        timestamp: new Date(formData.timestamp).toISOString()
      }

      if (onDataSubmit) {
        await onDataSubmit(dataEntry)
      }

      // Reset form
      setFormData({
        system: '',
        status: '',
        value: '',
        timestamp: new Date().toISOString().slice(0, 16),
        location: '',
        securityLevel: 'PUBLIC',
        notes: ''
      })

      alert('Data submitted successfully!')

    } catch (error) {
      alert(`Error submitting data: ${error.message}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <div className="manual-data-entry">
      <div className="entry-header">
        <h3>Manual Data Entry</h3>
        <span className="data-type-badge">{dataType.toUpperCase()}</span>
      </div>

      <form onSubmit={handleSubmit} className="data-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="system">System/Mission *</label>
            <select
              id="system"
              value={formData.system}
              onChange={(e) => handleChange('system', e.target.value)}
              className={errors.system ? 'error' : ''}
            >
              <option value="">Select System</option>
              {systemOptions[dataType]?.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
            {errors.system && <span className="error-text">{errors.system}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="status">Status *</label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => handleChange('status', e.target.value)}
              className={errors.status ? 'error' : ''}
            >
              <option value="">Select Status</option>
              {statusOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
            {errors.status && <span className="error-text">{errors.status}</span>}
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="value">Value *</label>
            <input
              type="number"
              id="value"
              step="0.01"
              value={formData.value}
              onChange={(e) => handleChange('value', e.target.value)}
              placeholder="Enter numeric value"
              className={errors.value ? 'error' : ''}
            />
            {errors.value && <span className="error-text">{errors.value}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="timestamp">Timestamp *</label>
            <input
              type="datetime-local"
              id="timestamp"
              value={formData.timestamp}
              onChange={(e) => handleChange('timestamp', e.target.value)}
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="location">Location *</label>
            <select
              id="location"
              value={formData.location}
              onChange={(e) => handleChange('location', e.target.value)}
              className={errors.location ? 'error' : ''}
            >
              <option value="">Select Location</option>
              {locationOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
            {errors.location && <span className="error-text">{errors.location}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="securityLevel">Security Level</label>
            <select
              id="securityLevel"
              value={formData.securityLevel}
              onChange={(e) => handleChange('securityLevel', e.target.value)}
            >
              {securityLevels.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="form-group full-width">
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleChange('notes', e.target.value)}
            placeholder="Additional notes or comments..."
            rows="3"
          />
        </div>

        <div className="form-actions">
          <button 
            type="submit" 
            className="submit-btn"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Data'}
          </button>
          <button 
            type="button" 
            className="reset-btn"
            onClick={() => setFormData({
              system: '',
              status: '',
              value: '',
              timestamp: new Date().toISOString().slice(0, 16),
              location: '',
              securityLevel: 'PUBLIC',
              notes: ''
            })}
          >
            Reset Form
          </button>
        </div>
      </form>
    </div>
  )
}

export default ManualDataEntry
