/* Roles Page - DRDO v1.7 */
.roles-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.roles-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.roles-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.roles-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.roles-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 32px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1A3C5E;
  margin: 0 0 24px 0;
}

/* Roles Overview */
.roles-overview {
  margin-bottom: 48px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.role-card {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
  transition: all 0.3s ease;
}

.role-card:hover {
  box-shadow: 0 8px 24px rgba(26, 60, 94, 0.15);
  transform: translateY(-2px);
}

.role-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.role-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 4px 0;
}

.role-description {
  font-size: 14px;
  color: #64748B;
  margin: 0;
}

.role-stats {
  text-align: right;
}

.user-count {
  font-size: 12px;
  color: #64748B;
  background: #F8FAFC;
  padding: 4px 8px;
  border-radius: 12px;
}

.role-permissions h4 {
  font-size: 14px;
  color: #1A3C5E;
  margin: 0 0 12px 0;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  padding: 4px 8px;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 6px;
  font-size: 11px;
  color: #1A3C5E;
}

.permission-tag.more {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* Permissions Matrix */
.permissions-matrix {
  margin-bottom: 48px;
}

.matrix-container {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.matrix-header {
  display: grid;
  grid-template-columns: 250px repeat(6, 1fr);
  background: #F8FAFC;
  border-bottom: 2px solid #E2E8F0;
}

.matrix-cell {
  padding: 12px 16px;
  border-right: 1px solid #E2E8F0;
  font-size: 12px;
  font-weight: 500;
}

.matrix-cell.header {
  background: #F8FAFC;
  font-weight: 600;
  color: #1A3C5E;
}

.matrix-cell.role-header {
  color: white;
  text-align: center;
}

.permission-category {
  border-bottom: 1px solid #F1F5F9;
}

.category-header {
  background: #F8FAFC;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #64748B;
  text-transform: uppercase;
  border-bottom: 1px solid #E2E8F0;
}

.matrix-row {
  display: grid;
  grid-template-columns: 250px repeat(6, 1fr);
  border-bottom: 1px solid #F8FAFC;
}

.matrix-row:hover {
  background: #F8FAFC;
}

.permission-name {
  font-weight: 500;
  color: #1A3C5E;
}

.permission-cell {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-granted {
  color: #059669;
  font-size: 16px;
  font-weight: 700;
}

.permission-denied {
  color: #DC2626;
  font-size: 16px;
  font-weight: 700;
}

/* Role Hierarchy */
.role-hierarchy {
  margin-bottom: 32px;
}

.hierarchy-chart {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.hierarchy-level {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;
  position: relative;
}

.hierarchy-level:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 24px;
  background: #E2E8F0;
}

.hierarchy-role {
  padding: 16px 24px;
  border-radius: 8px;
  text-align: center;
  color: white;
  min-width: 150px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hierarchy-role span {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.hierarchy-role small {
  font-size: 11px;
  opacity: 0.8;
}

.hierarchy-role.admin {
  background: #7C3AED;
}

.hierarchy-role.commander {
  background: #DC2626;
}

.hierarchy-role.scientist {
  background: #059669;
}

.hierarchy-role.engineer {
  background: #F59E0B;
}

.hierarchy-role.analyst {
  background: #3B82F6;
}

.hierarchy-role.guest {
  background: #64748B;
}

/* Access Denied */
.access-denied {
  text-align: center;
  padding: 80px 32px;
}

.access-denied h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.access-denied p {
  color: #64748B;
  font-size: 16px;
}

/* Roles Footer */
.roles-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-info {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .matrix-header,
  .matrix-row {
    grid-template-columns: 200px repeat(6, 1fr);
  }
}

@media (max-width: 768px) {
  .roles-content {
    padding: 24px 16px;
  }
  
  .roles-grid {
    grid-template-columns: 1fr;
  }
  
  .matrix-container {
    overflow-x: auto;
  }
  
  .matrix-header,
  .matrix-row {
    grid-template-columns: 150px repeat(6, 80px);
    min-width: 630px;
  }
  
  .hierarchy-level {
    flex-direction: column;
    align-items: center;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.role-card,
.matrix-container,
.hierarchy-chart {
  animation: fadeIn 0.5s ease-out;
}
