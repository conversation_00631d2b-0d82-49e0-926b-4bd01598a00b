/* Header v1.7 - DRDO Official Styling */
.header-v17 {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(26, 60, 94, 0.15);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

/* Header Left - Brand */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
}

.drdo-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drdo-logo {
  font-size: 32px;
  background: linear-gradient(135deg, #FF9933, #FFFFFF, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: white;
  letter-spacing: 0.5px;
}

.brand-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Header Center - Search */
.header-center {
  flex: 1;
  max-width: 400px;
}

.search-form {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  outline: none;
  border-color: #FF9933;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 153, 51, 0.2);
}

.search-btn {
  position: absolute;
  right: 4px;
  background: #FF9933;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.search-btn:hover {
  background: #E8890B;
  transform: scale(1.05);
}

/* Header Right - User Controls */
.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  min-width: 300px;
  justify-content: flex-end;
}

.status-indicators {
  display: flex;
  gap: 16px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.status-label {
  font-size: 10px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
  letter-spacing: 0.5px;
}

.status-value {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.status-value.secure {
  background: #FF9933;
  color: white;
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.theme-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #FF9933;
}

/* Theme Menu */
.theme-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 160px;
  z-index: 1000;
  margin-top: 8px;
}

.theme-option {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #1A3C5E;
  text-align: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.theme-option:hover {
  background: #F8FAFC;
}

.theme-option.active {
  background: #FF9933;
  color: white;
}

/* User Menu */
.user-menu-container {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 8px 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-menu-trigger:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #FF9933;
}

.user-avatar {
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
}

.user-role {
  font-size: 11px;
  background: #FF9933;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  font-weight: 600;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 250px;
  z-index: 1000;
  margin-top: 8px;
}

.user-info {
  margin-bottom: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-full-name {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
}

.user-email {
  font-size: 12px;
  color: #64748B;
}

.user-department {
  font-size: 12px;
  color: #64748B;
  font-style: italic;
}

.menu-divider {
  height: 1px;
  background: #E2E8F0;
  margin: 12px 0;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #1A3C5E;
  text-align: left;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  margin-bottom: 4px;
}

.menu-item:hover {
  background: #F8FAFC;
}

.menu-item.logout {
  color: #DC2626;
}

.menu-item.logout:hover {
  background: #FEE2E2;
}

/* Login Prompt */
.login-prompt {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* Development Badge */
.dev-badge {
  background: #F59E0B;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    padding: 12px 24px;
    gap: 16px;
  }
  
  .status-indicators {
    display: none;
  }
  
  .header-center {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .header-left,
  .header-right {
    min-width: auto;
  }
  
  .header-center {
    max-width: 100%;
    order: 3;
  }
  
  .brand-text {
    display: none;
  }
  
  .user-menu {
    right: auto;
    left: 0;
    min-width: 200px;
  }
  
  .theme-menu {
    right: auto;
    left: 0;
  }
}

/* Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
