/* Footer - DRDO Professional Styling */
.drdo-footer {
  background: linear-gradient(135deg, #1A3C5E 0%, #0F2A3E 100%);
  color: white;
  margin-top: auto;
  border-top: 3px solid #FF9933;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 48px;
  padding: 48px 32px 32px;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 32px;
  background: linear-gradient(135deg, #FF9933, #FFFFFF, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.org-name {
  font-size: 20px;
  font-weight: 700;
  color: white;
  letter-spacing: 1px;
}

.system-name {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.footer-description {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.footer-center {
  display: flex;
  justify-content: center;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  width: 100%;
}

.link-group h4 {
  font-size: 14px;
  font-weight: 600;
  color: #FF9933;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 8px;
}

.link-group a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 13px;
  transition: all 0.3s ease;
  display: block;
  padding: 4px 0;
}

.link-group a:hover {
  color: #FF9933;
  padding-left: 8px;
}

.footer-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.system-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  min-width: 160px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 12px;
  font-weight: 600;
  color: white;
  background: rgba(255, 153, 51, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px 32px;
  background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.copyright p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.footer-badges {
  display: flex;
  gap: 12px;
  align-items: center;
}

.security-badge,
.compliance-badge,
.version-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.security-badge {
  background: #059669;
  color: white;
}

.compliance-badge {
  background: #4A90E2;
  color: white;
}

.version-badge {
  background: #FF9933;
  color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }
  
  .footer-right {
    grid-column: 1 / -1;
    align-items: center;
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 32px 16px 24px;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .footer-bottom {
    padding: 16px;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .copyright {
    order: 2;
  }
  
  .footer-badges {
    order: 1;
  }
  
  .system-info {
    width: 100%;
  }
  
  .info-item {
    min-width: auto;
  }
}
