{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAMM,SAAS,0CAAS,KAAwC;IAC/D,OAAO,CAAA,GAAA,oBAAM,EAAE;QACb,IAAI,OAAO,UAAU,UACnB,IAAI;YACF,OAAO,CAAA,GAAA,oCAAS,EAAE;QACpB,EAAE,OAAM;YACN,OAAO;QACT;QAEF,OAAO;IACT,GAAG;QAAC;KAAM;AACZ", "sources": ["packages/@react-stately/color/src/useColor.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Color} from '@react-types/color';\nimport {parseColor} from './Color';\nimport {useMemo} from 'react';\n\nexport function useColor(value: string | Color | undefined | null): Color | null | undefined {\n  return useMemo(() => {\n    if (typeof value === 'string') {\n      try {\n        return parseColor(value);\n      } catch {\n        return undefined;\n      }\n    }\n    return value;\n  }, [value]);\n}\n"], "names": [], "version": 3, "file": "useColor.main.js.map"}