/* DRDO Formal Header Styling - Version 1.5 */
.drdo-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2D5A87 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-bottom: 3px solid #F5A623;
  position: sticky;
  top: 0;
  z-index: 1000;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  max-width: 1400px;
  margin: 0 auto;
  gap: 24px;
}

/* Brand Section */
.header-brand {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.drdo-logo {
  display: flex;
  align-items: center;
}

.logo-img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.org-title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1.2;
  letter-spacing: -0.3px;
}

.system-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 4px;
}

.system-name {
  font-size: 14px;
  color: #E2E8F0;
  font-weight: 500;
}

.version {
  font-size: 11px;
  color: #1A3C5E;
  background: #F5A623;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Search Section */
.header-search {
  flex: 1;
  max-width: 400px;
  margin: 0 24px;
}

.search-form {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 2px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 8px 12px;
  color: #FFFFFF;
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-btn {
  background: #F5A623;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  color: #1A3C5E;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: #E09612;
  transform: translateY(-1px);
}

/* Controls Section */
.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.control-label {
  font-size: 12px;
  color: #E2E8F0;
  font-weight: 500;
  white-space: nowrap;
}

/* Dropdown Styling */
.dropdown {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dropdown:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dropdown-value {
  font-size: 12px;
  font-weight: 600;
  color: #FFFFFF;
}

.dropdown-arrow {
  font-size: 10px;
  color: #E2E8F0;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #E5E7EB;
  min-width: 150px;
  z-index: 1001;
  margin-top: 4px;
  overflow: hidden;
}

.dropdown-item {
  padding: 10px 16px;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #F3F4F6;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #F3F4F6;
  color: #1A3C5E;
}

/* Theme Select */
.theme-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  outline: none;
}

.theme-select option {
  background: #1A3C5E;
  color: #FFFFFF;
}

/* User Menu */
.user-control {
  margin-left: 12px;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-menu:hover {
  background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  font-size: 18px;
}

.user-name {
  font-size: 13px;
  font-weight: 600;
  color: #FFFFFF;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-dropdown {
  min-width: 250px;
  padding: 0;
}

.user-info {
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
  background: #F8FAFC;
}

.user-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-detail strong {
  font-size: 14px;
  color: #1A3C5E;
  font-weight: 600;
}

.user-detail span {
  font-size: 12px;
  color: #6B7280;
}

.clearance {
  background: #FEF3C7;
  color: #92400E;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 10px !important;
  align-self: flex-start;
  margin-top: 4px;
}

.session-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #E5E7EB;
}

.session-info span {
  font-size: 11px;
  color: #9CA3AF;
  font-family: 'Courier New', monospace;
}

.user-actions {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-action-btn {
  background: transparent;
  border: none;
  padding: 8px 12px;
  text-align: left;
  color: #374151;
  font-size: 13px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.user-action-btn:hover {
  background: #F3F4F6;
  color: #1A3C5E;
}

.logout-btn:hover {
  background: #FEE2E2;
  color: #DC2626;
}

/* Login Button */
.login-btn-header {
  background: linear-gradient(135deg, #F5A623 0%, #E09612 100%);
  color: #1A3C5E;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(245, 166, 35, 0.3);
}

.login-btn-header:hover {
  background: linear-gradient(135deg, #E09612 0%, #D68910 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 166, 35, 0.4);
}
