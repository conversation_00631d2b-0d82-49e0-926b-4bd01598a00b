{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,UAAU,CAAC;IAC3C,iBAAiB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;IACtF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,WAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,UAAU,CAAC,qBAAqB,CAAC;IACxF,sBAAsB,CAAC,4CAA4C,CAAC;IACpE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,eAAe,CAAC;IAC9B,YAAY,CAAC,mBAAgB,CAAC;AAChC", "sources": ["packages/@react-aria/table/intl/pt-PT.json"], "sourcesContent": ["{\n  \"ascending\": \"ascendente\",\n  \"ascendingSort\": \"Ordenar por coluna {columnName} em ordem ascendente\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"descendente\",\n  \"descendingSort\": \"Ordenar por coluna {columnName} em ordem descendente\",\n  \"resizerDescription\": \"Prima Enter para iniciar o redimensionamento\",\n  \"select\": \"Selecionar\",\n  \"selectAll\": \"Selecionar tudo\",\n  \"sortable\": \"Coluna ordenável\"\n}\n"], "names": [], "version": 3, "file": "pt-PT.module.js.map"}