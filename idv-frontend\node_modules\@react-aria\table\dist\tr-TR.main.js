module.exports = {
    "ascending": `artan s\u{131}rada`,
    "ascendingSort": (args)=>`${args.columnName} s\xfctuna g\xf6re artan d\xfczende s\u{131}rala`,
    "columnSize": (args)=>`${args.value} piksel`,
    "descending": `azalan s\u{131}rada`,
    "descendingSort": (args)=>`${args.columnName} s\xfctuna g\xf6re azalan d\xfczende s\u{131}rala`,
    "resizerDescription": `Yeniden boyutland\u{131}rmak i\xe7in Enter'a bas\u{131}n`,
    "select": `Se\xe7`,
    "selectAll": `T\xfcm\xfcn\xfc Se\xe7`,
    "sortable": `S\u{131}ralanabilir s\xfctun`
};


//# sourceMappingURL=tr-TR.main.js.map
