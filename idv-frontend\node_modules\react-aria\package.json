{"name": "react-aria", "version": "3.41.1", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {".": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "./i18n": {"types": "./i18n/index.d.ts", "import": "./i18n/index.mjs", "require": "./i18n/index.js"}, "./i18n/*": {"types": "./i18n/lang.d.ts", "import": "./i18n/*.mjs", "require": "./i18n/*.js"}}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "i18n"], "sideEffects": false, "scripts": {"prepublishOnly": "mkdir -p dist; cp src/index.ts dist/types.d.ts; grep -v '^export type' src/index.ts > dist/module.js; babel --root-mode upward src/index.ts -o dist/main.js"}, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@internationalized/string": "^3.2.7", "@react-aria/breadcrumbs": "^3.5.26", "@react-aria/button": "^3.13.3", "@react-aria/calendar": "^3.8.3", "@react-aria/checkbox": "^3.15.7", "@react-aria/color": "^3.0.9", "@react-aria/combobox": "^3.12.5", "@react-aria/datepicker": "^3.14.5", "@react-aria/dialog": "^3.5.27", "@react-aria/disclosure": "^3.0.6", "@react-aria/dnd": "^3.10.1", "@react-aria/focus": "^3.20.5", "@react-aria/gridlist": "^3.13.2", "@react-aria/i18n": "^3.12.10", "@react-aria/interactions": "^3.25.3", "@react-aria/label": "^3.7.19", "@react-aria/landmark": "^3.0.4", "@react-aria/link": "^3.8.3", "@react-aria/listbox": "^3.14.6", "@react-aria/menu": "^3.18.5", "@react-aria/meter": "^3.4.24", "@react-aria/numberfield": "^3.11.16", "@react-aria/overlays": "^3.27.3", "@react-aria/progress": "^3.4.24", "@react-aria/radio": "^3.11.5", "@react-aria/searchfield": "^3.8.6", "@react-aria/select": "^3.15.7", "@react-aria/selection": "^3.24.3", "@react-aria/separator": "^3.4.10", "@react-aria/slider": "^3.7.21", "@react-aria/ssr": "^3.9.9", "@react-aria/switch": "^3.7.5", "@react-aria/table": "^3.17.5", "@react-aria/tabs": "^3.10.5", "@react-aria/tag": "^3.6.2", "@react-aria/textfield": "^3.17.5", "@react-aria/toast": "^3.0.5", "@react-aria/tooltip": "^3.8.5", "@react-aria/tree": "^3.1.1", "@react-aria/utils": "^3.29.1", "@react-aria/visually-hidden": "^3.8.25", "@react-types/shared": "^3.30.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.3"}, "publishConfig": {"access": "public"}, "gitHead": "a063122082d2b372e4846b58c85ae69ec73887ff"}