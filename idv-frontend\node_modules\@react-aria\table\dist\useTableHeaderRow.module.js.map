{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAkBM,SAAS,0CAAqB,KAAsB,EAAE,KAAoB,EAAE,GAA8B;IAC/G,IAAI,QAAC,IAAI,iBAAE,aAAa,EAAC,GAAG;IAC5B,IAAI,WAAW;QACb,MAAM;IACR;IAEA,IAAI,iBAAiB,CAAE,CAAA,CAAA,GAAA,sBAAc,OAAO,kBAAkB,KAAI,GAChE,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,GAAG,2BAA2B;IAGzE,OAAO;kBACL;IACF;AACF", "sources": ["packages/@react-aria/table/src/useTableHeaderRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {GridRowProps} from '@react-aria/grid';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState} from '@react-stately/table';\n\nexport interface TableHeaderRowAria {\n  /** Props for the grid row element. */\n  rowProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a header row in a table.\n * @param props - Props for the row.\n * @param state - State of the table, as returned by `useTableState`.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function useTableHeaderRow<T>(props: GridRowProps<T>, state: TableState<T>, ref: RefObject<Element | null>): TableHeaderRowAria {\n  let {node, isVirtualized} = props;\n  let rowProps = {\n    role: 'row'\n  };\n\n  if (isVirtualized && !(tableNestedRows() && 'expandedKeys' in state)) {\n    rowProps['aria-rowindex'] = node.index + 1; // aria-rowindex is 1 based\n  }\n\n  return {\n    rowProps\n  };\n}\n"], "names": [], "version": 3, "file": "useTableHeaderRow.module.js.map"}