.dashboard-container {
  padding: 25px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.page-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.content-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.content-section.full-width {
  grid-column: 1 / -1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.section-title {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.section-badge {
  background-color: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #ced4da;
}

.section-content {
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.system-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.status-value {
  font-size: 12px;
  color: #666;
  background-color: #fff;
  padding: 3px 8px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

.visualization-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.viz-placeholder {
  padding: 20px;
  background-color: #f8f9fa;
  border: 2px dashed #ced4da;
  border-radius: 6px;
  text-align: center;
}

.viz-placeholder h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.viz-placeholder p {
  margin: 0;
  color: #666;
  font-size: 12px;
}
