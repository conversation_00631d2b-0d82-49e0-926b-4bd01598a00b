import React, { useState, useEffect } from 'react'
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell 
} from 'recharts'
import DataExportModal from '../DataSharing/DataExportModal'
import './AdvancedEquipmentStatus.css'

const AdvancedEquipmentStatus = () => {
  const [selectedSystem, setSelectedSystem] = useState('BrahMos')
  const [timeRange, setTimeRange] = useState('24h')
  const [viewMode, setViewMode] = useState('overview')
  const [showExportModal, setShowExportModal] = useState(false)
  const [realTimeData, setRealTimeData] = useState([])
  const [alertLevel, setAlertLevel] = useState('normal')

  // Comprehensive equipment data based on research papers
  const equipmentData = {
    BrahMos: {
      name: 'BrahMos Supersonic Cruise Missile',
      status: 'Operational',
      readiness: 98.5,
      lastMaintenance: '2025-07-05',
      nextMaintenance: '2025-07-15',
      location: 'Chandipur Test Range',
      classification: 'CONFIDENTIAL',
      specifications: {
        range: '290 km',
        speed: 'Mach 2.8-3.0',
        warhead: '200-300 kg',
        guidance: 'Inertial + GPS + Active Radar'
      },
      healthMetrics: [
        { time: '00:00', propulsion: 98, guidance: 97, warhead: 99, communication: 96 },
        { time: '04:00', propulsion: 97, guidance: 98, warhead: 99, communication: 97 },
        { time: '08:00', propulsion: 98, guidance: 96, warhead: 98, communication: 98 },
        { time: '12:00', propulsion: 99, guidance: 97, warhead: 99, communication: 97 },
        { time: '16:00', propulsion: 98, guidance: 98, warhead: 98, communication: 99 },
        { time: '20:00', propulsion: 97, guidance: 97, warhead: 99, communication: 98 }
      ],
      performanceData: [
        { parameter: 'Accuracy', value: 95, target: 90 },
        { parameter: 'Reliability', value: 98, target: 95 },
        { parameter: 'Availability', value: 92, target: 85 },
        { parameter: 'Maintainability', value: 88, target: 80 },
        { parameter: 'Safety', value: 99, target: 98 }
      ]
    },
    'Agni-V': {
      name: 'Agni-V Intercontinental Ballistic Missile',
      status: 'Ready',
      readiness: 95.2,
      lastMaintenance: '2025-07-03',
      nextMaintenance: '2025-07-20',
      location: 'Wheeler Island',
      classification: 'SECRET',
      specifications: {
        range: '5000+ km',
        speed: 'Mach 24',
        warhead: '1500 kg',
        guidance: 'Ring Laser Gyroscope + Accelerometer'
      },
      healthMetrics: [
        { time: '00:00', propulsion: 95, guidance: 96, warhead: 97, communication: 94 },
        { time: '04:00', propulsion: 94, guidance: 95, warhead: 96, communication: 95 },
        { time: '08:00', propulsion: 96, guidance: 97, warhead: 97, communication: 96 },
        { time: '12:00', propulsion: 95, guidance: 96, warhead: 98, communication: 95 },
        { time: '16:00', propulsion: 97, guidance: 98, warhead: 97, communication: 97 },
        { time: '20:00', propulsion: 96, guidance: 97, warhead: 98, communication: 96 }
      ],
      performanceData: [
        { parameter: 'Accuracy', value: 92, target: 88 },
        { parameter: 'Reliability', value: 96, target: 92 },
        { parameter: 'Availability', value: 89, target: 82 },
        { parameter: 'Maintainability', value: 85, target: 78 },
        { parameter: 'Safety', value: 98, target: 96 }
      ]
    },
    Tejas: {
      name: 'Tejas Light Combat Aircraft',
      status: 'Active',
      readiness: 92.8,
      lastMaintenance: '2025-07-06',
      nextMaintenance: '2025-07-12',
      location: 'HAL Bangalore',
      classification: 'RESTRICTED',
      specifications: {
        range: '3000 km',
        speed: 'Mach 1.8',
        payload: '5300 kg',
        avionics: 'Digital Fly-by-Wire'
      },
      healthMetrics: [
        { time: '00:00', propulsion: 93, guidance: 92, warhead: 94, communication: 91 },
        { time: '04:00', propulsion: 92, guidance: 93, warhead: 93, communication: 92 },
        { time: '08:00', propulsion: 94, guidance: 91, warhead: 95, communication: 93 },
        { time: '12:00', propulsion: 93, guidance: 94, warhead: 94, communication: 92 },
        { time: '16:00', propulsion: 95, guidance: 93, warhead: 96, communication: 94 },
        { time: '20:00', propulsion: 94, guidance: 92, warhead: 95, communication: 93 }
      ],
      performanceData: [
        { parameter: 'Accuracy', value: 89, target: 85 },
        { parameter: 'Reliability', value: 94, target: 90 },
        { parameter: 'Availability', value: 87, target: 80 },
        { parameter: 'Maintainability', value: 82, target: 75 },
        { parameter: 'Safety', value: 97, target: 95 }
      ]
    }
  }

  const systems = Object.keys(equipmentData)
  const currentSystem = equipmentData[selectedSystem]

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      const newDataPoint = {
        time: new Date().toLocaleTimeString(),
        value: 90 + Math.random() * 10,
        temperature: 45 + Math.random() * 10,
        pressure: 100 + Math.random() * 20
      }
      setRealTimeData(prev => [...prev.slice(-19), newDataPoint])
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status) => {
    switch (status) {
      case 'Operational': return '#059669'
      case 'Ready': return '#0ea5e9'
      case 'Active': return '#8b5cf6'
      case 'Maintenance': return '#f59e0b'
      case 'Offline': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getReadinessColor = (readiness) => {
    if (readiness >= 95) return '#059669'
    if (readiness >= 90) return '#0ea5e9'
    if (readiness >= 85) return '#f59e0b'
    return '#ef4444'
  }

  return (
    <div className="advanced-equipment-status">
      {/* Modern Header with Controls */}
      <div className="status-header">
        <div className="header-left">
          <h2 className="module-title">Equipment Status Monitor</h2>
          <p className="module-subtitle">Real-time Indigenous Defense Systems Analysis</p>
        </div>
        <div className="header-controls">
          <div className="control-group">
            <label>System:</label>
            <select 
              value={selectedSystem}
              onChange={(e) => setSelectedSystem(e.target.value)}
              className="modern-select"
            >
              {systems.map(system => (
                <option key={system} value={system}>{system}</option>
              ))}
            </select>
          </div>
          <div className="control-group">
            <label>Time Range:</label>
            <div className="time-tabs">
              {['1h', '6h', '24h', '7d'].map(range => (
                <button
                  key={range}
                  className={`time-tab ${timeRange === range ? 'active' : ''}`}
                  onClick={() => setTimeRange(range)}
                >
                  {range}
                </button>
              ))}
            </div>
          </div>
          <div className="control-group">
            <button 
              className="export-btn modern-btn"
              onClick={() => setShowExportModal(true)}
            >
              📊 Export Report
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="metrics-grid">
        <div className="metric-card primary">
          <div className="metric-header">
            <span className="metric-icon">🎯</span>
            <span className="metric-title">System Readiness</span>
            <span className="info-icon">ℹ️</span>
          </div>
          <div className="metric-value">{currentSystem.readiness}%</div>
          <div className="metric-comparison">
            vs. 94.2% last week
            <span className="metric-change positive">+{(currentSystem.readiness - 94.2).toFixed(1)}%</span>
          </div>
          <div className="metric-chart">
            <div 
              className="progress-bar"
              style={{ 
                width: `${currentSystem.readiness}%`,
                backgroundColor: getReadinessColor(currentSystem.readiness)
              }}
            />
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon">⚡</span>
            <span className="metric-title">Performance Score</span>
            <span className="info-icon">ℹ️</span>
          </div>
          <div className="metric-value">94.8</div>
          <div className="metric-comparison">
            vs. 92.1 last week
            <span className="metric-change positive">+2.7</span>
          </div>
          <div className="metric-mini-chart">
            <ResponsiveContainer width="100%" height={40}>
              <AreaChart data={currentSystem.healthMetrics.slice(-6)}>
                <Area 
                  type="monotone" 
                  dataKey="propulsion" 
                  stroke="#4A90E2" 
                  fill="#E6F0FA" 
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon">🔧</span>
            <span className="metric-title">Maintenance Status</span>
            <span className="info-icon">ℹ️</span>
          </div>
          <div className="metric-value">8 Days</div>
          <div className="metric-comparison">
            Until next maintenance
            <span className="metric-change neutral">Scheduled</span>
          </div>
          <div className="maintenance-timeline">
            <div className="timeline-bar">
              <div className="timeline-progress" style={{ width: '65%' }} />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="content-layout">
        {/* Left Section - System Health Chart */}
        <div className="chart-section">
          <div className="chart-card">
            <div className="chart-header">
              <h3>System Health Monitoring</h3>
              <div className="chart-tabs">
                <button className={`chart-tab ${viewMode === 'overview' ? 'active' : ''}`}
                        onClick={() => setViewMode('overview')}>Overview</button>
                <button className={`chart-tab ${viewMode === 'detailed' ? 'active' : ''}`}
                        onClick={() => setViewMode('detailed')}>Detailed</button>
                <button className={`chart-tab ${viewMode === 'realtime' ? 'active' : ''}`}
                        onClick={() => setViewMode('realtime')}>Real-time</button>
              </div>
            </div>
            <div className="chart-content">
              {viewMode === 'overview' && (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={currentSystem.healthMetrics}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="time" stroke="#6b7280" fontSize={12} />
                    <YAxis domain={[85, 100]} stroke="#6b7280" fontSize={12} />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Legend />
                    <Line type="monotone" dataKey="propulsion" stroke="#4A90E2" strokeWidth={3} dot={{ r: 4 }} />
                    <Line type="monotone" dataKey="guidance" stroke="#F5A623" strokeWidth={3} dot={{ r: 4 }} />
                    <Line type="monotone" dataKey="warhead" stroke="#059669" strokeWidth={3} dot={{ r: 4 }} />
                    <Line type="monotone" dataKey="communication" stroke="#8b5cf6" strokeWidth={3} dot={{ r: 4 }} />
                  </LineChart>
                </ResponsiveContainer>
              )}

              {viewMode === 'detailed' && (
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={currentSystem.healthMetrics}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="time" stroke="#6b7280" fontSize={12} />
                    <YAxis domain={[85, 100]} stroke="#6b7280" fontSize={12} />
                    <Tooltip />
                    <Area type="monotone" dataKey="propulsion" stackId="1" stroke="#4A90E2" fill="#E6F0FA" />
                    <Area type="monotone" dataKey="guidance" stackId="2" stroke="#F5A623" fill="#FFF3E0" />
                    <Area type="monotone" dataKey="warhead" stackId="3" stroke="#059669" fill="#E6F7F1" />
                    <Area type="monotone" dataKey="communication" stackId="4" stroke="#8b5cf6" fill="#F3E8FF" />
                  </AreaChart>
                </ResponsiveContainer>
              )}

              {viewMode === 'realtime' && (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={realTimeData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="time" stroke="#6b7280" fontSize={12} />
                    <YAxis stroke="#6b7280" fontSize={12} />
                    <Tooltip />
                    <Line type="monotone" dataKey="value" stroke="#ef4444" strokeWidth={2} dot={false} />
                    <Line type="monotone" dataKey="temperature" stroke="#f59e0b" strokeWidth={2} dot={false} />
                    <Line type="monotone" dataKey="pressure" stroke="#0ea5e9" strokeWidth={2} dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </div>

        {/* Right Section - Performance Radar & Stats */}
        <div className="stats-section">
          {/* Performance Radar Chart */}
          <div className="radar-card">
            <h4>Performance Analysis</h4>
            <ResponsiveContainer width="100%" height={200}>
              <RadarChart data={currentSystem.performanceData}>
                <PolarGrid stroke="#e5e7eb" />
                <PolarAngleAxis dataKey="parameter" tick={{ fontSize: 10 }} />
                <PolarRadiusAxis domain={[0, 100]} tick={false} />
                <Radar name="Current" dataKey="value" stroke="#4A90E2" fill="#4A90E2" fillOpacity={0.3} strokeWidth={2} />
                <Radar name="Target" dataKey="target" stroke="#F5A623" fill="#F5A623" fillOpacity={0.1} strokeWidth={2} />
                <Tooltip />
              </RadarChart>
            </ResponsiveContainer>
          </div>

          {/* System Statistics */}
          <div className="stats-cards">
            <div className="stat-item">
              <div className="stat-label">Highest Performance</div>
              <div className="stat-value">99.2%</div>
              <div className="stat-period">Yesterday</div>
              <div className="stat-bars">
                <div className="stat-bar" style={{ width: '85%', backgroundColor: '#4A90E2' }}>Propulsion</div>
                <div className="stat-bar" style={{ width: '92%', backgroundColor: '#F5A623' }}>Guidance</div>
              </div>
            </div>

            <div className="stat-item">
              <div className="stat-label">Lowest Performance</div>
              <div className="stat-value">87.5%</div>
              <div className="stat-period">Last Week</div>
              <div className="stat-bars">
                <div className="stat-bar" style={{ width: '70%', backgroundColor: '#4A90E2' }}>Communication</div>
                <div className="stat-bar" style={{ width: '75%', backgroundColor: '#F5A623' }}>Maintenance</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section - Detailed Data Table */}
      <div className="data-table-section">
        <div className="table-header">
          <h3>System Components Status</h3>
          <div className="table-filters">
            <select className="filter-select">
              <option>All Components</option>
              <option>Critical Only</option>
              <option>Maintenance Required</option>
            </select>
            <select className="filter-select">
              <option>All Status</option>
              <option>Operational</option>
              <option>Warning</option>
              <option>Critical</option>
            </select>
            <button className="filter-btn">🔍 Filter</button>
          </div>
        </div>

        <div className="data-table">
          <table>
            <thead>
              <tr>
                <th>Component</th>
                <th>Status</th>
                <th>Health Score</th>
                <th>Last Check</th>
                <th>Next Maintenance</th>
                <th>Criticality</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Propulsion System</td>
                <td><span className="status-dot operational"></span>Operational</td>
                <td>98.5%</td>
                <td>2 hours ago</td>
                <td>8 days</td>
                <td><span className="criticality high">High</span></td>
                <td><button className="action-btn">View Details →</button></td>
              </tr>
              <tr>
                <td>Guidance System</td>
                <td><span className="status-dot operational"></span>Operational</td>
                <td>97.2%</td>
                <td>1 hour ago</td>
                <td>12 days</td>
                <td><span className="criticality high">High</span></td>
                <td><button className="action-btn">View Details →</button></td>
              </tr>
              <tr>
                <td>Warhead Assembly</td>
                <td><span className="status-dot operational"></span>Operational</td>
                <td>99.1%</td>
                <td>30 min ago</td>
                <td>15 days</td>
                <td><span className="criticality critical">Critical</span></td>
                <td><button className="action-btn">View Details →</button></td>
              </tr>
              <tr>
                <td>Communication Module</td>
                <td><span className="status-dot warning"></span>Warning</td>
                <td>89.7%</td>
                <td>3 hours ago</td>
                <td>5 days</td>
                <td><span className="criticality medium">Medium</span></td>
                <td><button className="action-btn">View Details →</button></td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="table-pagination">
          <span>Showing 1-4 of 12 components</span>
          <div className="pagination-controls">
            <button>← Previous</button>
            <button className="active">1</button>
            <button>2</button>
            <button>3</button>
            <button>Next →</button>
          </div>
        </div>
      </div>

      {/* Export Modal */}
      {showExportModal && (
        <DataExportModal
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
          data={currentSystem}
          title={`${currentSystem.name} Status Report`}
        />
      )}
    </div>
  )
}

export default AdvancedEquipmentStatus
