// Type definitions for IDX Dashboard v1.5

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  department: string;
  clearanceLevel: SecurityLevel;
}

export type UserRole = 'admin' | 'scientist' | 'engineer' | 'analyst' | 'guest';

export type SecurityLevel = 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';

export type ThemeType = 'formal-white' | 'minimal' | 'dark' | 'dracula';

export type Language = 'en' | 'hi' | 'ta' | 'te';

export interface AuditEvent {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  securityLevel: SecurityLevel;
  sessionId: string;
  module: string;
  success: boolean;
  errorMessage?: string;
}

export interface EncryptedData {
  data: string;
  timestamp: string;
  securityLevel: SecurityLevel;
  checksum: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  description: string;
  requiredRole: UserRole;
  path: string;
}

export interface ChartData {
  [key: string]: any;
}

export interface SystemMetrics {
  name: string;
  status: 'Operational' | 'Warning' | 'Critical' | 'Offline';
  health: number;
  readiness: number;
  lastUpdate: string;
  specifications: Record<string, any>;
  healthMetrics: ChartData[];
  performanceData: ChartData[];
}

export interface DashboardState {
  activeView: string;
  selectedSystem: string;
  timeRange: string;
  filters: Record<string, any>;
}

export interface ThemeConfig {
  name: string;
  description: string;
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
}
