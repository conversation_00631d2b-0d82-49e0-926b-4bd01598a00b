{"mappings": ";AAAA,4BAAiB;IAAG,SAAS,CAAC,KAAK,CAAC;IAClC,SAAS,CAAC,OAAO,CAAC;IAClB,QAAQ,CAAC,IAAI,CAAC;IACd,eAAe,CAAC,QAAQ,CAAC;IACzB,cAAc,CAAC,UAAU,CAAC;IAC1B,SAAS,CAAC,KAAK,CAAC;IAChB,gBAAgB,CAAC,SAAS,CAAC;IAC3B,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;IACrE,QAAQ,CAAC,IAAI,CAAC;IACd,aAAa,CAAC,QAAQ,CAAC;IACvB,QAAQ,CAAC,OAAO,CAAC;IACjB,QAAQ,CAAC,IAAI,CAAC;IACd,WAAW,CAAC,aAAU,CAAC;IACvB,SAAS,CAAC,OAAI,CAAC;IACf,cAAc,CAAC,WAAQ,CAAC;IACxB,OAAO,CAAC,OAAO,CAAC;IAChB,SAAS,CAAC,MAAM,CAAC;IACjB,aAAa,CAAC,WAAW,CAAC;IAC1B,WAAW,CAAC,OAAO,CAAC;IACpB,gBAAgB,CAAC,WAAW,CAAC;IAC7B,UAAU,CAAC,MAAM,CAAC;IAClB,iBAAiB,CAAC,UAAU,CAAC;IAC7B,QAAQ,CAAC,OAAO,CAAC;IACjB,QAAQ,CAAC,IAAI,CAAC;IACd,YAAY,CAAC,OAAO,CAAC;IACrB,UAAU,CAAC,IAAI,CAAC;IAChB,kBAAkB,CAAC,WAAW,CAAC;IAC/B,OAAO,CAAC,GAAG,CAAC;IACZ,cAAc,CAAC,SAAS,CAAC;IACzB,cAAc,CAAC,YAAS,CAAC;IACzB,wBAAwB,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,kBAAkB,CAAC,YAAY,CAAC;IAC3H,aAAa,CAAC,YAAY,CAAC;IAC3B,cAAc,CAAC,WAAW,CAAC;IAC3B,WAAW,CAAC,SAAS,CAAC;IACtB,SAAS,CAAC,OAAI,CAAC;IACf,UAAU,CAAC,IAAI,CAAC;IAChB,gBAAgB,CAAC,WAAQ,CAAC;AAC5B", "sources": ["packages/@react-stately/color/intl/de-DE.json"], "sourcesContent": ["{\n  \"alpha\": \"Alpha\",\n  \"black\": \"<PERSON><PERSON><PERSON>\",\n  \"blue\": \"<PERSON><PERSON>\",\n  \"blue purple\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"brightness\": \"Helligkeit\",\n  \"brown\": \"<PERSON>\",\n  \"brown yellow\": \"<PERSON><PERSON><PERSON>\",\n  \"colorName\": \"{lightness} {chroma} {hue}\",\n  \"cyan\": \"<PERSON><PERSON>\",\n  \"cyan blue\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"dark\": \"dunkles\",\n  \"gray\": \"Grau\",\n  \"grayish\": \"gräuliches\",\n  \"green\": \"Grün\",\n  \"green cyan\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"hue\": \"Farbton\",\n  \"light\": \"helles\",\n  \"lightness\": \"Leuchtkraft\",\n  \"magenta\": \"Magenta\",\n  \"magenta pink\": \"Magentarosa\",\n  \"orange\": \"Orange\",\n  \"orange yellow\": \"Orangegelb\",\n  \"pale\": \"blasses\",\n  \"pink\": \"Rosa\",\n  \"pink red\": \"Rosarot\",\n  \"purple\": \"Lila\",\n  \"purple magenta\": \"Lilamagenta\",\n  \"red\": \"Rot\",\n  \"red orange\": \"Rotorange\",\n  \"saturation\": \"<PERSON><PERSON>tigung\",\n  \"transparentColorName\": \"{lightness} {chroma} {hue}, zu {percentTransparent} transparent\",\n  \"very dark\": \"sehr dunkles\",\n  \"very light\": \"sehr helles\",\n  \"vibrant\": \"lebhaftes\",\n  \"white\": \"Weiß\",\n  \"yellow\": \"Gelb\",\n  \"yellow green\": \"Gelbgrün\"\n}\n"], "names": [], "version": 3, "file": "de-DE.module.js.map"}