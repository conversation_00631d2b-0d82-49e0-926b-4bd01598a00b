{"mappings": ";AAAA,4BAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;IACjC,SAAS,CAAC,KAAK,CAAC;IAChB,QAAQ,CAAC,QAAQ,CAAC;IAClB,eAAe,CAAC,eAAe,CAAC;IAChC,cAAc,CAAC,YAAY,CAAC;IAC5B,SAAS,CAAC,IAAI,CAAC;IACf,gBAAgB,CAAC,WAAW,CAAC;IAC7B,aAAa,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;IACrE,QAAQ,CAAC,IAAI,CAAC;IACd,aAAa,CAAC,aAAa,CAAC;IAC5B,QAAQ,CAAC,SAAM,CAAC;IAChB,QAAQ,CAAC,GAAG,CAAC;IACb,WAAW,CAAC,aAAO,CAAC;IACpB,SAAS,CAAC,KAAK,CAAC;IAChB,cAAc,CAAC,UAAU,CAAC;IAC1B,OAAO,CAAC,kBAAM,CAAC;IACf,SAAS,CAAC,OAAO,CAAC;IAClB,aAAa,CAAC,YAAY,CAAC;IAC3B,WAAW,CAAC,MAAM,CAAC;IACnB,gBAAgB,CAAC,UAAU,CAAC;IAC5B,UAAU,CAAC,UAAU,CAAC;IACtB,iBAAiB,CAAC,iBAAiB,CAAC;IACpC,QAAQ,CAAC,GAAG,CAAC;IACb,QAAQ,CAAC,GAAG,CAAC;IACb,YAAY,CAAC,cAAQ,CAAC;IACtB,UAAU,CAAC,MAAM,CAAC;IAClB,kBAAkB,CAAC,aAAa,CAAC;IACjC,OAAO,CAAC,UAAI,CAAC;IACb,cAAc,CAAC,qBAAe,CAAC;IAC/B,cAAc,CAAC,eAAS,CAAC;IACzB,wBAAwB,CAAC,OAAS,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,kBAAkB,CAAC,YAAY,CAAC;IACxH,aAAa,CAAC,gBAAa,CAAC;IAC5B,cAAc,CAAC,cAAc,CAAC;IAC9B,WAAW,CAAC,yBAAa,CAAC;IAC1B,SAAS,CAAC,GAAG,CAAC;IACd,UAAU,CAAC,MAAM,CAAC;IAClB,gBAAgB,CAAC,YAAY,CAAC;AAChC", "sources": ["packages/@react-stately/color/intl/ro-RO.json"], "sourcesContent": ["{\n  \"alpha\": \"Alfa\",\n  \"black\": \"negru\",\n  \"blue\": \"<PERSON>stru\",\n  \"blue purple\": \"albastru-violet\",\n  \"brightness\": \"Luminozitate\",\n  \"brown\": \"maro\",\n  \"brown yellow\": \"galben maro\",\n  \"colorName\": \"{lightness} {chroma} {hue}\",\n  \"cyan\": \"bleu\",\n  \"cyan blue\": \"albastru-bleu\",\n  \"dark\": \"închis\",\n  \"gray\": \"gri\",\n  \"grayish\": \"cenu<PERSON>iu\",\n  \"green\": \"Verde\",\n  \"green cyan\": \"verde bleu\",\n  \"hue\": \"Nuanță\",\n  \"light\": \"deschis\",\n  \"lightness\": \"Luminozitate\",\n  \"magenta\": \"fucsia\",\n  \"magenta pink\": \"roz-fucsia\",\n  \"orange\": \"portocaliu\",\n  \"orange yellow\": \"galben-portocaliu\",\n  \"pale\": \"pal\",\n  \"pink\": \"roz\",\n  \"pink red\": \"roz-roșu\",\n  \"purple\": \"violet\",\n  \"purple magenta\": \"violet-fucsia\",\n  \"red\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"red orange\": \"portocaliu-roșu\",\n  \"saturation\": \"Saturație\",\n  \"transparentColorName\": \"{lightness} {chroma} {hue}, {percentTransparent} transparent\",\n  \"very dark\": \"foarte închis\",\n  \"very light\": \"foarte deschis\",\n  \"vibrant\": \"plin de viață\",\n  \"white\": \"alb\",\n  \"yellow\": \"galben\",\n  \"yellow green\": \"galben-verde\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.module.js.map"}