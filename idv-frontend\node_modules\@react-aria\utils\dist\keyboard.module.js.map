{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAUM,SAAS,0CAAiB,CAAQ;IACvC,IAAI,CAAA,GAAA,yCAAI,KACN,OAAO,EAAE,OAAO;IAGlB,OAAO,EAAE,OAAO;AAClB", "sources": ["packages/@react-aria/utils/src/keyboard.tsx"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isMac} from './platform';\n\ninterface Event {\n  altKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean\n}\n\nexport function isCtrlKeyPressed(e: Event): boolean {\n  if (isMac()) {\n    return e.metaKey;\n  }\n\n  return e.ctrlKey;\n}\n"], "names": [], "version": 3, "file": "keyboard.module.js.map"}