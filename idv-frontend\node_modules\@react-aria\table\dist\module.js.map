{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;;AAaM,SAAS;IACd,OAAO,CAAA,GAAA,sBAAc;AACvB", "sources": ["packages/@react-aria/table/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {useTable} from './useTable';\nexport {useTableColumnHeader} from './useTableColumnHeader';\nexport {useTableRow} from './useTableRow';\nexport {useTableHeaderRow} from './useTableHeaderRow';\nexport {useTableCell} from './useTableCell';\nexport {useTableSelectionCheckbox, useTableSelectAllCheckbox} from './useTableSelectionCheckbox';\nexport {useTableColumnResize} from './useTableColumnResize';\n\n// Workaround for a Parcel bug where re-exports don't work in the CommonJS output format...\n// export {useGridRowGroup as useTableRowGroup} from '@react-aria/grid';\nimport {GridRowGroupAria, useGridRowGroup} from '@react-aria/grid';\nexport function useTableRowGroup(): GridRowGroupAria {\n  return useGridRowGroup();\n}\n\nexport type {AriaTableProps} from './useTable';\nexport type {GridAria, GridRowAria, GridRowProps} from '@react-aria/grid';\nexport type {AriaTableColumnHeaderProps, TableColumnHeaderAria} from './useTableColumnHeader';\nexport type {AriaTableCellProps, TableCellAria} from './useTableCell';\nexport type {TableHeaderRowAria} from './useTableHeaderRow';\nexport type {AriaTableSelectionCheckboxProps, TableSelectionCheckboxAria, TableSelectAllCheckboxAria} from './useTableSelectionCheckbox';\nexport type {AriaTableColumnResizeProps, TableColumnResizeAria} from './useTableColumnResize';\n"], "names": [], "version": 3, "file": "module.js.map"}