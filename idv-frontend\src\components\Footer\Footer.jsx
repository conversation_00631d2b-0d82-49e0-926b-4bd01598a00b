import React from 'react'
import './Footer.css'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  
  return (
    <footer className="drdo-footer">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-left">
            <div className="footer-logo">
              <span className="logo-icon">🇮🇳</span>
              <div className="logo-text">
                <span className="org-name">DRDO</span>
                <span className="system-name">Interactive Dashboard System</span>
              </div>
            </div>
            <p className="footer-description">
              Defence Research and Development Organisation - Advancing India's Defence Capabilities
            </p>
          </div>
          
          <div className="footer-center">
            <div className="footer-links">
              <div className="link-group">
                <h4>System</h4>
                <ul>
                  <li><a href="/dashboard">Dashboard</a></li>
                  <li><a href="/data-transfer">Data Transfer</a></li>
                  <li><a href="/visual-sandbox">Analytics</a></li>
                  <li><a href="/audit-log">Audit Log</a></li>
                </ul>
              </div>
              <div className="link-group">
                <h4>Administration</h4>
                <ul>
                  <li><a href="/users">User Management</a></li>
                  <li><a href="/roles">Role Management</a></li>
                  <li><a href="/settings">System Settings</a></li>
                  <li><a href="/profile">Profile</a></li>
                </ul>
              </div>
              <div className="link-group">
                <h4>Support</h4>
                <ul>
                  <li><a href="#help">Help Center</a></li>
                  <li><a href="#docs">Documentation</a></li>
                  <li><a href="#security">Security Policy</a></li>
                  <li><a href="#contact">Contact Support</a></li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="footer-right">
            <div className="system-info">
              <div className="info-item">
                <span className="info-label">Version</span>
                <span className="info-value">v1.7</span>
              </div>
              <div className="info-item">
                <span className="info-label">Environment</span>
                <span className="info-value">Development</span>
              </div>
              <div className="info-item">
                <span className="info-label">Security</span>
                <span className="info-value">AES-256</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="copyright">
              <p>© {currentYear} Defence Research and Development Organisation, Government of India</p>
              <p>All rights reserved. Classified system for authorized personnel only.</p>
            </div>
            <div className="footer-badges">
              <span className="security-badge">🔒 Secure</span>
              <span className="compliance-badge">✅ Compliant</span>
              <span className="version-badge">IDX v1.7</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
