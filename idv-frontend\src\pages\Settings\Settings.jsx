import React, { useState, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import './Settings.css'

const Settings = () => {
  const { theme, setTheme, themeConfig } = useTheme()
  const { user, autoLogin, isDevelopmentMode } = useAuth()
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(5)
  const [language, setLanguage] = useState('EN')
  const [notifications, setNotifications] = useState(true)
  const [dataRetention, setDataRetention] = useState(30)
  const [securityLevel, setSecurityLevel] = useState('STANDARD')

  useEffect(() => {
    // Load saved settings
    const savedSettings = localStorage.getItem('drdo-settings')
    if (savedSettings) {
      const settings = JSON.parse(savedSettings)
      setAutoRefreshInterval(settings.autoRefreshInterval || 5)
      setLanguage(settings.language || 'EN')
      setNotifications(settings.notifications !== false)
      setDataRetention(settings.dataRetention || 30)
      setSecurityLevel(settings.securityLevel || 'STANDARD')
    }

    logAuditEvent('SETTINGS_VIEW', 'Settings page accessed', 'SETTINGS', 'PUBLIC', user?.id, user?.name)
  }, [user])

  const saveSettings = () => {
    const settings = {
      autoRefreshInterval,
      language,
      notifications,
      dataRetention,
      securityLevel,
      lastUpdated: new Date().toISOString()
    }
    
    localStorage.setItem('drdo-settings', JSON.stringify(settings))
    logAuditEvent('SETTINGS_UPDATE', 'Settings updated', 'SETTINGS', 'RESTRICTED', user?.id, user?.name)
    
    // Show success message
    alert('Settings saved successfully!')
  }

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      localStorage.removeItem('drdo-settings')
      setAutoRefreshInterval(5)
      setLanguage('EN')
      setNotifications(true)
      setDataRetention(30)
      setSecurityLevel('STANDARD')
      setTheme('formal-white')
      
      logAuditEvent('SETTINGS_RESET', 'Settings reset to default', 'SETTINGS', 'RESTRICTED', user?.id, user?.name)
      alert('Settings reset to default values!')
    }
  }

  const languages = [
    { code: 'EN', name: 'English', flag: '🇺🇸' },
    { code: 'HI', name: 'हिंदी (Hindi)', flag: '🇮🇳' },
    { code: 'TA', name: 'தமிழ் (Tamil)', flag: '🇮🇳' },
    { code: 'TE', name: 'తెలుగు (Telugu)', flag: '🇮🇳' }
  ]

  const refreshIntervals = [
    { value: 1, label: '1 second (Testing)' },
    { value: 5, label: '5 seconds' },
    { value: 10, label: '10 seconds' },
    { value: 30, label: '30 seconds' },
    { value: 60, label: '1 minute' },
    { value: 300, label: '5 minutes' }
  ]

  return (
    <div className="settings-page">
      <div className="settings-header">
        <h1 className="settings-title">⚙️ System Settings</h1>
        <p className="settings-subtitle">Configure your DRDO IDX Dashboard preferences</p>
      </div>

      <div className="settings-content">
        {/* Theme Settings */}
        <div className="settings-section">
          <h2 className="section-title">🎨 Appearance</h2>
          <div className="setting-group">
            <label className="setting-label">Theme Selection</label>
            <div className="theme-grid">
              {Object.entries(themeConfig).map(([key, config]) => (
                <div 
                  key={key}
                  className={`theme-card ${theme === key ? 'active' : ''}`}
                  onClick={() => setTheme(key)}
                >
                  <div className="theme-preview" style={{ 
                    background: config.primary,
                    border: `2px solid ${config.accent}`
                  }}>
                    <div className="theme-accent" style={{ background: config.accent }}></div>
                  </div>
                  <div className="theme-info">
                    <h4>{config.name}</h4>
                    <p>{config.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Language Settings */}
        <div className="settings-section">
          <h2 className="section-title">🌐 Language & Localization</h2>
          <div className="setting-group">
            <label className="setting-label">Interface Language</label>
            <div className="language-grid">
              {languages.map(lang => (
                <div 
                  key={lang.code}
                  className={`language-card ${language === lang.code ? 'active' : ''}`}
                  onClick={() => setLanguage(lang.code)}
                >
                  <span className="language-flag">{lang.flag}</span>
                  <span className="language-name">{lang.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Real-time Settings */}
        <div className="settings-section">
          <h2 className="section-title">⚡ Real-time Updates</h2>
          <div className="setting-group">
            <label className="setting-label">Auto-refresh Interval</label>
            <select 
              value={autoRefreshInterval} 
              onChange={(e) => setAutoRefreshInterval(Number(e.target.value))}
              className="setting-select"
            >
              {refreshIntervals.map(interval => (
                <option key={interval.value} value={interval.value}>
                  {interval.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="setting-group">
            <label className="setting-label">
              <input 
                type="checkbox" 
                checked={notifications} 
                onChange={(e) => setNotifications(e.target.checked)}
                className="setting-checkbox"
              />
              Enable Real-time Notifications
            </label>
          </div>
        </div>

        {/* Security Settings */}
        <div className="settings-section">
          <h2 className="section-title">🔒 Security & Privacy</h2>
          <div className="setting-group">
            <label className="setting-label">Security Level</label>
            <select 
              value={securityLevel} 
              onChange={(e) => setSecurityLevel(e.target.value)}
              className="setting-select"
            >
              <option value="STANDARD">Standard</option>
              <option value="ENHANCED">Enhanced</option>
              <option value="MAXIMUM">Maximum</option>
            </select>
          </div>
          
          <div className="setting-group">
            <label className="setting-label">Data Retention (days)</label>
            <input 
              type="number" 
              value={dataRetention} 
              onChange={(e) => setDataRetention(Number(e.target.value))}
              min="1" 
              max="365"
              className="setting-input"
            />
          </div>
        </div>

        {/* Development Settings */}
        {isDevelopmentMode && (
          <div className="settings-section development">
            <h2 className="section-title">🛠️ Development Mode</h2>
            <div className="dev-info">
              <p>Development mode is active. Additional options available:</p>
            </div>
            <div className="setting-group">
              <label className="setting-label">Auto-login Role</label>
              <div className="role-buttons">
                <button onClick={() => autoLogin('scientist')} className="role-btn scientist">
                  👨‍🔬 Scientist
                </button>
                <button onClick={() => autoLogin('commander')} className="role-btn commander">
                  🎖️ Commander
                </button>
                <button onClick={() => autoLogin('admin')} className="role-btn admin">
                  👑 Admin
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="settings-actions">
          <button onClick={saveSettings} className="btn-primary">
            💾 Save Settings
          </button>
          <button onClick={resetSettings} className="btn-secondary">
            🔄 Reset to Default
          </button>
        </div>
      </div>

      {/* Settings Footer */}
      <div className="settings-footer">
        <div className="footer-info">
          <span>Current User: {user?.name} ({user?.role})</span>
          <span>Security Level: {user?.clearanceLevel}</span>
          <span>Session: {user ? 'Active' : 'Inactive'}</span>
        </div>
      </div>
    </div>
  )
}

export default Settings
