/* DRDO Navigation Styling - Version 1.5 */
.drdo-navigation {
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
  border-bottom: 2px solid #E5E7EB;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 72px; /* Below header */
  z-index: 999;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-items {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.nav-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.nav-item:not(.disabled):hover {
  transform: translateY(-1px);
}

.nav-item.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.nav-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:not(.disabled):hover .nav-item-content {
  background: rgba(74, 144, 226, 0.1);
  border-color: rgba(74, 144, 226, 0.2);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
}

.nav-item.active .nav-item-content {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #FFFFFF;
  border-color: #4A90E2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.nav-item.active .nav-item-content::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: #F5A623;
  border-radius: 2px 2px 0 0;
}

.nav-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.nav-label {
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
  letter-spacing: 0.3px;
}

.nav-item.active .nav-label {
  color: #FFFFFF;
}

.access-indicator {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
}

/* Tooltip */
.nav-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  z-index: 1000;
  animation: tooltipFadeIn 0.2s ease-out;
}

.tooltip-content {
  background: #1A3C5E;
  color: #FFFFFF;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  text-align: center;
  position: relative;
}

.tooltip-content::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #1A3C5E;
}

.tooltip-content strong {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #F5A623;
}

.tooltip-content p {
  font-size: 12px;
  margin: 0 0 8px 0;
  color: #E2E8F0;
  line-height: 1.4;
}

.required-role {
  font-size: 10px;
  color: #94A3B8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Security Status */
.security-status {
  display: flex;
  align-items: center;
  margin-left: 24px;
}

.security-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(26, 60, 94, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(26, 60, 94, 0.2);
}

.security-icon {
  font-size: 14px;
}

.security-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.security-level {
  font-size: 10px;
  font-weight: 700;
  color: #1A3C5E;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.security-role {
  font-size: 9px;
  color: #6B7280;
  text-transform: capitalize;
}

/* Theme Variations */
[data-theme="dark"] .drdo-navigation {
  background: linear-gradient(135deg, #1F2937 0%, #111827 100%);
  border-bottom-color: #374151;
}

[data-theme="dark"] .nav-item-content {
  color: #F9FAFB;
}

[data-theme="dark"] .nav-item:not(.disabled):hover .nav-item-content {
  background: rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.3);
}

[data-theme="dark"] .security-indicator {
  background: rgba(96, 165, 250, 0.1);
  border-color: rgba(96, 165, 250, 0.2);
}

[data-theme="dark"] .security-level {
  color: #60A5FA;
}

[data-theme="dracula"] .drdo-navigation {
  background: linear-gradient(135deg, #44475A 0%, #282A36 100%);
  border-bottom-color: #6272A4;
}

[data-theme="dracula"] .nav-item-content {
  color: #F8F8F2;
}

[data-theme="dracula"] .nav-item:not(.disabled):hover .nav-item-content {
  background: rgba(80, 250, 123, 0.2);
  border-color: rgba(80, 250, 123, 0.3);
}

[data-theme="dracula"] .nav-item.active .nav-item-content {
  background: linear-gradient(135deg, #50FA7B 0%, #8BE9FD 100%);
  color: #282A36;
}

[data-theme="dracula"] .security-indicator {
  background: rgba(80, 250, 123, 0.1);
  border-color: rgba(80, 250, 123, 0.2);
}

[data-theme="dracula"] .security-level {
  color: #50FA7B;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .nav-items {
    gap: 1px;
  }
  
  .nav-item-content {
    padding: 10px 12px;
  }
  
  .nav-label {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .nav-items {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }
  
  .nav-item-content {
    padding: 8px 12px;
  }
  
  .nav-label {
    display: none;
  }
  
  .nav-icon {
    font-size: 18px;
  }
  
  .security-status {
    margin-left: 0;
  }
  
  .tooltip-content {
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .nav-items {
    gap: 4px;
  }
  
  .nav-item-content {
    padding: 6px 8px;
  }
  
  .nav-icon {
    font-size: 16px;
  }
}
