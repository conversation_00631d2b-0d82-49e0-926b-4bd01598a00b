{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAoCM,SAAS,0CAAgB,KAAyB,EAAE,KAAoB,EAAE,GAAuC;QAEtG;IADhB,IAAI,iBAAC,aAAa,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,gCAAU,EAAE,OAAO,OAAO;IAC3D,IAAI,aAAY,qBAAA,MAAM,IAAI,CAAC,MAAM,cAAjB,yCAAA,mBAAmB,GAAG;IACtC,IAAI,aAAa,QAAQ,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,YAAY;QAC5E,cAAc,IAAI,GAAG;QACrB,cAAc,EAAE,GAAG,CAAA,GAAA,mCAAQ,EAAE,OAAO,MAAM,IAAI,CAAC,SAAS,EAAG;IAC7D;IAEA,OAAO;uBACL;mBACA;IACF;AACF", "sources": ["packages/@react-aria/table/src/useTableCell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getCellId} from './utils';\nimport {GridNode} from '@react-types/grid';\nimport {TableState} from '@react-stately/table';\nimport {useGridCell} from '@react-aria/grid';\n\nexport interface AriaTableCellProps {\n  /** An object representing the table cell. Contains all the relevant information that makes up the row header. */\n  node: GridNode<unknown>,\n  /** Whether the cell is contained in a virtual scroller. */\n  isVirtualized?: boolean,\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean,\n  /**\n   * Handler that is called when a user performs an action on the cell.\n   * Please use onCellAction at the collection level instead.\n   * @deprecated\n   **/\n  onAction?: () => void\n}\n\nexport interface TableCellAria {\n  /** Props for the table cell element. */\n  gridCellProps: DOMAttributes,\n  /** Whether the cell is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a cell in a table.\n * @param props - Props for the cell.\n * @param state - State of the table, as returned by `useTableState`.\n * @param ref - The ref attached to the cell element.\n */\nexport function useTableCell<T>(props: AriaTableCellProps, state: TableState<T>, ref: RefObject<FocusableElement | null>): TableCellAria {\n  let {gridCellProps, isPressed} = useGridCell(props, state, ref);\n  let columnKey = props.node.column?.key;\n  if (columnKey != null && state.collection.rowHeaderColumnKeys.has(columnKey)) {\n    gridCellProps.role = 'rowheader';\n    gridCellProps.id = getCellId(state, props.node.parentKey!, columnKey);\n  }\n\n  return {\n    gridCellProps,\n    isPressed\n  };\n}\n"], "names": [], "version": 3, "file": "useTableCell.main.js.map"}