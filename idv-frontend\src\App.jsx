import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom'
import { ThemeProvider } from './context/ThemeContext'
import { AuthProvider } from './context/AuthContext'
import { RealTimeProvider } from './context/RealTimeContext'
import Layout from './components/Layout/Layout'
import AppRouter from './router/AppRouter'
import Changelog from './components/Changelog/Changelog'
import './App.css'

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <RealTimeProvider>
          <Router>
            <Layout>
              <AppRouter />
              <Changelog />
            </Layout>
          </Router>
        </RealTimeProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
