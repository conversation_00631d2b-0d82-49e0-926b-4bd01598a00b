import CryptoJS from 'crypto-js';

// Mock encryption key - in production, this would be securely managed
const ENCRYPTION_KEY = 'DRDO-IDX-DASHBOARD-2025-SECURE-KEY-AES256';

export type SecurityLevel = 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';

export interface EncryptedData {
  data: string;
  timestamp: string;
  securityLevel: SecurityLevel;
  checksum: string;
}

export class SecurityManager {
  private static instance: SecurityManager;
  
  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  /**
   * Encrypt data with AES-256
   */
  encrypt(data: any, securityLevel: SecurityLevel = 'PUBLIC'): EncryptedData {
    try {
      const jsonString = JSON.stringify(data);
      const timestamp = new Date().toISOString();
      
      // Create checksum for integrity verification
      const checksum = CryptoJS.SHA256(jsonString + timestamp).toString();
      
      // Encrypt the data
      const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
      
      return {
        data: encrypted,
        timestamp,
        securityLevel,
        checksum,
      };
    } catch (error) {
      throw new Error(`Encryption failed: ${error}`);
    }
  }

  /**
   * Decrypt data
   */
  decrypt(encryptedData: EncryptedData): any {
    try {
      // Decrypt the data
      const decryptedBytes = CryptoJS.AES.decrypt(encryptedData.data, ENCRYPTION_KEY);
      const decryptedString = decryptedBytes.toString(CryptoJS.enc.Utf8);
      
      if (!decryptedString) {
        throw new Error('Decryption failed - invalid key or corrupted data');
      }
      
      // Verify checksum
      const expectedChecksum = CryptoJS.SHA256(decryptedString + encryptedData.timestamp).toString();
      if (expectedChecksum !== encryptedData.checksum) {
        throw new Error('Data integrity check failed');
      }
      
      return JSON.parse(decryptedString);
    } catch (error) {
      throw new Error(`Decryption failed: ${error}`);
    }
  }

  /**
   * Encrypt file content
   */
  encryptFile(file: File, securityLevel: SecurityLevel = 'PUBLIC'): Promise<EncryptedData> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const content = event.target?.result as string;
          const fileData = {
            name: file.name,
            type: file.type,
            size: file.size,
            content: content,
            lastModified: file.lastModified,
          };
          
          const encrypted = this.encrypt(fileData, securityLevel);
          resolve(encrypted);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('File reading failed'));
      reader.readAsDataURL(file);
    });
  }

  /**
   * Generate secure hash for data
   */
  generateHash(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  /**
   * Validate security level access
   */
  validateAccess(dataSecurityLevel: SecurityLevel, userClearanceLevel: SecurityLevel): boolean {
    const levels = ['PUBLIC', 'RESTRICTED', 'CONFIDENTIAL', 'SECRET'];
    const dataLevelIndex = levels.indexOf(dataSecurityLevel);
    const userLevelIndex = levels.indexOf(userClearanceLevel);
    
    return userLevelIndex >= dataLevelIndex;
  }

  /**
   * Secure data storage
   */
  secureStore(key: string, data: any, securityLevel: SecurityLevel = 'PUBLIC'): void {
    const encrypted = this.encrypt(data, securityLevel);
    localStorage.setItem(`drdo-secure-${key}`, JSON.stringify(encrypted));
  }

  /**
   * Secure data retrieval
   */
  secureRetrieve(key: string): any {
    try {
      const stored = localStorage.getItem(`drdo-secure-${key}`);
      if (!stored) return null;
      
      const encryptedData: EncryptedData = JSON.parse(stored);
      return this.decrypt(encryptedData);
    } catch (error) {
      console.error('Secure retrieval failed:', error);
      return null;
    }
  }

  /**
   * Clear secure storage
   */
  clearSecureStorage(): void {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('drdo-secure-')) {
        localStorage.removeItem(key);
      }
    });
  }
}

// Export singleton instance
export const securityManager = SecurityManager.getInstance();

// Utility functions
export const encryptData = (data: any, level: SecurityLevel = 'PUBLIC') => 
  securityManager.encrypt(data, level);

export const decryptData = (encryptedData: EncryptedData) => 
  securityManager.decrypt(encryptedData);

export const secureStore = (key: string, data: any, level: SecurityLevel = 'PUBLIC') => 
  securityManager.secureStore(key, data, level);

export const secureRetrieve = (key: string) => 
  securityManager.secureRetrieve(key);

export const validateSecurityAccess = (dataLevel: SecurityLevel, userLevel: SecurityLevel) => 
  securityManager.validateAccess(dataLevel, userLevel);
