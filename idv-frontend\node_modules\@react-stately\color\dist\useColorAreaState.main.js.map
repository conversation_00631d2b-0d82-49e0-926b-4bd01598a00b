{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AA0DD,MAAM,sCAAgB,CAAA,GAAA,oCAAS,EAAE;AAK1B,SAAS,0CAAkB,KAAqB;IACrD,IAAI,SACF,KAAK,gBACL,YAAY,cACZ,UAAU,YACV,QAAQ,YACR,QAAQ,YACR,QAAQ,eACR,WAAW,EACZ,GAAG;IAEJ,IAAI,CAAC,SAAS,CAAC,cACb,eAAe;IAEjB,IAAI,OACF,QAAQ,CAAA,GAAA,wCAAa,EAAE;IAEzB,IAAI,cACF,eAAe,CAAA,GAAA,wCAAa,EAAE;IAGhC,+HAA+H;IAC/H,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,2CAAiB,EAAS,OAAgB,cAAuB;IACnG,IAAI,QAAQ,CAAA,GAAA,oBAAM,EAAE,IAAM,cAAc,aAAa,WAAW,QAAQ,CAAC,cAAc,YAAY;QAAC;QAAY;KAAW;IAC3H,IAAI,WAAW,CAAA,GAAA,mBAAK,EAAE;IACtB,IAAI,WAAW,CAAC;QACd,SAAS,OAAO,GAAG;QACnB,cAAc;IAChB;IAEA,IAAI,WAAW,CAAA,GAAA,oBAAM,EAAE,IACrB,MAAM,iBAAiB,CAAC;sBAAC;sBAAU;QAAQ,IAC3C;QAAC;QAAO;QAAU;KAAS;IAG7B,IAAI,gBAAgB,MAAM,eAAe,CAAC,SAAS,QAAQ;IAC3D,IAAI,gBAAgB,MAAM,eAAe,CAAC,SAAS,QAAQ;IAC3D,IAAI,EAAC,UAAU,SAAS,EAAE,UAAU,SAAS,EAAE,MAAM,KAAK,EAAE,UAAU,SAAS,EAAC,GAAG;IACnF,IAAI,EAAC,UAAU,SAAS,EAAE,UAAU,SAAS,EAAE,MAAM,KAAK,EAAE,UAAU,SAAS,EAAC,GAAG;IAEnF,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,qBAAO,EAAE;IACzC,IAAI,gBAAgB,CAAA,GAAA,mBAAK,EAAE;IAE3B,IAAI,SAAS,MAAM,eAAe,CAAC,SAAS,QAAQ;IACpD,IAAI,SAAS,MAAM,eAAe,CAAC,SAAS,QAAQ;IACpD,IAAI,YAAY,CAAC;QACf,IAAI,MAAM,QACR;QAEF,IAAI,WAAW,MAAM,gBAAgB,CAAC,SAAS,QAAQ,EAAE;QACzD,SAAS;IACX;IACA,IAAI,YAAY,CAAC;QACf,IAAI,MAAM,QACR;QAEF,IAAI,WAAW,MAAM,gBAAgB,CAAC,SAAS,QAAQ,EAAE;QACzD,SAAS;IACX;IAEA,OAAO;kBACL;QACA,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAClB,OAAO;QACP,UAAS,KAAK;YACZ,SAAS,CAAA,GAAA,wCAAa,EAAE;QAC1B;gBACA;mBACA;gBACA;mBACA;QACA,mBAAkB,CAAS,EAAE,CAAS;YACpC,IAAI,YAAY,YAAY,CAAA,GAAA,8BAAI,EAAE,GAAG,GAAG,KAAM,CAAA,YAAY,SAAQ;YAClE,IAAI,YAAY,YAAY,AAAC,CAAA,IAAI,CAAA,GAAA,8BAAI,EAAE,GAAG,GAAG,EAAC,IAAM,CAAA,YAAY,SAAQ;YACxE,IAAI;YACJ,IAAI,cAAc,QAAQ;gBACxB,uEAAuE;gBACvE,YAAY,CAAA,GAAA,wCAAc,EAAE,WAAW,WAAW,WAAW;gBAC7D,WAAW,MAAM,gBAAgB,CAAC,SAAS,QAAQ,EAAE;YACvD;YACA,IAAI,cAAc,QAAQ;gBACxB,uEAAuE;gBACvE,YAAY,CAAA,GAAA,wCAAc,EAAE,WAAW,WAAW,WAAW;gBAC7D,WAAW,AAAC,CAAA,YAAY,KAAI,EAAG,gBAAgB,CAAC,SAAS,QAAQ,EAAE;YACrE;YACA,IAAI,UACF,SAAS;QAEb;QACA;YACE,IAAI,IAAI,AAAC,CAAA,SAAS,SAAQ,IAAM,CAAA,YAAY,SAAQ;YACpD,IAAI,IAAI,IAAI,AAAC,CAAA,SAAS,SAAQ,IAAM,CAAA,YAAY,SAAQ;YACxD,OAAO;mBAAC;mBAAG;YAAC;QACd;QACA,YAAW,WAAW,CAAC;YACrB,UAAU,SAAS,WAAW,YAAY,YAAY,CAAA,GAAA,wCAAc,EAAE,SAAS,UAAU,WAAW,WAAW;QACjH;QACA,YAAW,WAAW,CAAC;YACrB,UAAU,SAAS,WAAW,YAAY,YAAY,CAAA,GAAA,wCAAc,EAAE,SAAS,UAAU,WAAW,WAAW;QACjH;QACA,YAAW,WAAW,CAAC;YACrB,UAAU,CAAA,GAAA,wCAAc,EAAE,SAAS,UAAU,WAAW,WAAW;QACrE;QACA,YAAW,WAAW,CAAC;YACrB,UAAU,CAAA,GAAA,wCAAc,EAAE,SAAS,UAAU,WAAW,WAAW;QACrE;QACA,aAAY,UAAU;YACpB,IAAI,cAAc,cAAc,OAAO;YACvC,cAAc,OAAO,GAAG;YAExB,IAAI,eAAe,CAAC,cAAc,aAChC,YAAY,SAAS,OAAO;YAG9B,YAAY;QACd;oBACA;QACA;YACE,OAAO,MAAM,gBAAgB,CAAC,SAAS;QACzC;IACF;AACF", "sources": ["packages/@react-stately/color/src/useColorAreaState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {clamp, snapValueToStep, useControlledState} from '@react-stately/utils';\nimport {Color, ColorAreaProps, ColorChannel} from '@react-types/color';\nimport {normalizeColor, parseColor} from './Color';\nimport {useMemo, useRef, useState} from 'react';\n\nexport interface ColorAreaState {\n  /** The current color value displayed by the color area. */\n  readonly value: Color,\n  /** Sets the current color value. If a string is passed, it will be parsed to a Color. */\n  setValue(value: string | Color): void,\n\n  /** The current value of the horizontal axis channel displayed by the color area. */\n  xValue: number,\n  /** Sets the value for the horizontal axis channel displayed by the color area, and triggers `onChange`. */\n  setXValue(value: number): void,\n\n  /** The current value of the vertical axis channel displayed by the color area. */\n  yValue: number,\n  /** Sets the value for the vertical axis channel displayed by the color area, and triggers `onChange`. */\n  setYValue(value: number): void,\n\n  /** Sets the x and y channels of the current color value based on a percentage of the width and height of the color area, and triggers `onChange`. */\n  setColorFromPoint(x: number, y: number): void,\n  /** Returns the coordinates of the thumb relative to the upper left corner of the color area as a percentage. */\n  getThumbPosition(): {x: number, y: number},\n\n  /** Increments the value of the horizontal axis channel by the channel step or page amount. */\n  incrementX(stepSize?: number): void,\n  /** Decrements the value of the horizontal axis channel by the channel step or page amount. */\n  decrementX(stepSize?: number): void,\n\n  /** Increments the value of the vertical axis channel by the channel step or page amount. */\n  incrementY(stepSize?: number): void,\n  /** Decrements the value of the vertical axis channel by the channel step or page amount. */\n  decrementY(stepSize?: number): void,\n\n  /** Whether the color area is currently being dragged. */\n  readonly isDragging: boolean,\n  /** Sets whether the color area is being dragged. */\n  setDragging(value: boolean): void,\n\n  /** Returns the xChannel, yChannel and zChannel names based on the color value. */\n  channels: {xChannel: ColorChannel, yChannel: ColorChannel, zChannel: ColorChannel},\n  /** The step value of the xChannel, used when incrementing and decrementing. */\n  xChannelStep: number,\n  /** The step value of the yChannel, used when incrementing and decrementing. */\n  yChannelStep: number,\n  /** The page step value of the xChannel, used when incrementing and decrementing. */\n  xChannelPageStep: number,\n  /** The page step value of the yChannel, used when incrementing and decrementing. */\n  yChannelPageStep: number,\n\n  /** Returns the color that should be displayed in the color area thumb instead of `value`. */\n  getDisplayColor(): Color\n}\n\nconst DEFAULT_COLOR = parseColor('#ffffff');\n/**\n * Provides state management for a color area component.\n * Color area allows users to adjust two channels of an HSL, HSB or RGB color value against a two-dimensional gradient background.\n */\nexport function useColorAreaState(props: ColorAreaProps): ColorAreaState {\n  let {\n    value,\n    defaultValue,\n    colorSpace,\n    xChannel,\n    yChannel,\n    onChange,\n    onChangeEnd\n  } = props;\n\n  if (!value && !defaultValue) {\n    defaultValue = DEFAULT_COLOR;\n  }\n  if (value) {\n    value = normalizeColor(value);\n  }\n  if (defaultValue) {\n    defaultValue = normalizeColor(defaultValue);\n  }\n\n  // safe to cast value and defaultValue to Color, one of them will always be defined because if neither are, we assign a default\n  let [colorValue, setColorState] = useControlledState<Color>(value as Color, defaultValue as Color, onChange);\n  let color = useMemo(() => colorSpace && colorValue ? colorValue.toFormat(colorSpace) : colorValue, [colorValue, colorSpace]);\n  let valueRef = useRef(color);\n  let setColor = (color: Color) => {\n    valueRef.current = color;\n    setColorState(color);\n  };\n\n  let channels = useMemo(() =>\n    color.getColorSpaceAxes({xChannel, yChannel}),\n    [color, xChannel, yChannel]\n  );\n\n  let xChannelRange = color.getChannelRange(channels.xChannel);\n  let yChannelRange = color.getChannelRange(channels.yChannel);\n  let {minValue: minValueX, maxValue: maxValueX, step: stepX, pageSize: pageSizeX} = xChannelRange;\n  let {minValue: minValueY, maxValue: maxValueY, step: stepY, pageSize: pageSizeY} = yChannelRange;\n\n  let [isDragging, setDragging] = useState(false);\n  let isDraggingRef = useRef(false);\n\n  let xValue = color.getChannelValue(channels.xChannel);\n  let yValue = color.getChannelValue(channels.yChannel);\n  let setXValue = (v: number) => {\n    if (v === xValue) {\n      return;\n    }\n    let newColor = color.withChannelValue(channels.xChannel, v);\n    setColor(newColor);\n  };\n  let setYValue = (v: number) => {\n    if (v === yValue) {\n      return;\n    }\n    let newColor = color.withChannelValue(channels.yChannel, v);\n    setColor(newColor);\n  };\n\n  return {\n    channels,\n    xChannelStep: stepX,\n    yChannelStep: stepY,\n    xChannelPageStep: pageSizeX,\n    yChannelPageStep: pageSizeY,\n    value: color,\n    setValue(value) {\n      setColor(normalizeColor(value));\n    },\n    xValue,\n    setXValue,\n    yValue,\n    setYValue,\n    setColorFromPoint(x: number, y: number) {\n      let newXValue = minValueX + clamp(x, 0, 1) * (maxValueX - minValueX);\n      let newYValue = minValueY + (1 - clamp(y, 0, 1)) * (maxValueY - minValueY);\n      let newColor: Color | undefined;\n      if (newXValue !== xValue) {\n        // Round new value to multiple of step, clamp value between min and max\n        newXValue = snapValueToStep(newXValue, minValueX, maxValueX, stepX);\n        newColor = color.withChannelValue(channels.xChannel, newXValue);\n      }\n      if (newYValue !== yValue) {\n        // Round new value to multiple of step, clamp value between min and max\n        newYValue = snapValueToStep(newYValue, minValueY, maxValueY, stepY);\n        newColor = (newColor || color).withChannelValue(channels.yChannel, newYValue);\n      }\n      if (newColor) {\n        setColor(newColor);\n      }\n    },\n    getThumbPosition() {\n      let x = (xValue - minValueX) / (maxValueX - minValueX);\n      let y = 1 - (yValue - minValueY) / (maxValueY - minValueY);\n      return {x, y};\n    },\n    incrementX(stepSize = 1) {\n      setXValue(xValue + stepSize > maxValueX ? maxValueX : snapValueToStep(xValue + stepSize, minValueX, maxValueX, stepX));\n    },\n    incrementY(stepSize = 1) {\n      setYValue(yValue + stepSize > maxValueY ? maxValueY : snapValueToStep(yValue + stepSize, minValueY, maxValueY, stepY));\n    },\n    decrementX(stepSize = 1) {\n      setXValue(snapValueToStep(xValue - stepSize, minValueX, maxValueX, stepX));\n    },\n    decrementY(stepSize = 1) {\n      setYValue(snapValueToStep(yValue - stepSize, minValueY, maxValueY, stepY));\n    },\n    setDragging(isDragging) {\n      let wasDragging = isDraggingRef.current;\n      isDraggingRef.current = isDragging;\n\n      if (onChangeEnd && !isDragging && wasDragging) {\n        onChangeEnd(valueRef.current);\n      }\n\n      setDragging(isDragging);\n    },\n    isDragging,\n    getDisplayColor() {\n      return color.withChannelValue('alpha', 1);\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useColorAreaState.main.js.map"}