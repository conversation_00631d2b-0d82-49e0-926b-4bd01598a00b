.export-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.export-modal {
  background-color: white;
  width: 90%;
  max-width: 600px;
  max-height: 80%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-group > label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.radio-option:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.radio-option input[type="radio"] {
  margin: 0;
}

.radio-option span {
  font-weight: 500;
  color: #333;
}

.radio-option small {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-option input[type="checkbox"] {
  margin: 0;
}

.security-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.security-select:focus {
  outline: none;
  border-color: #007bff;
}

.data-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
}

.data-preview h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.preview-content {
  font-size: 12px;
  color: #666;
}

.preview-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.preview-stats span {
  background-color: white;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #ddd;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.cancel-btn,
.export-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background-color: #545b62;
}

.export-btn {
  background-color: #007bff;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.export-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .export-modal {
    width: 95%;
    max-height: 90%;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 15px;
  }
  
  .preview-stats {
    flex-direction: column;
    gap: 8px;
  }
}
