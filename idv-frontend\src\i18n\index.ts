import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// a json file is not created as of now will be created later
const resources = {
  en: {
    translation: {
      "title": "IDX - Integrated Dashboard for eXcellence"
    }
  },
  hi: {
    translation: {
      "title": "आईडीएक्स - उत्कृष्टता के लिए एकीकृत डैशबोर्ड"
    }
  },
  ta: {
    translation: {
      "title": "ஐடிஎக்ஸ் - சிறப்புக்கான ஒருங்கிணைந்த டாஷ்போர்டு"
    }
  },
  te: {
    translation: {
      "title": "ఐడిఎక్స్ - ఎక్సలెన్స్ కోసం ఇంటిగ్రేటెడ్ డాష్‌బోర్డ్"
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
