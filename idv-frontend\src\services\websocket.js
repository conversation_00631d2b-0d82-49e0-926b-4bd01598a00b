// WebSocket Service for Real-time Updates - DRDO IDX v1.7
import { logAuditEvent } from '../utils/audit'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.listeners = new Map()
    this.isConnected = false
    this.heartbeatInterval = null
    this.messageQueue = []
  }

  // Connect to WebSocket server
  connect(url = 'ws://localhost:8080/ws') {
    try {
      this.ws = new WebSocket(url)
      this.setupEventHandlers()
      logAuditEvent('WEBSOCKET_CONNECT', 'WebSocket connection initiated', 'WEBSOCKET', 'PUBLIC')
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.handleReconnect()
    }
  }

  // Setup WebSocket event handlers
  setupEventHandlers() {
    this.ws.onopen = (event) => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.processMessageQueue()
      this.notifyListeners('connection', { status: 'connected' })
      logAuditEvent('WEBSOCKET_OPEN', 'WebSocket connection established', 'WEBSOCKET', 'PUBLIC')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason)
      this.isConnected = false
      this.stopHeartbeat()
      this.notifyListeners('connection', { status: 'disconnected', code: event.code })
      
      if (event.code !== 1000) { // Not a normal closure
        this.handleReconnect()
      }
      
      logAuditEvent('WEBSOCKET_CLOSE', `WebSocket connection closed: ${event.code}`, 'WEBSOCKET', 'PUBLIC')
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.notifyListeners('error', { error })
      logAuditEvent('WEBSOCKET_ERROR', 'WebSocket error occurred', 'WEBSOCKET', 'RESTRICTED')
    }
  }

  // Handle incoming messages
  handleMessage(data) {
    const { type, payload, timestamp } = data

    switch (type) {
      case 'equipment_update':
        this.notifyListeners('equipment', payload)
        break
      case 'mission_status':
        this.notifyListeners('mission', payload)
        break
      case 'system_alert':
        this.notifyListeners('alert', payload)
        break
      case 'user_activity':
        this.notifyListeners('activity', payload)
        break
      case 'heartbeat':
        this.handleHeartbeat(payload)
        break
      default:
        this.notifyListeners('message', data)
    }

    logAuditEvent('WEBSOCKET_MESSAGE', `Received ${type} message`, 'WEBSOCKET', 'PUBLIC')
  }

  // Send message to server
  send(type, payload) {
    const message = {
      type,
      payload,
      timestamp: new Date().toISOString()
    }

    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
      logAuditEvent('WEBSOCKET_SEND', `Sent ${type} message`, 'WEBSOCKET', 'PUBLIC')
    } else {
      // Queue message for later
      this.messageQueue.push(message)
      console.warn('WebSocket not connected, message queued')
    }
  }

  // Process queued messages
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.ws.send(JSON.stringify(message))
    }
  }

  // Subscribe to specific message types
  subscribe(type, callback) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set())
    }
    this.listeners.get(type).add(callback)

    // Return unsubscribe function
    return () => {
      const typeListeners = this.listeners.get(type)
      if (typeListeners) {
        typeListeners.delete(callback)
        if (typeListeners.size === 0) {
          this.listeners.delete(type)
        }
      }
    }
  }

  // Notify all listeners of a specific type
  notifyListeners(type, data) {
    const typeListeners = this.listeners.get(type)
    if (typeListeners) {
      typeListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in WebSocket listener:', error)
        }
      })
    }
  }

  // Start heartbeat to keep connection alive
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send('heartbeat', { timestamp: Date.now() })
      }
    }, 30000) // Send heartbeat every 30 seconds
  }

  // Stop heartbeat
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  // Handle heartbeat response
  handleHeartbeat(payload) {
    const latency = Date.now() - payload.timestamp
    this.notifyListeners('heartbeat', { latency })
  }

  // Handle reconnection
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval * this.reconnectAttempts)
    } else {
      console.error('Max reconnection attempts reached')
      this.notifyListeners('connection', { status: 'failed' })
    }
  }

  // Disconnect WebSocket
  disconnect() {
    if (this.ws) {
      this.stopHeartbeat()
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
      this.isConnected = false
      logAuditEvent('WEBSOCKET_DISCONNECT', 'WebSocket disconnected by client', 'WEBSOCKET', 'PUBLIC')
    }
  }

  // Get connection status
  getStatus() {
    return {
      connected: this.isConnected,
      readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length
    }
  }

  // Simulate real-time data for development
  simulateRealTimeData() {
    if (!this.isConnected) return

    // Simulate equipment updates
    setInterval(() => {
      const equipmentData = {
        id: `eq_${Math.floor(Math.random() * 6) + 1}`,
        health: Math.floor(Math.random() * 20) + 80,
        temperature: Math.floor(Math.random() * 15) + 35,
        status: Math.random() > 0.1 ? 'operational' : 'warning',
        timestamp: new Date().toISOString()
      }
      this.notifyListeners('equipment', equipmentData)
    }, 5000)

    // Simulate mission updates
    setInterval(() => {
      const missionData = {
        id: `mission_${Math.floor(Math.random() * 3) + 1}`,
        progress: Math.floor(Math.random() * 10) + 85,
        status: ['active', 'standby', 'planning'][Math.floor(Math.random() * 3)],
        timestamp: new Date().toISOString()
      }
      this.notifyListeners('mission', missionData)
    }, 10000)

    // Simulate system alerts
    setInterval(() => {
      if (Math.random() > 0.8) { // 20% chance of alert
        const alertData = {
          level: ['info', 'warning', 'error'][Math.floor(Math.random() * 3)],
          message: 'System notification from simulation',
          source: 'Equipment Monitor',
          timestamp: new Date().toISOString()
        }
        this.notifyListeners('alert', alertData)
      }
    }, 15000)
  }
}

// Create singleton instance
const websocketService = new WebSocketService()

// Auto-connect in development mode
if (process.env.NODE_ENV === 'development') {
  // Use simulation instead of real WebSocket in development
  websocketService.isConnected = true
  websocketService.simulateRealTimeData()
}

export default websocketService
