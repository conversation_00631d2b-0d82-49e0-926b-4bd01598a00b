var $2d805fd52bb48883$exports = {};
$2d805fd52bb48883$exports = {
    "alpha": `\u{623}\u{644}\u{641}\u{627}`,
    "black": `\u{623}\u{633}\u{648}\u{62F}`,
    "blue": `\u{623}\u{632}\u{631}\u{642}`,
    "blue purple": `\u{623}\u{631}\u{62C}\u{648}\u{627}\u{646}\u{64A} \u{645}\u{632}\u{631}\u{642}`,
    "brightness": `\u{627}\u{644}\u{633}\u{637}\u{648}\u{639}`,
    "brown": `\u{628}\u{646}\u{64A}`,
    "brown yellow": `\u{623}\u{635}\u{641}\u{631} \u{628}\u{646}\u{64A}`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `\u{633}\u{645}\u{627}\u{648}\u{64A}`,
    "cyan blue": `\u{623}\u{632}\u{631}\u{642} \u{633}\u{645}\u{627}\u{648}\u{64A}`,
    "dark": `\u{62F}\u{627}\u{643}\u{646}`,
    "gray": `\u{631}\u{645}\u{627}\u{62F}\u{64A}`,
    "grayish": `\u{645}\u{627}\u{626}\u{644} \u{644}\u{644}\u{631}\u{645}\u{627}\u{62F}\u{64A}`,
    "green": `\u{623}\u{62E}\u{636}\u{631}`,
    "green cyan": `\u{633}\u{645}\u{627}\u{648}\u{64A} \u{645}\u{62E}\u{636}\u{631}`,
    "hue": `\u{62F}\u{631}\u{62C}\u{629} \u{627}\u{644}\u{644}\u{648}\u{646}`,
    "light": `\u{641}\u{627}\u{62A}\u{62D}`,
    "lightness": `\u{627}\u{644}\u{625}\u{636}\u{627}\u{621}\u{629}`,
    "magenta": `\u{623}\u{631}\u{62C}\u{648}\u{627}\u{646}\u{64A}`,
    "magenta pink": `\u{623}\u{631}\u{62C}\u{648}\u{627}\u{646}\u{64A} \u{648}\u{631}\u{62F}\u{64A}`,
    "orange": `\u{628}\u{631}\u{62A}\u{642}\u{627}\u{644}\u{64A}`,
    "orange yellow": `\u{623}\u{635}\u{641}\u{631} \u{628}\u{631}\u{62A}\u{642}\u{627}\u{644}\u{64A}`,
    "pale": `\u{628}\u{627}\u{647}\u{62A}`,
    "pink": `\u{648}\u{631}\u{62F}\u{64A}`,
    "pink red": `\u{623}\u{62D}\u{645}\u{631} \u{648}\u{631}\u{62F}\u{64A}`,
    "purple": `\u{623}\u{631}\u{62C}\u{648}\u{627}\u{646}\u{64A}`,
    "purple magenta": `\u{628}\u{646}\u{641}\u{633}\u{62C}\u{64A}`,
    "red": `\u{623}\u{62D}\u{645}\u{631}`,
    "red orange": `\u{628}\u{631}\u{62A}\u{642}\u{627}\u{644}\u{64A} \u{645}\u{62D}\u{645}\u{631}`,
    "saturation": `\u{627}\u{644}\u{62A}\u{634}\u{628}\u{639}`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} \u{634}\u{641}\u{627}\u{641}`,
    "very dark": `\u{62F}\u{627}\u{643}\u{646} \u{62C}\u{62F}\u{64B}\u{627}`,
    "very light": `\u{641}\u{627}\u{62A}\u{62D} \u{62C}\u{62F}\u{64B}\u{627}`,
    "vibrant": `\u{633}\u{627}\u{637}\u{639}`,
    "white": `\u{623}\u{628}\u{64A}\u{636}`,
    "yellow": `\u{623}\u{635}\u{641}\u{631}`,
    "yellow green": `\u{623}\u{62E}\u{636}\u{631} \u{645}\u{635}\u{641}\u{631}`
};


export {$2d805fd52bb48883$exports as default};
//# sourceMappingURL=ar-AE.module.js.map
