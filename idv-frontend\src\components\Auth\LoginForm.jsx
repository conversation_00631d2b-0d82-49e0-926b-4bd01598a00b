import React, { useState } from 'react'
import { useAuth } from '../../context/AuthContext'
import { t } from '../../utils/i18n'
import './LoginForm.css'

const LoginForm = ({ onClose }) => {
  const { login } = useAuth()
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const success = await login(credentials)
      if (success) {
        onClose()
      } else {
        setError('Invalid credentials. Please try again.')
      }
    } catch (err) {
      setError('Lo<PERSON> failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDemoLogin = (role) => {
    const demoCredentials = {
      admin: { email: '<EMAIL>', password: 'drdo123' },
      scientist: { email: '<EMAIL>', password: 'drdo123' },
      engineer: { email: '<EMAIL>', password: 'drdo123' }
    }

    setCredentials(demoCredentials[role])
  }

  return (
    <div className="login-overlay">
      <div className="login-modal">
        <div className="login-header">
          <div className="drdo-branding">
            <div className="drdo-emblem">🇮🇳</div>
            <div className="login-title">
              <h2>DRDO IDX Dashboard</h2>
              <p>Secure Access Portal</p>
            </div>
          </div>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              value={credentials.email}
              onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
              placeholder="<EMAIL>"
              required
              disabled={isLoading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={credentials.password}
              onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
              placeholder="Enter your password"
              required
              disabled={isLoading}
            />
          </div>

          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          <button
            type="submit"
            className="login-btn"
            disabled={isLoading || !credentials.email || !credentials.password}
          >
            {isLoading ? 'Authenticating...' : 'Secure Login'}
          </button>
        </form>

        <div className="demo-section">
          <div className="demo-header">
            <span>Demo Access</span>
            <small>For testing purposes</small>
          </div>
          <div className="demo-buttons">
            <button
              type="button"
              className="demo-btn admin"
              onClick={() => handleDemoLogin('admin')}
              disabled={isLoading}
            >
              👨‍💼 Admin Access
              <small>Full system access</small>
            </button>
            <button
              type="button"
              className="demo-btn scientist"
              onClick={() => handleDemoLogin('scientist')}
              disabled={isLoading}
            >
              👩‍🔬 Scientist Access
              <small>Research & analysis</small>
            </button>
            <button
              type="button"
              className="demo-btn engineer"
              onClick={() => handleDemoLogin('engineer')}
              disabled={isLoading}
            >
              👨‍🔧 Engineer Access
              <small>Technical operations</small>
            </button>
          </div>
        </div>

        <div className="security-notice">
          <div className="security-icon">🔒</div>
          <div className="security-text">
            <strong>Security Notice</strong>
            <p>This is a demonstration system. All data is encrypted and stored locally for testing purposes only.</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginForm
