{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,+DAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,6JAAyB,EAAE,KAAK,UAAU,CAAC,iIAAqB,CAAC;IAC7F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,8BAAM,CAAC;IAC7C,cAAc,CAAC,+DAAS,CAAC;IACzB,kBAAkB,CAAC,OAAS,CAAC,6JAAyB,EAAE,KAAK,UAAU,CAAC,iIAAqB,CAAC;IAC9F,sBAAsB,CAAC,6NAAyC,CAAC;IACjE,UAAU,CAAC,iDAAO,CAAC;IACnB,aAAa,CAAC,uEAAW,CAAC;IAC1B,YAAY,CAAC,6IAAqB,CAAC;AACrC", "sources": ["packages/@react-aria/table/intl/uk-UA.json"], "sourcesContent": ["{\n  \"ascending\": \"висхідний\",\n  \"ascendingSort\": \"відсортовано за стовпцем {columnName} у висхідному порядку\",\n  \"columnSize\": \"{value} пікс.\",\n  \"descending\": \"низхідний\",\n  \"descendingSort\": \"відсортовано за стовпцем {columnName} у низхідному порядку\",\n  \"resizerDescription\": \"Натисніть Enter, щоб почати зміну розміру\",\n  \"select\": \"Вибрати\",\n  \"selectAll\": \"Вибрати все\",\n  \"sortable\": \"сортувальний стовпець\"\n}\n"], "names": [], "version": 3, "file": "uk-UA.module.js.map"}