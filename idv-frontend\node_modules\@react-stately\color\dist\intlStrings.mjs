import $7n5uc$arAEmodulejs from "./ar-AE.mjs";
import $7n5uc$bgBGmodulejs from "./bg-BG.mjs";
import $7n5uc$csCZmodulejs from "./cs-CZ.mjs";
import $7n5uc$daDKmodulejs from "./da-DK.mjs";
import $7n5uc$deDEmodulejs from "./de-DE.mjs";
import $7n5uc$elGRmodulejs from "./el-GR.mjs";
import $7n5uc$enUSmodulejs from "./en-US.mjs";
import $7n5uc$esESmodulejs from "./es-ES.mjs";
import $7n5uc$etEEmodulejs from "./et-EE.mjs";
import $7n5uc$fiFImodulejs from "./fi-FI.mjs";
import $7n5uc$frFRmodulejs from "./fr-FR.mjs";
import $7n5uc$heILmodulejs from "./he-IL.mjs";
import $7n5uc$hrHRmodulejs from "./hr-HR.mjs";
import $7n5uc$huHUmodulejs from "./hu-HU.mjs";
import $7n5uc$itITmodulejs from "./it-IT.mjs";
import $7n5uc$jaJPmodulejs from "./ja-JP.mjs";
import $7n5uc$koKRmodulejs from "./ko-KR.mjs";
import $7n5uc$ltLTmodulejs from "./lt-LT.mjs";
import $7n5uc$lvLVmodulejs from "./lv-LV.mjs";
import $7n5uc$nbNOmodulejs from "./nb-NO.mjs";
import $7n5uc$nlNLmodulejs from "./nl-NL.mjs";
import $7n5uc$plPLmodulejs from "./pl-PL.mjs";
import $7n5uc$ptBRmodulejs from "./pt-BR.mjs";
import $7n5uc$ptPTmodulejs from "./pt-PT.mjs";
import $7n5uc$roROmodulejs from "./ro-RO.mjs";
import $7n5uc$ruRUmodulejs from "./ru-RU.mjs";
import $7n5uc$skSKmodulejs from "./sk-SK.mjs";
import $7n5uc$slSImodulejs from "./sl-SI.mjs";
import $7n5uc$srSPmodulejs from "./sr-SP.mjs";
import $7n5uc$svSEmodulejs from "./sv-SE.mjs";
import $7n5uc$trTRmodulejs from "./tr-TR.mjs";
import $7n5uc$ukUAmodulejs from "./uk-UA.mjs";
import $7n5uc$zhCNmodulejs from "./zh-CN.mjs";
import $7n5uc$zhTWmodulejs from "./zh-TW.mjs";

var $f98dad178a72c00d$exports = {};


































$f98dad178a72c00d$exports = {
    "ar-AE": $7n5uc$arAEmodulejs,
    "bg-BG": $7n5uc$bgBGmodulejs,
    "cs-CZ": $7n5uc$csCZmodulejs,
    "da-DK": $7n5uc$daDKmodulejs,
    "de-DE": $7n5uc$deDEmodulejs,
    "el-GR": $7n5uc$elGRmodulejs,
    "en-US": $7n5uc$enUSmodulejs,
    "es-ES": $7n5uc$esESmodulejs,
    "et-EE": $7n5uc$etEEmodulejs,
    "fi-FI": $7n5uc$fiFImodulejs,
    "fr-FR": $7n5uc$frFRmodulejs,
    "he-IL": $7n5uc$heILmodulejs,
    "hr-HR": $7n5uc$hrHRmodulejs,
    "hu-HU": $7n5uc$huHUmodulejs,
    "it-IT": $7n5uc$itITmodulejs,
    "ja-JP": $7n5uc$jaJPmodulejs,
    "ko-KR": $7n5uc$koKRmodulejs,
    "lt-LT": $7n5uc$ltLTmodulejs,
    "lv-LV": $7n5uc$lvLVmodulejs,
    "nb-NO": $7n5uc$nbNOmodulejs,
    "nl-NL": $7n5uc$nlNLmodulejs,
    "pl-PL": $7n5uc$plPLmodulejs,
    "pt-BR": $7n5uc$ptBRmodulejs,
    "pt-PT": $7n5uc$ptPTmodulejs,
    "ro-RO": $7n5uc$roROmodulejs,
    "ru-RU": $7n5uc$ruRUmodulejs,
    "sk-SK": $7n5uc$skSKmodulejs,
    "sl-SI": $7n5uc$slSImodulejs,
    "sr-SP": $7n5uc$srSPmodulejs,
    "sv-SE": $7n5uc$svSEmodulejs,
    "tr-TR": $7n5uc$trTRmodulejs,
    "uk-UA": $7n5uc$ukUAmodulejs,
    "zh-CN": $7n5uc$zhCNmodulejs,
    "zh-TW": $7n5uc$zhTWmodulejs
};


export {$f98dad178a72c00d$exports as default};
//# sourceMappingURL=intlStrings.module.js.map
