module.exports = {
    "ascending": `\u{432}\u{43E}\u{437}\u{440}\u{430}\u{441}\u{442}\u{430}\u{43D}\u{438}\u{435}`,
    "ascendingSort": (args)=>`\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{43E}\u{432}\u{430}\u{442}\u{44C} \u{441}\u{442}\u{43E}\u{43B}\u{431}\u{435}\u{446} ${args.columnName} \u{432} \u{43F}\u{43E}\u{440}\u{44F}\u{434}\u{43A}\u{435} \u{432}\u{43E}\u{437}\u{440}\u{430}\u{441}\u{442}\u{430}\u{43D}\u{438}\u{44F}`,
    "columnSize": (args)=>`${args.value} \u{43F}\u{438}\u{43A}\u{441}.`,
    "descending": `\u{443}\u{431}\u{44B}\u{432}\u{430}\u{43D}\u{438}\u{435}`,
    "descendingSort": (args)=>`\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{43E}\u{432}\u{430}\u{442}\u{44C} \u{441}\u{442}\u{43E}\u{43B}\u{431}\u{435}\u{446} ${args.columnName} \u{432} \u{43F}\u{43E}\u{440}\u{44F}\u{434}\u{43A}\u{435} \u{443}\u{431}\u{44B}\u{432}\u{430}\u{43D}\u{438}\u{44F}`,
    "resizerDescription": `\u{41D}\u{430}\u{436}\u{43C}\u{438}\u{442}\u{435} \u{43A}\u{43B}\u{430}\u{432}\u{438}\u{448}\u{443} Enter \u{434}\u{43B}\u{44F} \u{43D}\u{430}\u{447}\u{430}\u{43B}\u{430} \u{438}\u{437}\u{43C}\u{435}\u{43D}\u{435}\u{43D}\u{438}\u{44F} \u{440}\u{430}\u{437}\u{43C}\u{435}\u{440}\u{43E}\u{432}`,
    "select": `\u{412}\u{44B}\u{431}\u{440}\u{430}\u{442}\u{44C}`,
    "selectAll": `\u{412}\u{44B}\u{431}\u{440}\u{430}\u{442}\u{44C} \u{432}\u{441}\u{435}`,
    "sortable": `\u{441}\u{43E}\u{440}\u{442}\u{438}\u{440}\u{443}\u{435}\u{43C}\u{44B}\u{439} \u{441}\u{442}\u{43E}\u{43B}\u{431}\u{435}\u{446}`
};


//# sourceMappingURL=ru-RU.main.js.map
