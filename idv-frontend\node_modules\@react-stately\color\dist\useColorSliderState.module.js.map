{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AA6BM,SAAS,0CAAoB,KAA8B;IAChE,IAAI,WAAC,OAAO,cAAE,UAAU,SAAE,KAAK,gBAAE,YAAY,YAAE,QAAQ,UAAE,MAAM,EAAE,GAAG,YAAW,GAAG;IAClF,IAAI,SAAS,QAAQ,gBAAgB,MACnC,MAAM,IAAI,MAAM;IAGlB,IAAI,OACF,QAAQ,CAAA,GAAA,yCAAa,EAAE;IAEzB,IAAI,cACF,eAAe,CAAA,GAAA,yCAAa,EAAE;IAEhC,6HAA6H;IAC7H,IAAI,CAAC,YAAY,SAAS,GAAG,CAAA,GAAA,yBAAiB,EAAS,OAAgB,cAAuB;IAC9F,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE,IAAM,cAAc,aAAa,WAAW,QAAQ,CAAC,cAAc,YAAY;QAAC;QAAY;KAAW;IAC3H,IAAI,cAAc,CAAA,GAAA,qBAAa,EAAE;QAC/B,GAAG,MAAM,eAAe,CAAC,QAAQ;QACjC,GAAG,UAAU;QACb,sHAAsH;QACtH,aAAa;QACb,iBAAiB;QACjB,OAAO,MAAM,eAAe,CAAC;QAC7B,UAAS,CAAC;YACR,SAAS,MAAM,gBAAgB,CAAC,SAAS;QAC3C;QACA,aAAY,CAAC;YACX,mGAAmG;YACnG,IAAI,MAAM,WAAW,EACnB,MAAM,WAAW,CAAC,MAAM,gBAAgB,CAAC,SAAS;QAEtD;IACF;IAEA,IAAI,QAAC,IAAI,YAAE,QAAQ,EAAC,GAAG,MAAM,eAAe,CAAC;IAC7C,OAAO;QACL,GAAG,WAAW;QACd,OAAO;QACP,UAAS,KAAK;YACZ,SAAS,CAAA,GAAA,yCAAa,EAAE;QAC1B;QACA;YACE,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAA,GAAA,wCAAS,EAAE,CAAC,IAAI,EAAE,MAAM,eAAe,CAAC,OAAO,YAAY,CAAC;gBACrE,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO,MAAM,gBAAgB,CAAC,SAAS;gBACzC,KAAK;oBACH,OAAO;gBAET;oBACE,MAAM,IAAI,MAAM,4BAA4B;YAChD;QACF;QACA;YACE,OAAO,MAAM,kBAAkB,CAAC,SAAS;QAC3C;cACA;kBACA;QACA,YAAY,YAAY,eAAe,CAAC;IAC1C;AACF", "sources": ["packages/@react-stately/color/src/useColorSliderState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Color, ColorSliderProps} from '@react-types/color';\nimport {normalizeColor, parseColor} from './Color';\nimport {SliderState, useSliderState} from '@react-stately/slider';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo} from 'react';\n\nexport interface ColorSliderState extends SliderState {\n  /** The current color value represented by the color slider. */\n  readonly value: Color,\n  /** Sets the current color value. If a string is passed, it will be parsed to a Color. */\n  setValue(value: string | Color): void,\n  /** Returns the color that should be displayed in the slider instead of `value` or the optional parameter. */\n  getDisplayColor(): Color,\n  /** Whether the color slider is currently being dragged. */\n  readonly isDragging: boolean\n}\n\n\nexport interface ColorSliderStateOptions extends ColorSliderProps {\n  /** The locale to use for formatting the color channel value. */\n  locale: string\n}\n\n/**\n * Provides state management for a color slider component.\n * Color sliders allow users to adjust an individual channel of a color value.\n */\nexport function useColorSliderState(props: ColorSliderStateOptions): ColorSliderState {\n  let {channel, colorSpace, value, defaultValue, onChange, locale, ...otherProps} = props;\n  if (value == null && defaultValue == null) {\n    throw new Error('useColorSliderState requires a value or defaultValue');\n  }\n\n  if (value) {\n    value = normalizeColor(value);\n  }\n  if (defaultValue) {\n    defaultValue = normalizeColor(defaultValue);\n  }\n  // safe to cast value and defaultValue to Color, one of them will always be defined because if neither are, we throw an error\n  let [colorValue, setColor] = useControlledState<Color>(value as Color, defaultValue as Color, onChange);\n  let color = useMemo(() => colorSpace && colorValue ? colorValue.toFormat(colorSpace) : colorValue, [colorValue, colorSpace]);\n  let sliderState = useSliderState({\n    ...color.getChannelRange(channel),\n    ...otherProps,\n    // Unused except in getThumbValueLabel, which is overridden below. null to localize the TypeScript error for ignoring.\n    // @ts-ignore\n    numberFormatter: null,\n    value: color.getChannelValue(channel),\n    onChange(v) {\n      setColor(color.withChannelValue(channel, v));\n    },\n    onChangeEnd(v) {\n      // onChange will have already been called with the right value, this is just to trigger onChangeEnd\n      if (props.onChangeEnd) {\n        props.onChangeEnd(color.withChannelValue(channel, v));\n      }\n    }\n  });\n\n  let {step, pageSize} = color.getChannelRange(channel);\n  return {\n    ...sliderState,\n    value: color,\n    setValue(value) {\n      setColor(normalizeColor(value));\n    },\n    getDisplayColor() {\n      switch (channel) {\n        case 'hue':\n          return parseColor(`hsl(${color.getChannelValue('hue')}, 100%, 50%)`);\n        case 'lightness':\n        case 'brightness':\n        case 'saturation':\n        case 'red':\n        case 'green':\n        case 'blue':\n          return color.withChannelValue('alpha', 1);\n        case 'alpha': {\n          return color;\n        }\n        default:\n          throw new Error('Unknown color channel: ' + channel);\n      }\n    },\n    getThumbValueLabel() {\n      return color.formatChannelValue(channel, locale);\n    },\n    step,\n    pageSize,\n    isDragging: sliderState.isThumbDragging(0)\n  };\n}\n"], "names": [], "version": 3, "file": "useColorSliderState.module.js.map"}