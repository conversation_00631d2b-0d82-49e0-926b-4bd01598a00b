{"version": 3, "sources": ["../../d3-array/src/ascending.js", "../../d3-array/src/descending.js", "../../d3-array/src/bisector.js", "../../d3-array/src/number.js", "../../d3-array/src/bisect.js", "../../d3-array/src/blur.js", "../../d3-array/src/count.js", "../../d3-array/src/cross.js", "../../d3-array/src/cumsum.js", "../../d3-array/src/variance.js", "../../d3-array/src/deviation.js", "../../d3-array/src/extent.js", "../../d3-array/src/fsum.js", "../../internmap/src/index.js", "../../d3-array/src/identity.js", "../../d3-array/src/group.js", "../../d3-array/src/permute.js", "../../d3-array/src/sort.js", "../../d3-array/src/groupSort.js", "../../d3-array/src/ticks.js", "../../d3-array/src/nice.js", "../../d3-array/src/threshold/sturges.js", "../../d3-array/src/array.js", "../../d3-array/src/constant.js", "../../d3-array/src/bin.js", "../../d3-array/src/max.js", "../../d3-array/src/maxIndex.js", "../../d3-array/src/min.js", "../../d3-array/src/minIndex.js", "../../d3-array/src/quickselect.js", "../../d3-array/src/greatest.js", "../../d3-array/src/quantile.js", "../../d3-array/src/threshold/freedmanDiaconis.js", "../../d3-array/src/threshold/scott.js", "../../d3-array/src/mean.js", "../../d3-array/src/median.js", "../../d3-array/src/merge.js", "../../d3-array/src/mode.js", "../../d3-array/src/pairs.js", "../../d3-array/src/range.js", "../../d3-array/src/rank.js", "../../d3-array/src/least.js", "../../d3-array/src/leastIndex.js", "../../d3-array/src/greatestIndex.js", "../../d3-array/src/scan.js", "../../d3-array/src/shuffle.js", "../../d3-array/src/sum.js", "../../d3-array/src/transpose.js", "../../d3-array/src/zip.js", "../../d3-array/src/every.js", "../../d3-array/src/some.js", "../../d3-array/src/filter.js", "../../d3-array/src/map.js", "../../d3-array/src/reduce.js", "../../d3-array/src/reverse.js", "../../d3-array/src/difference.js", "../../d3-array/src/disjoint.js", "../../d3-array/src/intersection.js", "../../d3-array/src/superset.js", "../../d3-array/src/subset.js", "../../d3-array/src/union.js", "../../d3-color/src/define.js", "../../d3-color/src/color.js", "../../d3-color/src/math.js", "../../d3-color/src/lab.js", "../../d3-color/src/cubehelix.js", "../../d3-interpolate/src/basis.js", "../../d3-interpolate/src/basisClosed.js", "../../d3-interpolate/src/constant.js", "../../d3-interpolate/src/color.js", "../../d3-interpolate/src/rgb.js", "../../d3-interpolate/src/numberArray.js", "../../d3-interpolate/src/date.js", "../../d3-interpolate/src/number.js", "../../d3-interpolate/src/object.js", "../../d3-interpolate/src/string.js", "../../d3-interpolate/src/value.js", "../../d3-interpolate/src/array.js", "../../d3-interpolate/src/discrete.js", "../../d3-interpolate/src/hue.js", "../../d3-interpolate/src/round.js", "../../d3-interpolate/src/transform/decompose.js", "../../d3-interpolate/src/transform/parse.js", "../../d3-interpolate/src/transform/index.js", "../../d3-interpolate/src/zoom.js", "../../d3-interpolate/src/hsl.js", "../../d3-interpolate/src/lab.js", "../../d3-interpolate/src/hcl.js", "../../d3-interpolate/src/cubehelix.js", "../../d3-interpolate/src/piecewise.js", "../../d3-interpolate/src/quantize.js", "../../d3-path/src/path.js", "../../d3-format/src/formatSpecifier.js", "../../d3-format/src/formatDecimal.js", "../../d3-format/src/exponent.js", "../../d3-format/src/formatGroup.js", "../../d3-format/src/formatNumerals.js", "../../d3-format/src/formatTrim.js", "../../d3-format/src/formatPrefixAuto.js", "../../d3-format/src/formatRounded.js", "../../d3-format/src/formatTypes.js", "../../d3-format/src/identity.js", "../../d3-format/src/locale.js", "../../d3-format/src/defaultLocale.js", "../../d3-format/src/precisionFixed.js", "../../d3-format/src/precisionPrefix.js", "../../d3-format/src/precisionRound.js", "../../d3-scale/src/init.js", "../../d3-scale/src/ordinal.js", "../../d3-scale/src/band.js", "../../d3-scale/src/tickFormat.js", "../../d3-scale/src/constant.js", "../../d3-scale/src/number.js", "../../d3-scale/src/continuous.js", "../../d3-scale/src/linear.js", "../../d3-scale/src/identity.js", "../../d3-scale/src/nice.js", "../../d3-scale/src/log.js", "../../d3-scale/src/symlog.js", "../../d3-scale/src/pow.js", "../../d3-scale/src/radial.js", "../../d3-scale/src/quantile.js", "../../d3-scale/src/quantize.js", "../../d3-scale/src/threshold.js", "../../d3-time/src/interval.js", "../../d3-time/src/millisecond.js", "../../d3-time/src/duration.js", "../../d3-time/src/second.js", "../../d3-time/src/minute.js", "../../d3-time/src/hour.js", "../../d3-time/src/day.js", "../../d3-time/src/week.js", "../../d3-time/src/month.js", "../../d3-time/src/year.js", "../../d3-time/src/ticks.js", "../../d3-time-format/src/locale.js", "../../d3-time-format/src/defaultLocale.js", "../../d3-time-format/src/isoFormat.js", "../../d3-time-format/src/isoParse.js", "../../d3-scale/src/time.js", "../../d3-scale/src/utcTime.js", "../../d3-scale/src/sequential.js", "../../d3-scale/src/sequentialQuantile.js", "../../d3-scale/src/diverging.js", "../../d3-shape/src/constant.js", "../../d3-shape/src/math.js", "../../d3-shape/src/path.js", "../../d3-shape/src/arc.js", "../../d3-shape/src/curve/linear.js", "../../d3-shape/src/array.js", "../../d3-shape/src/point.js", "../../d3-shape/src/line.js", "../../d3-shape/src/area.js", "../../d3-shape/src/descending.js", "../../d3-shape/src/identity.js", "../../d3-shape/src/pie.js", "../../d3-shape/src/curve/radial.js", "../../d3-shape/src/lineRadial.js", "../../d3-shape/src/areaRadial.js", "../../d3-shape/src/pointRadial.js", "../../d3-shape/src/curve/bump.js", "../../d3-shape/src/link.js", "../../d3-shape/src/symbol/asterisk.js", "../../d3-shape/src/symbol/circle.js", "../../d3-shape/src/symbol/cross.js", "../../d3-shape/src/symbol/diamond.js", "../../d3-shape/src/symbol/diamond2.js", "../../d3-shape/src/symbol/plus.js", "../../d3-shape/src/symbol/square.js", "../../d3-shape/src/symbol/square2.js", "../../d3-shape/src/symbol/star.js", "../../d3-shape/src/symbol/triangle.js", "../../d3-shape/src/symbol/triangle2.js", "../../d3-shape/src/symbol/wye.js", "../../d3-shape/src/symbol/times.js", "../../d3-shape/src/symbol.js", "../../d3-shape/src/curve/basis.js", "../../d3-shape/src/noop.js", "../../d3-shape/src/curve/basisClosed.js", "../../d3-shape/src/curve/basisOpen.js", "../../d3-shape/src/curve/bundle.js", "../../d3-shape/src/curve/cardinal.js", "../../d3-shape/src/curve/cardinalClosed.js", "../../d3-shape/src/curve/cardinalOpen.js", "../../d3-shape/src/curve/catmullRom.js", "../../d3-shape/src/curve/catmullRomClosed.js", "../../d3-shape/src/curve/catmullRomOpen.js", "../../d3-shape/src/curve/linearClosed.js", "../../d3-shape/src/curve/monotone.js", "../../d3-shape/src/curve/natural.js", "../../d3-shape/src/curve/step.js", "../../d3-shape/src/offset/none.js", "../../d3-shape/src/order/none.js", "../../d3-shape/src/stack.js", "../../d3-shape/src/offset/expand.js", "../../d3-shape/src/offset/diverging.js", "../../d3-shape/src/offset/silhouette.js", "../../d3-shape/src/offset/wiggle.js", "../../d3-shape/src/order/appearance.js", "../../d3-shape/src/order/ascending.js", "../../d3-shape/src/order/descending.js", "../../d3-shape/src/order/insideOut.js", "../../d3-shape/src/order/reverse.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export function blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nexport const blur2 = Blur2(blurf);\n\nexport const blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n", "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n", "function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n", "export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}", "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n", "import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n", "export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "export default function identity(x) {\n  return x;\n}\n", "import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n", "export default function permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n", "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nexport function compareDefined(compare = ascending) {\n  if (compare === ascending) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nexport function ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n", "import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n", "const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n", "import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n", "import count from \"../count.js\";\n\nexport default function thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log(count(values)) / Math.LN2) + 1);\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "export default function constant(x) {\n  return () => x;\n}\n", "import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function bin() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n", "import {ascendingDefined, compareDefined} from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n", "import max from \"./max.js\";\nimport maxIndex from \"./maxIndex.js\";\nimport min from \"./min.js\";\nimport minIndex from \"./minIndex.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\nimport {ascendingDefined} from \"./sort.js\";\nimport greatest from \"./greatest.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileIndex(values, p, valueof = number) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => number(valueof(values[i], i, values)));\n  if (p <= 0) return minIndex(numbers);\n  if (p >= 1) return maxIndex(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  quickselect(index, i, 0, j, (i, j) => ascendingDefined(numbers[i], numbers[j]));\n  i = greatest(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n", "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function thresholdFreedmanDiaconis(values, min, max) {\n  const c = count(values), d = quantile(values, 0.75) - quantile(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n", "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function thresholdScott(values, min, max) {\n  const c = count(values), d = deviation(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "import quantile, {quantileIndex} from \"./quantile.js\";\n\nexport default function median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n\nexport function medianIndex(values, valueof) {\n  return quantileIndex(values, 0.5, valueof);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "import {InternMap} from \"internmap\";\n\nexport default function mode(values, valueof) {\n  const counts = new InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n", "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n", "export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import ascending from \"./ascending.js\";\nimport {ascendingDefined, compareDefined} from \"./sort.js\";\n\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n", "import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n", "export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import min from \"./min.js\";\n\nexport default function transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n", "import transpose from \"./transpose.js\";\n\nexport default function zip() {\n  return transpose(arguments);\n}\n", "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n", "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n", "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n", "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n", "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}\n", "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export const radians = Math.PI / 180;\nexport const degrees = 180 / Math.PI;\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\n// https://observablehq.com/@mbostock/lab-and-rgb\nconst K = 18,\n    Xn = 0.96422,\n    Yn = 1,\n    Zn = 0.82521,\n    t0 = 4 / 29,\n    t1 = 6 / 29,\n    t2 = 3 * t1 * t1,\n    t3 = t1 * t1 * t1;\n\nfunction labConvert(o) {\n  if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);\n  if (o instanceof Hcl) return hcl2lab(o);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = rgb2lrgb(o.r),\n      g = rgb2lrgb(o.g),\n      b = rgb2lrgb(o.b),\n      y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;\n  if (r === g && g === b) x = z = y; else {\n    x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);\n    z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);\n  }\n  return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);\n}\n\nexport function gray(l, opacity) {\n  return new Lab(l, 0, 0, opacity == null ? 1 : opacity);\n}\n\nexport default function lab(l, a, b, opacity) {\n  return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);\n}\n\nexport function Lab(l, a, b, opacity) {\n  this.l = +l;\n  this.a = +a;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Lab, lab, extend(Color, {\n  brighter(k) {\n    return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  darker(k) {\n    return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  rgb() {\n    var y = (this.l + 16) / 116,\n        x = isNaN(this.a) ? y : y + this.a / 500,\n        z = isNaN(this.b) ? y : y - this.b / 200;\n    x = Xn * lab2xyz(x);\n    y = Yn * lab2xyz(y);\n    z = Zn * lab2xyz(z);\n    return new Rgb(\n      lrgb2rgb( 3.1338561 * x - 1.6168667 * y - 0.4906146 * z),\n      lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z),\n      lrgb2rgb( 0.0719453 * x - 0.2289914 * y + 1.4052427 * z),\n      this.opacity\n    );\n  }\n}));\n\nfunction xyz2lab(t) {\n  return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;\n}\n\nfunction lab2xyz(t) {\n  return t > t1 ? t * t * t : t2 * (t - t0);\n}\n\nfunction lrgb2rgb(x) {\n  return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);\n}\n\nfunction rgb2lrgb(x) {\n  return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);\n}\n\nfunction hclConvert(o) {\n  if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);\n  if (!(o instanceof Lab)) o = labConvert(o);\n  if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0 < o.l && o.l < 100 ? 0 : NaN, o.l, o.opacity);\n  var h = Math.atan2(o.b, o.a) * degrees;\n  return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);\n}\n\nexport function lch(l, c, h, opacity) {\n  return arguments.length === 1 ? hclConvert(l) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function hcl(h, c, l, opacity) {\n  return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function Hcl(h, c, l, opacity) {\n  this.h = +h;\n  this.c = +c;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\nfunction hcl2lab(o) {\n  if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);\n  var h = o.h * radians;\n  return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);\n}\n\ndefine(Hcl, hcl, extend(Color, {\n  brighter(k) {\n    return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);\n  },\n  darker(k) {\n    return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);\n  },\n  rgb() {\n    return hcl2lab(this).rgb();\n  }\n}));\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb, darker, brighter} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\nvar A = -0.14861,\n    B = +1.78277,\n    C = -0.29227,\n    D = -0.90649,\n    E = +1.97294,\n    ED = E * D,\n    EB = E * B,\n    BC_DA = B * C - D * A;\n\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n      bl = b - l,\n      k = (E * (g - l) - C * bl) / D,\n      s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)), // NaN if l=0 or l=1\n      h = s ? Math.atan2(k, bl) * degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\n\nexport default function cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\n\nexport function Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Cubehelix, cubehelix, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * radians,\n        l = +this.l,\n        a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n        cosh = Math.cos(h),\n        sinh = Math.sin(h);\n    return new Rgb(\n      255 * (l + a * (A * cosh + B * sinh)),\n      255 * (l + a * (C * cosh + D * sinh)),\n      255 * (l + a * (E * cosh)),\n      this.opacity\n    );\n  }\n}));\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(range) {\n  var n = range.length;\n  return function(t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}\n", "import {hue} from \"./color.js\";\n\nexport default function(a, b) {\n  var i = hue(+a, +b);\n  return function(t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n", "import {hsl as colorHsl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hsl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hsl(hue);\nexport var hslLong = hsl(color);\n", "import {lab as colorLab} from \"d3-color\";\nimport color from \"./color.js\";\n\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n      a = color(start.a, end.a),\n      b = color(start.b, end.b),\n      opacity = color(start.opacity, end.opacity);\n  return function(t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}\n", "import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n", "import {cubehelix as colorCubehelix} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction cubehelix(hue) {\n  return (function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n          s = color(start.s, end.s),\n          l = color(start.l, end.l),\n          opacity = color(start.opacity, end.opacity);\n      return function(t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n\n    return cubehelix;\n  })(1);\n}\n\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);\n", "import {default as value} from \"./value.js\";\n\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0, n = values.length - 1, v = values[0], I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function(t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}\n", "export default function(interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}\n", "const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n", "import {InternMap} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport const implicit = Symbol(\"implicit\");\n\nexport default function ordinal() {\n  var index = new InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nexport default function identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, number) : [0, 1];\n\n  return linearish(scale);\n}\n", "export default function nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n", "import {ticks} from \"d3-array\";\nimport {format, formatSpecifier} from \"d3-format\";\nimport nice from \"./nice.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nexport function loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = ticks(u, v, n);\n    } else {\n      z = ticks(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;\n      specifier = format(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain(nice(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nexport default function log() {\n  const scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = () => copy(scale, log()).base(scale.base());\n  initRange.apply(scale, arguments);\n  return scale;\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nexport function symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return linearish(scale);\n}\n\nexport default function symlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, symlog()).constant(scale.constant());\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, identity, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nexport function powish(transform) {\n  var scale = transform(identity, identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(identity, identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return linearish(scale);\n}\n\nexport default function pow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, pow()).exponent(scale.exponent());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nexport function sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n", "import continuous from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\n\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\n\nexport default function radial() {\n  var squared = continuous(),\n      range = [0, 1],\n      round = false,\n      unknown;\n\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n\n  scale.invert = function(y) {\n    return squared.invert(square(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (squared.range((range = Array.from(_, number)).map(square)), scale) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return scale.range(_).round(true);\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return radial(squared.domain(), range)\n        .round(round)\n        .clamp(squared.clamp())\n        .unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {ascending, bisect, quantileSorted as threshold} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = threshold(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[bisect(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {linearish} from \"./linear.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(linearish(scale), arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n", "import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n", "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationWeek} from \"./duration.js\";\n\nfunction timeWeekday(i) {\n  return timeInterval((date) => {\n    date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setDate(date.getDate() + step * 7);\n  }, (start, end) => {\n    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;\n  });\n}\n\nexport const timeSunday = timeWeekday(0);\nexport const timeMonday = timeWeekday(1);\nexport const timeTuesday = timeWeekday(2);\nexport const timeWednesday = timeWeekday(3);\nexport const timeThursday = timeWeekday(4);\nexport const timeFriday = timeWeekday(5);\nexport const timeSaturday = timeWeekday(6);\n\nexport const timeSundays = timeSunday.range;\nexport const timeMondays = timeMonday.range;\nexport const timeTuesdays = timeTuesday.range;\nexport const timeWednesdays = timeWednesday.range;\nexport const timeThursdays = timeThursday.range;\nexport const timeFridays = timeFriday.range;\nexport const timeSaturdays = timeSaturday.range;\n\nfunction utcWeekday(i) {\n  return timeInterval((date) => {\n    date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCDate(date.getUTCDate() + step * 7);\n  }, (start, end) => {\n    return (end - start) / durationWeek;\n  });\n}\n\nexport const utcSunday = utcWeekday(0);\nexport const utcMonday = utcWeekday(1);\nexport const utcTuesday = utcWeekday(2);\nexport const utcWednesday = utcWeekday(3);\nexport const utcThursday = utcWeekday(4);\nexport const utcFriday = utcWeekday(5);\nexport const utcSaturday = utcWeekday(6);\n\nexport const utcSundays = utcSunday.range;\nexport const utcMondays = utcMonday.range;\nexport const utcTuesdays = utcTuesday.range;\nexport const utcWednesdays = utcWednesday.range;\nexport const utcThursdays = utcThursday.range;\nexport const utcFridays = utcFriday.range;\nexport const utcSaturdays = utcSaturday.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n", "import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n", "import {\n  timeDay,\n  timeSunday,\n  timeMonday,\n  timeThursday,\n  timeYear,\n  utcDay,\n  utcSunday,\n  utcMonday,\n  utcThursday,\n  utcYear\n} from \"d3-time\";\n\nfunction localDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n    date.setFullYear(d.y);\n    return date;\n  }\n  return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\n\nfunction utcDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n    date.setUTCFullYear(d.y);\n    return date;\n  }\n  return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\n\nfunction newDate(y, m, d) {\n  return {y: y, m: m, d: d, H: 0, M: 0, S: 0, L: 0};\n}\n\nexport default function formatLocale(locale) {\n  var locale_dateTime = locale.dateTime,\n      locale_date = locale.date,\n      locale_time = locale.time,\n      locale_periods = locale.periods,\n      locale_weekdays = locale.days,\n      locale_shortWeekdays = locale.shortDays,\n      locale_months = locale.months,\n      locale_shortMonths = locale.shortMonths;\n\n  var periodRe = formatRe(locale_periods),\n      periodLookup = formatLookup(locale_periods),\n      weekdayRe = formatRe(locale_weekdays),\n      weekdayLookup = formatLookup(locale_weekdays),\n      shortWeekdayRe = formatRe(locale_shortWeekdays),\n      shortWeekdayLookup = formatLookup(locale_shortWeekdays),\n      monthRe = formatRe(locale_months),\n      monthLookup = formatLookup(locale_months),\n      shortMonthRe = formatRe(locale_shortMonths),\n      shortMonthLookup = formatLookup(locale_shortMonths);\n\n  var formats = {\n    \"a\": formatShortWeekday,\n    \"A\": formatWeekday,\n    \"b\": formatShortMonth,\n    \"B\": formatMonth,\n    \"c\": null,\n    \"d\": formatDayOfMonth,\n    \"e\": formatDayOfMonth,\n    \"f\": formatMicroseconds,\n    \"g\": formatYearISO,\n    \"G\": formatFullYearISO,\n    \"H\": formatHour24,\n    \"I\": formatHour12,\n    \"j\": formatDayOfYear,\n    \"L\": formatMilliseconds,\n    \"m\": formatMonthNumber,\n    \"M\": formatMinutes,\n    \"p\": formatPeriod,\n    \"q\": formatQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatSeconds,\n    \"u\": formatWeekdayNumberMonday,\n    \"U\": formatWeekNumberSunday,\n    \"V\": formatWeekNumberISO,\n    \"w\": formatWeekdayNumberSunday,\n    \"W\": formatWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatYear,\n    \"Y\": formatFullYear,\n    \"Z\": formatZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var utcFormats = {\n    \"a\": formatUTCShortWeekday,\n    \"A\": formatUTCWeekday,\n    \"b\": formatUTCShortMonth,\n    \"B\": formatUTCMonth,\n    \"c\": null,\n    \"d\": formatUTCDayOfMonth,\n    \"e\": formatUTCDayOfMonth,\n    \"f\": formatUTCMicroseconds,\n    \"g\": formatUTCYearISO,\n    \"G\": formatUTCFullYearISO,\n    \"H\": formatUTCHour24,\n    \"I\": formatUTCHour12,\n    \"j\": formatUTCDayOfYear,\n    \"L\": formatUTCMilliseconds,\n    \"m\": formatUTCMonthNumber,\n    \"M\": formatUTCMinutes,\n    \"p\": formatUTCPeriod,\n    \"q\": formatUTCQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatUTCSeconds,\n    \"u\": formatUTCWeekdayNumberMonday,\n    \"U\": formatUTCWeekNumberSunday,\n    \"V\": formatUTCWeekNumberISO,\n    \"w\": formatUTCWeekdayNumberSunday,\n    \"W\": formatUTCWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatUTCYear,\n    \"Y\": formatUTCFullYear,\n    \"Z\": formatUTCZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var parses = {\n    \"a\": parseShortWeekday,\n    \"A\": parseWeekday,\n    \"b\": parseShortMonth,\n    \"B\": parseMonth,\n    \"c\": parseLocaleDateTime,\n    \"d\": parseDayOfMonth,\n    \"e\": parseDayOfMonth,\n    \"f\": parseMicroseconds,\n    \"g\": parseYear,\n    \"G\": parseFullYear,\n    \"H\": parseHour24,\n    \"I\": parseHour24,\n    \"j\": parseDayOfYear,\n    \"L\": parseMilliseconds,\n    \"m\": parseMonthNumber,\n    \"M\": parseMinutes,\n    \"p\": parsePeriod,\n    \"q\": parseQuarter,\n    \"Q\": parseUnixTimestamp,\n    \"s\": parseUnixTimestampSeconds,\n    \"S\": parseSeconds,\n    \"u\": parseWeekdayNumberMonday,\n    \"U\": parseWeekNumberSunday,\n    \"V\": parseWeekNumberISO,\n    \"w\": parseWeekdayNumberSunday,\n    \"W\": parseWeekNumberMonday,\n    \"x\": parseLocaleDate,\n    \"X\": parseLocaleTime,\n    \"y\": parseYear,\n    \"Y\": parseFullYear,\n    \"Z\": parseZone,\n    \"%\": parseLiteralPercent\n  };\n\n  // These recursive directive definitions must be deferred.\n  formats.x = newFormat(locale_date, formats);\n  formats.X = newFormat(locale_time, formats);\n  formats.c = newFormat(locale_dateTime, formats);\n  utcFormats.x = newFormat(locale_date, utcFormats);\n  utcFormats.X = newFormat(locale_time, utcFormats);\n  utcFormats.c = newFormat(locale_dateTime, utcFormats);\n\n  function newFormat(specifier, formats) {\n    return function(date) {\n      var string = [],\n          i = -1,\n          j = 0,\n          n = specifier.length,\n          c,\n          pad,\n          format;\n\n      if (!(date instanceof Date)) date = new Date(+date);\n\n      while (++i < n) {\n        if (specifier.charCodeAt(i) === 37) {\n          string.push(specifier.slice(j, i));\n          if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n          else pad = c === \"e\" ? \" \" : \"0\";\n          if (format = formats[c]) c = format(date, pad);\n          string.push(c);\n          j = i + 1;\n        }\n      }\n\n      string.push(specifier.slice(j, i));\n      return string.join(\"\");\n    };\n  }\n\n  function newParse(specifier, Z) {\n    return function(string) {\n      var d = newDate(1900, undefined, 1),\n          i = parseSpecifier(d, specifier, string += \"\", 0),\n          week, day;\n      if (i != string.length) return null;\n\n      // If a UNIX timestamp is specified, return it.\n      if (\"Q\" in d) return new Date(d.Q);\n      if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n\n      // If this is utcParse, never use the local timezone.\n      if (Z && !(\"Z\" in d)) d.Z = 0;\n\n      // The am-pm flag is 0 for AM, and 1 for PM.\n      if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n\n      // If the month was not specified, inherit from the quarter.\n      if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n\n      // Convert day-of-week and week-of-year to day-of-year.\n      if (\"V\" in d) {\n        if (d.V < 1 || d.V > 53) return null;\n        if (!(\"w\" in d)) d.w = 1;\n        if (\"Z\" in d) {\n          week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n          week = day > 4 || day === 0 ? utcMonday.ceil(week) : utcMonday(week);\n          week = utcDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getUTCFullYear();\n          d.m = week.getUTCMonth();\n          d.d = week.getUTCDate() + (d.w + 6) % 7;\n        } else {\n          week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n          week = day > 4 || day === 0 ? timeMonday.ceil(week) : timeMonday(week);\n          week = timeDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getFullYear();\n          d.m = week.getMonth();\n          d.d = week.getDate() + (d.w + 6) % 7;\n        }\n      } else if (\"W\" in d || \"U\" in d) {\n        if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n        day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n        d.m = 0;\n        d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n      }\n\n      // If a time zone is specified, all fields are interpreted as UTC and then\n      // offset according to the specified time zone.\n      if (\"Z\" in d) {\n        d.H += d.Z / 100 | 0;\n        d.M += d.Z % 100;\n        return utcDate(d);\n      }\n\n      // Otherwise, all fields are in local time.\n      return localDate(d);\n    };\n  }\n\n  function parseSpecifier(d, specifier, string, j) {\n    var i = 0,\n        n = specifier.length,\n        m = string.length,\n        c,\n        parse;\n\n    while (i < n) {\n      if (j >= m) return -1;\n      c = specifier.charCodeAt(i++);\n      if (c === 37) {\n        c = specifier.charAt(i++);\n        parse = parses[c in pads ? specifier.charAt(i++) : c];\n        if (!parse || ((j = parse(d, string, j)) < 0)) return -1;\n      } else if (c != string.charCodeAt(j++)) {\n        return -1;\n      }\n    }\n\n    return j;\n  }\n\n  function parsePeriod(d, string, i) {\n    var n = periodRe.exec(string.slice(i));\n    return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortWeekday(d, string, i) {\n    var n = shortWeekdayRe.exec(string.slice(i));\n    return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseWeekday(d, string, i) {\n    var n = weekdayRe.exec(string.slice(i));\n    return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortMonth(d, string, i) {\n    var n = shortMonthRe.exec(string.slice(i));\n    return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseMonth(d, string, i) {\n    var n = monthRe.exec(string.slice(i));\n    return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseLocaleDateTime(d, string, i) {\n    return parseSpecifier(d, locale_dateTime, string, i);\n  }\n\n  function parseLocaleDate(d, string, i) {\n    return parseSpecifier(d, locale_date, string, i);\n  }\n\n  function parseLocaleTime(d, string, i) {\n    return parseSpecifier(d, locale_time, string, i);\n  }\n\n  function formatShortWeekday(d) {\n    return locale_shortWeekdays[d.getDay()];\n  }\n\n  function formatWeekday(d) {\n    return locale_weekdays[d.getDay()];\n  }\n\n  function formatShortMonth(d) {\n    return locale_shortMonths[d.getMonth()];\n  }\n\n  function formatMonth(d) {\n    return locale_months[d.getMonth()];\n  }\n\n  function formatPeriod(d) {\n    return locale_periods[+(d.getHours() >= 12)];\n  }\n\n  function formatQuarter(d) {\n    return 1 + ~~(d.getMonth() / 3);\n  }\n\n  function formatUTCShortWeekday(d) {\n    return locale_shortWeekdays[d.getUTCDay()];\n  }\n\n  function formatUTCWeekday(d) {\n    return locale_weekdays[d.getUTCDay()];\n  }\n\n  function formatUTCShortMonth(d) {\n    return locale_shortMonths[d.getUTCMonth()];\n  }\n\n  function formatUTCMonth(d) {\n    return locale_months[d.getUTCMonth()];\n  }\n\n  function formatUTCPeriod(d) {\n    return locale_periods[+(d.getUTCHours() >= 12)];\n  }\n\n  function formatUTCQuarter(d) {\n    return 1 + ~~(d.getUTCMonth() / 3);\n  }\n\n  return {\n    format: function(specifier) {\n      var f = newFormat(specifier += \"\", formats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    parse: function(specifier) {\n      var p = newParse(specifier += \"\", false);\n      p.toString = function() { return specifier; };\n      return p;\n    },\n    utcFormat: function(specifier) {\n      var f = newFormat(specifier += \"\", utcFormats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    utcParse: function(specifier) {\n      var p = newParse(specifier += \"\", true);\n      p.toString = function() { return specifier; };\n      return p;\n    }\n  };\n}\n\nvar pads = {\"-\": \"\", \"_\": \" \", \"0\": \"0\"},\n    numberRe = /^\\s*\\d+/, // note: ignores next directive\n    percentRe = /^%/,\n    requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\n\nfunction pad(value, fill, width) {\n  var sign = value < 0 ? \"-\" : \"\",\n      string = (sign ? -value : value) + \"\",\n      length = string.length;\n  return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\n\nfunction requote(s) {\n  return s.replace(requoteRe, \"\\\\$&\");\n}\n\nfunction formatRe(names) {\n  return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\n\nfunction formatLookup(names) {\n  return new Map(names.map((name, i) => [name.toLowerCase(), i]));\n}\n\nfunction parseWeekdayNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekdayNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberISO(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseFullYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 4));\n  return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\n\nfunction parseZone(d, string, i) {\n  var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n  return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\n\nfunction parseQuarter(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\n\nfunction parseMonthNumber(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\n\nfunction parseDayOfMonth(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseDayOfYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseHour24(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMinutes(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMilliseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMicroseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 6));\n  return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\n\nfunction parseLiteralPercent(d, string, i) {\n  var n = percentRe.exec(string.slice(i, i + 1));\n  return n ? i + n[0].length : -1;\n}\n\nfunction parseUnixTimestamp(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseUnixTimestampSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\n\nfunction formatDayOfMonth(d, p) {\n  return pad(d.getDate(), p, 2);\n}\n\nfunction formatHour24(d, p) {\n  return pad(d.getHours(), p, 2);\n}\n\nfunction formatHour12(d, p) {\n  return pad(d.getHours() % 12 || 12, p, 2);\n}\n\nfunction formatDayOfYear(d, p) {\n  return pad(1 + timeDay.count(timeYear(d), d), p, 3);\n}\n\nfunction formatMilliseconds(d, p) {\n  return pad(d.getMilliseconds(), p, 3);\n}\n\nfunction formatMicroseconds(d, p) {\n  return formatMilliseconds(d, p) + \"000\";\n}\n\nfunction formatMonthNumber(d, p) {\n  return pad(d.getMonth() + 1, p, 2);\n}\n\nfunction formatMinutes(d, p) {\n  return pad(d.getMinutes(), p, 2);\n}\n\nfunction formatSeconds(d, p) {\n  return pad(d.getSeconds(), p, 2);\n}\n\nfunction formatWeekdayNumberMonday(d) {\n  var day = d.getDay();\n  return day === 0 ? 7 : day;\n}\n\nfunction formatWeekNumberSunday(d, p) {\n  return pad(timeSunday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction dISO(d) {\n  var day = d.getDay();\n  return (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n}\n\nfunction formatWeekNumberISO(d, p) {\n  d = dISO(d);\n  return pad(timeThursday.count(timeYear(d), d) + (timeYear(d).getDay() === 4), p, 2);\n}\n\nfunction formatWeekdayNumberSunday(d) {\n  return d.getDay();\n}\n\nfunction formatWeekNumberMonday(d, p) {\n  return pad(timeMonday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction formatYear(d, p) {\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatYearISO(d, p) {\n  d = dISO(d);\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatFullYear(d, p) {\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatFullYearISO(d, p) {\n  var day = d.getDay();\n  d = (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatZone(d) {\n  var z = d.getTimezoneOffset();\n  return (z > 0 ? \"-\" : (z *= -1, \"+\"))\n      + pad(z / 60 | 0, \"0\", 2)\n      + pad(z % 60, \"0\", 2);\n}\n\nfunction formatUTCDayOfMonth(d, p) {\n  return pad(d.getUTCDate(), p, 2);\n}\n\nfunction formatUTCHour24(d, p) {\n  return pad(d.getUTCHours(), p, 2);\n}\n\nfunction formatUTCHour12(d, p) {\n  return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\n\nfunction formatUTCDayOfYear(d, p) {\n  return pad(1 + utcDay.count(utcYear(d), d), p, 3);\n}\n\nfunction formatUTCMilliseconds(d, p) {\n  return pad(d.getUTCMilliseconds(), p, 3);\n}\n\nfunction formatUTCMicroseconds(d, p) {\n  return formatUTCMilliseconds(d, p) + \"000\";\n}\n\nfunction formatUTCMonthNumber(d, p) {\n  return pad(d.getUTCMonth() + 1, p, 2);\n}\n\nfunction formatUTCMinutes(d, p) {\n  return pad(d.getUTCMinutes(), p, 2);\n}\n\nfunction formatUTCSeconds(d, p) {\n  return pad(d.getUTCSeconds(), p, 2);\n}\n\nfunction formatUTCWeekdayNumberMonday(d) {\n  var dow = d.getUTCDay();\n  return dow === 0 ? 7 : dow;\n}\n\nfunction formatUTCWeekNumberSunday(d, p) {\n  return pad(utcSunday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction UTCdISO(d) {\n  var day = d.getUTCDay();\n  return (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n}\n\nfunction formatUTCWeekNumberISO(d, p) {\n  d = UTCdISO(d);\n  return pad(utcThursday.count(utcYear(d), d) + (utcYear(d).getUTCDay() === 4), p, 2);\n}\n\nfunction formatUTCWeekdayNumberSunday(d) {\n  return d.getUTCDay();\n}\n\nfunction formatUTCWeekNumberMonday(d, p) {\n  return pad(utcMonday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction formatUTCYear(d, p) {\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCYearISO(d, p) {\n  d = UTCdISO(d);\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCFullYear(d, p) {\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCFullYearISO(d, p) {\n  var day = d.getUTCDay();\n  d = (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCZone() {\n  return \"+0000\";\n}\n\nfunction formatLiteralPercent() {\n  return \"%\";\n}\n\nfunction formatUnixTimestamp(d) {\n  return +d;\n}\n\nfunction formatUnixTimestampSeconds(d) {\n  return Math.floor(+d / 1000);\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\n\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}\n", "import {utcFormat} from \"./defaultLocale.js\";\n\nexport var isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\n\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\n\nvar formatIso = Date.prototype.toISOString\n    ? formatIsoNative\n    : utcFormat(isoSpecifier);\n\nexport default formatIso;\n", "import {isoSpecifier} from \"./isoFormat.js\";\nimport {utcParse} from \"./defaultLocale.js\";\n\nfunction parseIsoNative(string) {\n  var date = new Date(string);\n  return isNaN(date) ? null : date;\n}\n\nvar parseIso = +new Date(\"2000-01-01T00:00:00.000Z\")\n    ? parseIsoNative\n    : utcParse(isoSpecifier);\n\nexport default parseIso;\n", "import {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport nice from \"./nice.js\";\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n", "import {utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcTicks, utcTickInterval} from \"d3-time\";\nimport {utcFormat} from \"d3-time-format\";\nimport {calendar} from \"./time.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function utcTime() {\n  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n", "import {interpolate, interpolateRound} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n", "import {ascending, bisect, quantile} from \"d3-array\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\n\nexport default function sequentialQuantile() {\n  var domain = [],\n      interpolator = identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator((bisect(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => quantile(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n", "import {interpolate, interpolateRound, piecewise} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {copy} from \"./sequential.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = piecewise(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nexport default function diverging() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, diverging());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingLog() {\n  var scale = loggish(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, divergingLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\n\nexport const epsilon = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n", "import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n", "import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n", "function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n", "export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x, y) {\n  var defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(line);\n\n  x = typeof x === \"function\" ? x : (x === undefined) ? pointX : constant(x);\n  y = typeof y === \"function\" ? y : (y === undefined) ? pointY : constant(y);\n\n  function line(data) {\n    var i,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x0, y0, y1) {\n  var x1 = null,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(area);\n\n  x0 = typeof x0 === \"function\" ? x0 : (x0 === undefined) ? pointX : constant(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : (y0 === undefined) ? constant(0) : constant(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : (y1 === undefined) ? pointY : constant(+y1);\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function(d) {\n  return d;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = (data = array(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n", "import curveLinear from \"./linear.js\";\n\nexport var curveRadialLinear = curveRadial(curveLinear);\n\nfunction Radial(curve) {\n  this._curve = curve;\n}\n\nRadial.prototype = {\n  areaStart: function() {\n    this._curve.areaStart();\n  },\n  areaEnd: function() {\n    this._curve.areaEnd();\n  },\n  lineStart: function() {\n    this._curve.lineStart();\n  },\n  lineEnd: function() {\n    this._curve.lineEnd();\n  },\n  point: function(a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\n\nexport default function curveRadial(curve) {\n\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n\n  radial._curve = curve;\n\n  return radial;\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport line from \"./line.js\";\n\nexport function lineRadial(l) {\n  var c = l.curve;\n\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n\n  l.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return l;\n}\n\nexport default function() {\n  return lineRadial(line().curve(curveRadialLinear));\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport area from \"./area.js\";\nimport {lineRadial} from \"./lineRadial.js\";\n\nexport default function() {\n  var a = area().curve(curveRadialLinear),\n      c = a.curve,\n      x0 = a.lineX0,\n      x1 = a.lineX1,\n      y0 = a.lineY0,\n      y1 = a.lineY1;\n\n  a.angle = a.x, delete a.x;\n  a.startAngle = a.x0, delete a.x0;\n  a.endAngle = a.x1, delete a.x1;\n  a.radius = a.y, delete a.y;\n  a.innerRadius = a.y0, delete a.y0;\n  a.outerRadius = a.y1, delete a.y1;\n  a.lineStartAngle = function() { return lineRadial(x0()); }, delete a.lineX0;\n  a.lineEndAngle = function() { return lineRadial(x1()); }, delete a.lineX1;\n  a.lineInnerRadius = function() { return lineRadial(y0()); }, delete a.lineY0;\n  a.lineOuterRadius = function() { return lineRadial(y1()); }, delete a.lineY1;\n\n  a.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return a;\n}\n", "export default function(x, y) {\n  return [(y = +y) * Math.cos(x -= Math.PI / 2), y * Math.sin(x)];\n}\n", "import pointRadial from \"../pointRadial.js\";\n\nclass Bump {\n  constructor(context, x) {\n    this._context = context;\n    this._x = x;\n  }\n  areaStart() {\n    this._line = 0;\n  }\n  areaEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  }\n  point(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: {\n        this._point = 1;\n        if (this._line) this._context.lineTo(x, y);\n        else this._context.moveTo(x, y);\n        break;\n      }\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._x) this._context.bezierCurveTo(this._x0 = (this._x0 + x) / 2, this._y0, this._x0, y, x, y);\n        else this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + y) / 2, x, this._y0, x, y);\n        break;\n      }\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nclass BumpRadial {\n  constructor(context) {\n    this._context = context;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {}\n  point(x, y) {\n    x = +x, y = +y;\n    if (this._point === 0) {\n      this._point = 1;\n    } else {\n      const p0 = pointRadial(this._x0, this._y0);\n      const p1 = pointRadial(this._x0, this._y0 = (this._y0 + y) / 2);\n      const p2 = pointRadial(x, this._y0);\n      const p3 = pointRadial(x, y);\n      this._context.moveTo(...p0);\n      this._context.bezierCurveTo(...p1, ...p2, ...p3);\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nexport function bumpX(context) {\n  return new Bump(context, true);\n}\n\nexport function bumpY(context) {\n  return new Bump(context, false);\n}\n\nexport function bumpRadial(context) {\n  return new BumpRadial(context);\n}\n", "import {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport {bumpX, bumpY, bumpRadial} from \"./curve/bump.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nexport function link(curve) {\n  let source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null,\n      output = null,\n      path = withPath(link);\n\n  function link() {\n    let buffer;\n    const argv = slice.call(arguments);\n    const s = source.apply(this, argv);\n    const t = target.apply(this, argv);\n    if (context == null) output = curve(buffer = path());\n    output.lineStart();\n    argv[0] = s, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    argv[0] = t, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    output.lineEnd();\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), link) : context;\n  };\n\n  return link;\n}\n\nexport function linkHorizontal() {\n  return link(bumpX);\n}\n\nexport function linkVertical() {\n  return link(bumpY);\n}\n\nexport function linkRadial() {\n  const l = link(bumpRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {min, sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size + min(size / 28, 0.75)) * 0.59436;\n    const t = r / 2;\n    const u = t * sqrt3;\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n    context.moveTo(-u, -t);\n    context.lineTo(u, t);\n    context.moveTo(-u, t);\n    context.lineTo(u, -t);\n  }\n};\n", "import {pi, sqrt, tau} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / pi);\n    context.moveTo(r, 0);\n    context.arc(0, 0, r, 0, tau);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst tan30 = sqrt(1 / 3);\nconst tan30_2 = tan30 * 2;\n\nexport default {\n  draw(context, size) {\n    const y = sqrt(size / tan30_2);\n    const x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.62625;\n    context.moveTo(0, -r);\n    context.lineTo(r, 0);\n    context.lineTo(0, r);\n    context.lineTo(-r, 0);\n    context.closePath();\n  }\n};\n", "import {min, sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 7, 2)) * 0.87559;\n    context.moveTo(-r, 0);\n    context.lineTo(r, 0);\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const w = sqrt(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.4431;\n    context.moveTo(r, r);\n    context.lineTo(r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, r);\n    context.closePath();\n  }\n};\n", "import {sin, cos, sqrt, pi, tau} from \"../math.js\";\n\nconst ka = 0.89081309152928522810;\nconst kr = sin(pi / 10) / sin(7 * pi / 10);\nconst kx = sin(tau / 10) * kr;\nconst ky = -cos(tau / 10) * kr;\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (let i = 1; i < 5; ++i) {\n      const a = tau * i / 5;\n      const c = cos(a);\n      const s = sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const y = -sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const s = sqrt(size) * 0.6824;\n    const t = s  / 2;\n    const u = (s * sqrt3) / 2; // cos(Math.PI / 6)\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst c = -0.5;\nconst s = sqrt(3) / 2;\nconst k = 1 / sqrt(12);\nconst a = (k / 2 + 1) * 3;\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / a);\n    const x0 = r / 2, y0 = r * k;\n    const x1 = x0, y1 = r * k + r;\n    const x2 = -x1, y2 = y1;\n    context.moveTo(x0, y0);\n    context.lineTo(x1, y1);\n    context.lineTo(x2, y2);\n    context.lineTo(c * x0 - s * y0, s * x0 + c * y0);\n    context.lineTo(c * x1 - s * y1, s * x1 + c * y1);\n    context.lineTo(c * x2 - s * y2, s * x2 + c * y2);\n    context.lineTo(c * x0 + s * y0, c * y0 - s * x0);\n    context.lineTo(c * x1 + s * y1, c * y1 - s * x1);\n    context.lineTo(c * x2 + s * y2, c * y2 - s * x2);\n    context.closePath();\n  }\n};\n", "import {min, sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 6, 1.7)) * 0.6189;\n    context.moveTo(-r, -r);\n    context.lineTo(r, r);\n    context.moveTo(-r, r);\n    context.lineTo(r, -r);\n  }\n};\n", "import constant from \"./constant.js\";\nimport {withPath} from \"./path.js\";\nimport asterisk from \"./symbol/asterisk.js\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport diamond2 from \"./symbol/diamond2.js\";\nimport plus from \"./symbol/plus.js\";\nimport square from \"./symbol/square.js\";\nimport square2 from \"./symbol/square2.js\";\nimport star from \"./symbol/star.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport triangle2 from \"./symbol/triangle2.js\";\nimport wye from \"./symbol/wye.js\";\nimport times from \"./symbol/times.js\";\n\n// These symbols are designed to be filled.\nexport const symbolsFill = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\n// These symbols are designed to be stroked (with a width of 1.5px and round caps).\nexport const symbolsStroke = [\n  circle,\n  plus,\n  times,\n  triangle2,\n  asterisk,\n  square2,\n  diamond2\n];\n\nexport default function Symbol(type, size) {\n  let context = null,\n      path = withPath(symbol);\n\n  type = typeof type === \"function\" ? type : constant(type || circle);\n  size = typeof size === \"function\" ? size : constant(size === undefined ? 64 : +size);\n\n  function symbol() {\n    let buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nexport function Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // falls through\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new Basis(context);\n}\n", "export default function() {}\n", "import noop from \"../noop.js\";\nimport {point} from \"./basis.js\";\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisClosed(context);\n}\n", "import {point} from \"./basis.js\";\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisOpen(context);\n}\n", "import {Basis} from \"./basis.js\";\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nexport default (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    that._x1 + that._k * (that._x2 - that._x0),\n    that._y1 + that._k * (that._y2 - that._y0),\n    that._x2 + that._k * (that._x1 - x),\n    that._y2 + that._k * (that._y1 - y),\n    that._x2,\n    that._y2\n  );\n}\n\nexport function Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinal.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: point(this, this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; this._x1 = x, this._y1 = y; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import noop from \"../noop.js\";\nimport {point} from \"./cardinal.js\";\n\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {point} from \"./cardinal.js\";\n\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {epsilon} from \"../math.js\";\nimport {<PERSON>} from \"./cardinal.js\";\n\nexport function point(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {CardinalClosed} from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {<PERSON><PERSON><PERSON>} from \"./cardinalOpen.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import noop from \"../noop.js\";\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nexport default function(context) {\n  return new LinearClosed(context);\n}\n", "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n}\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}\n", "function Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nexport default function(context) {\n  return new Natural(context);\n}\n", "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._t <= 0) {\n          this._context.lineTo(this._x, y);\n          this._context.lineTo(x, y);\n        } else {\n          var x1 = this._x * (1 - this._t) + x * this._t;\n          this._context.lineTo(x1, this._y);\n          this._context.lineTo(x1, y);\n        }\n        break;\n      }\n    }\n    this._x = x, this._y = y;\n  }\n};\n\nexport default function(context) {\n  return new Step(context, 0.5);\n}\n\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\n\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}\n", "export default function(series) {\n  var n = series.length, o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\n\nexport default function() {\n  var keys = constant([]),\n      order = orderNone,\n      offset = offsetNone,\n      value = stackValue;\n\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n        i, n = sz.length, j = -1,\n        oz;\n\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n\n    for (i = 0, oz = array(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n\n  return stack;\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n          sij0 = si[j][1] || 0,\n          sij1 = si[j - 1][1] || 0,\n          s3 = (sij0 - sij1) / 2;\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n            skj0 = sk[j][1] || 0,\n            skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n      s1 += sij0, s2 += s3 * sij0;\n    }\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var peaks = series.map(peak);\n  return none(series).sort(function(a, b) { return peaks[a] - peaks[b]; });\n}\n\nfunction peak(series) {\n  var i = -1, j = 0, n = series.length, vi, vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var sums = series.map(sum);\n  return none(series).sort(function(a, b) { return sums[a] - sums[b]; });\n}\n\nexport function sum(series) {\n  var s = 0, i = -1, n = series.length, v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function(series) {\n  return ascending(series).reverse();\n}\n", "import appearance from \"./appearance.js\";\nimport {sum} from \"./ascending.js\";\n\nexport default function(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(sum),\n      order = appearance(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  return none(series).reverse();\n}\n"], "mappings": ";AAAe,SAAR,UAA2BA,IAAG,GAAG;AACtC,SAAOA,MAAK,QAAQ,KAAK,OAAO,MAAMA,KAAI,IAAI,KAAKA,KAAI,IAAI,IAAIA,MAAK,IAAI,IAAI;AAC9E;;;ACFe,SAAR,WAA4BC,IAAG,GAAG;AACvC,SAAOA,MAAK,QAAQ,KAAK,OAAO,MAC5B,IAAIA,KAAI,KACR,IAAIA,KAAI,IACR,KAAKA,KAAI,IACT;AACN;;;ACHe,SAAR,SAA0B,GAAG;AAClC,MAAI,UAAU,UAAU;AAOxB,MAAI,EAAE,WAAW,GAAG;AAClB,eAAW;AACX,eAAW,CAAC,GAAGC,OAAM,UAAU,EAAE,CAAC,GAAGA,EAAC;AACtC,YAAQ,CAAC,GAAGA,OAAM,EAAE,CAAC,IAAIA;AAAA,EAC3B,OAAO;AACL,eAAW,MAAM,aAAa,MAAM,aAAa,IAAI;AACrD,eAAW;AACX,YAAQ;AAAA,EACV;AAEA,WAAS,KAAKC,IAAGD,IAAG,KAAK,GAAG,KAAKC,GAAE,QAAQ;AACzC,QAAI,KAAK,IAAI;AACX,UAAI,SAASD,IAAGA,EAAC,MAAM,EAAG,QAAO;AACjC,SAAG;AACD,cAAM,MAAO,KAAK,OAAQ;AAC1B,YAAI,SAASC,GAAE,GAAG,GAAGD,EAAC,IAAI,EAAG,MAAK,MAAM;AAAA,YACnC,MAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,MAAMC,IAAGD,IAAG,KAAK,GAAG,KAAKC,GAAE,QAAQ;AAC1C,QAAI,KAAK,IAAI;AACX,UAAI,SAASD,IAAGA,EAAC,MAAM,EAAG,QAAO;AACjC,SAAG;AACD,cAAM,MAAO,KAAK,OAAQ;AAC1B,YAAI,SAASC,GAAE,GAAG,GAAGD,EAAC,KAAK,EAAG,MAAK,MAAM;AAAA,YACpC,MAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,OAAOC,IAAGD,IAAG,KAAK,GAAG,KAAKC,GAAE,QAAQ;AAC3C,UAAM,IAAI,KAAKA,IAAGD,IAAG,IAAI,KAAK,CAAC;AAC/B,WAAO,IAAI,MAAM,MAAMC,GAAE,IAAI,CAAC,GAAGD,EAAC,IAAI,CAAC,MAAMC,GAAE,CAAC,GAAGD,EAAC,IAAI,IAAI,IAAI;AAAA,EAClE;AAEA,SAAO,EAAC,MAAM,QAAQ,MAAK;AAC7B;AAEA,SAAS,OAAO;AACd,SAAO;AACT;;;ACvDe,SAAR,OAAwBE,IAAG;AAChC,SAAOA,OAAM,OAAO,MAAM,CAACA;AAC7B;AAEO,UAAU,QAAQ,QAAQ,SAAS;AACxC,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;;;ACfA,IAAM,kBAAkB,SAAS,SAAS;AACnC,IAAM,cAAc,gBAAgB;AACpC,IAAM,aAAa,gBAAgB;AACnC,IAAM,eAAe,SAAS,MAAM,EAAE;AAC7C,IAAO,iBAAQ;;;ACRR,SAAS,KAAK,QAAQ,GAAG;AAC9B,MAAI,GAAG,IAAI,CAAC,MAAM,GAAI,OAAM,IAAI,WAAW,WAAW;AACtD,MAAIC,UAAS,OAAO;AACpB,MAAI,GAAGA,UAAS,KAAK,MAAMA,OAAM,MAAM,GAAI,OAAM,IAAI,WAAW,gBAAgB;AAChF,MAAI,CAACA,WAAU,CAAC,EAAG,QAAO;AAC1B,QAAMC,QAAO,MAAM,CAAC;AACpB,QAAM,OAAO,OAAO,MAAM;AAC1B,EAAAA,MAAK,QAAQ,MAAM,GAAGD,SAAQ,CAAC;AAC/B,EAAAC,MAAK,MAAM,QAAQ,GAAGD,SAAQ,CAAC;AAC/B,EAAAC,MAAK,QAAQ,MAAM,GAAGD,SAAQ,CAAC;AAC/B,SAAO;AACT;AAEO,IAAM,QAAQ,MAAM,KAAK;AAEzB,IAAM,YAAY,MAAM,UAAU;AAEzC,SAAS,MAAMC,OAAM;AACnB,SAAO,SAAS,MAAM,IAAI,KAAK,IAAI;AACjC,QAAI,GAAG,KAAK,CAAC,OAAO,GAAI,OAAM,IAAI,WAAW,YAAY;AACzD,QAAI,GAAG,KAAK,CAAC,OAAO,GAAI,OAAM,IAAI,WAAW,YAAY;AACzD,QAAI,EAAC,MAAM,QAAQ,OAAO,OAAM,IAAI;AACpC,QAAI,GAAG,QAAQ,KAAK,MAAM,KAAK,MAAM,GAAI,OAAM,IAAI,WAAW,eAAe;AAC7E,QAAI,GAAG,SAAS,KAAK,MAAM,WAAW,SAAY,SAAS,OAAO,SAAS,KAAK,MAAM,GAAI,OAAM,IAAI,WAAW,gBAAgB;AAC/H,QAAI,CAAC,SAAS,CAAC,UAAW,CAAC,MAAM,CAAC,GAAK,QAAO;AAC9C,UAAM,QAAQ,MAAMA,MAAK,EAAE;AAC3B,UAAM,QAAQ,MAAMA,MAAK,EAAE;AAC3B,UAAM,OAAO,OAAO,MAAM;AAC1B,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C,WAAW,OAAO;AAChB,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C,WAAW,OAAO;AAChB,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAMA,OAAM,GAAG,GAAG,GAAG,GAAG;AAC/B,WAASC,KAAI,GAAG,IAAI,IAAI,GAAGA,KAAI,KAAI;AACjC,IAAAD,MAAK,GAAG,GAAGC,IAAGA,MAAK,GAAG,CAAC;AAAA,EACzB;AACF;AAEA,SAAS,MAAMD,OAAM,GAAG,GAAG,GAAG,GAAG;AAC/B,WAASE,KAAI,GAAG,IAAI,IAAI,GAAGA,KAAI,GAAG,EAAEA,IAAG;AACrC,IAAAF,MAAK,GAAG,GAAGE,IAAGA,KAAI,GAAG,CAAC;AAAA,EACxB;AACF;AAEA,SAAS,WAAW,QAAQ;AAC1B,QAAMF,QAAO,MAAM,MAAM;AACzB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,cAAU,GAAG,SAAS,GAAG,SAAS;AAClC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AAAA,EACtC;AACF;AAQA,SAAS,MAAM,QAAQ;AACrB,QAAM,UAAU,KAAK,MAAM,MAAM;AACjC,MAAI,YAAY,OAAQ,QAAO,MAAM,MAAM;AAC3C,QAAM,IAAI,SAAS;AACnB,QAAM,IAAI,IAAI,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,QAAI,GAAG,QAAQ,SAAS,OAAQ;AAChC,QAAIG,OAAM,UAAU,EAAE,KAAK;AAC3B,UAAM,KAAK,OAAO;AAClB,UAAM,KAAK,KAAK;AAChB,aAAS,IAAI,OAAO,IAAI,QAAQ,IAAI,IAAI,GAAG,KAAK,MAAM;AACpD,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,IAC5B;AACA,aAAS,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,KAAK,MAAM;AAC/C,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,CAAC;AAC/B,QAAE,CAAC,KAAKA,OAAM,KAAK,EAAE,KAAK,IAAI,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,CAAC,MAAM;AAC9E,MAAAA,QAAO,EAAE,KAAK,IAAI,OAAO,IAAI,EAAE,CAAC;AAAA,IAClC;AAAA,EACF;AACF;AAGA,SAAS,MAAM,QAAQ;AACrB,QAAM,IAAI,IAAI,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,QAAI,GAAG,QAAQ,SAAS,OAAQ;AAChC,QAAIA,OAAM,SAAS,EAAE,KAAK;AAC1B,UAAMC,KAAI,OAAO;AACjB,aAAS,IAAI,OAAO,IAAI,QAAQA,IAAG,IAAI,GAAG,KAAK,MAAM;AACnD,MAAAD,QAAO,EAAE,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,IAC5B;AACA,aAAS,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,KAAK,MAAM;AAC/C,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,IAAIC,EAAC,CAAC;AAC9B,QAAE,CAAC,IAAID,OAAM;AACb,MAAAA,QAAO,EAAE,KAAK,IAAI,OAAO,IAAIC,EAAC,CAAC;AAAA,IACjC;AAAA,EACF;AACF;;;AClHe,SAAR,MAAuB,QAAQ,SAAS;AAC7C,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,UAAEA;AAAA,MACJ;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,UAAED;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACjBA,SAAS,OAAOE,QAAO;AACrB,SAAOA,OAAM,SAAS;AACxB;AAEA,SAAS,MAAMC,SAAQ;AACrB,SAAO,EAAEA,UAAS;AACpB;AAEA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW,YAAY,YAAY,SAAS,SAAS,MAAM,KAAK,MAAM;AACtF;AAEA,SAAS,QAAQC,SAAQ;AACvB,SAAO,YAAUA,QAAO,GAAG,MAAM;AACnC;AAEe,SAAR,SAA0B,QAAQ;AACvC,QAAMA,UAAS,OAAO,OAAO,OAAO,SAAS,CAAC,MAAM,cAAc,QAAQ,OAAO,IAAI,CAAC;AACtF,WAAS,OAAO,IAAI,QAAQ;AAC5B,QAAM,UAAU,OAAO,IAAI,MAAM;AACjC,QAAM,IAAI,OAAO,SAAS;AAC1B,QAAMC,SAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC;AACrC,QAAM,UAAU,CAAC;AACjB,MAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAG,QAAO;AACzC,SAAO,MAAM;AACX,YAAQ,KAAKA,OAAM,IAAI,CAACC,IAAGC,OAAM,OAAOA,EAAC,EAAED,EAAC,CAAC,CAAC;AAC9C,QAAI,IAAI;AACR,WAAO,EAAED,OAAM,CAAC,MAAM,QAAQ,CAAC,GAAG;AAChC,UAAI,MAAM,EAAG,QAAOD,UAAS,QAAQ,IAAIA,OAAM,IAAI;AACnD,MAAAC,OAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF;AACF;;;AChCe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,MAAIG,OAAM,GAAGC,SAAQ;AACrB,SAAO,aAAa,KAAK,QAAQ,YAAY,SACzC,OAAMD,QAAO,CAAC,KAAK,IACnB,OAAMA,QAAO,CAAC,QAAQ,GAAGC,UAAS,MAAM,KAAK,CAAE;AACrD;;;ACLe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIC,SAAQ;AACZ,MAAI;AACJ,MAAIC,QAAO;AACX,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,gBAAQ,QAAQD;AAChB,QAAAA,SAAQ,QAAQ,EAAED;AAClB,QAAAE,QAAO,SAAS,QAAQD;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,gBAAQ,QAAQF;AAChB,QAAAA,SAAQ,QAAQ,EAAED;AAClB,QAAAE,QAAO,SAAS,QAAQD;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAID,SAAQ,EAAG,QAAOE,QAAOF,SAAQ;AACvC;;;ACtBe,SAAR,UAA2B,QAAQ,SAAS;AACjD,QAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,SAAO,IAAI,KAAK,KAAK,CAAC,IAAI;AAC5B;;;ACLe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,MAAII;AACJ,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,MAAM;AACjB,YAAID,SAAQ,QAAW;AACrB,cAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,QAClC,OAAO;AACL,cAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,cAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,MAAM;AACrD,YAAIF,SAAQ,QAAW;AACrB,cAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,QAClC,OAAO;AACL,cAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,cAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAACD,MAAKC,IAAG;AAClB;;;AC3BO,IAAM,QAAN,MAAY;AAAA,EACjB,cAAc;AACZ,SAAK,YAAY,IAAI,aAAa,EAAE;AACpC,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,IAAIE,IAAG;AACL,UAAM,IAAI,KAAK;AACf,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK;AAC1C,YAAMC,KAAI,EAAE,CAAC,GACX,KAAKD,KAAIC,IACT,KAAK,KAAK,IAAID,EAAC,IAAI,KAAK,IAAIC,EAAC,IAAID,MAAK,KAAKC,MAAKA,MAAK,KAAKD;AAC5D,UAAI,GAAI,GAAE,GAAG,IAAI;AACjB,MAAAA,KAAI;AAAA,IACN;AACA,MAAE,CAAC,IAAIA;AACP,SAAK,KAAK,IAAI;AACd,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,UAAM,IAAI,KAAK;AACf,QAAI,IAAI,KAAK,IAAIA,IAAGC,IAAG,IAAI,KAAK;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,EAAE,EAAE,CAAC;AACV,aAAO,IAAI,GAAG;AACZ,QAAAD,KAAI;AACJ,QAAAC,KAAI,EAAE,EAAE,CAAC;AACT,aAAKD,KAAIC;AACT,aAAKA,MAAK,KAAKD;AACf,YAAI,GAAI;AAAA,MACV;AACA,UAAI,IAAI,MAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,KAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,IAAK;AACnE,QAAAC,KAAI,KAAK;AACT,QAAAD,KAAI,KAAKC;AACT,YAAIA,MAAKD,KAAI,GAAI,MAAKA;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,KAAK,QAAQ,SAAS;AACpC,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,OAAO;AAClB,cAAM,IAAI,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,cAAM,IAAI,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAEO,SAAS,QAAQ,QAAQ,SAAS;AACvC,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAIA,SAAQ;AACZ,SAAO,aAAa;AAAA,IAAK;AAAA,IAAQ,YAAY,SACvC,OAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IACtB,OAAK,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAEA,QAAO,MAAM,KAAK,CAAC;AAAA,EACtD;AACF;;;ACpEO,IAAM,YAAN,cAAwB,IAAI;AAAA,EACjC,YAAY,SAAS,MAAM,OAAO;AAChC,UAAM;AACN,WAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,QAAI,WAAW,KAAM,YAAW,CAACC,MAAK,KAAK,KAAK,QAAS,MAAK,IAAIA,MAAK,KAAK;AAAA,EAC9E;AAAA,EACA,IAAI,KAAK;AACP,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,KAAK,OAAO;AACd,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK;AAAA,EAC/C;AAAA,EACA,OAAO,KAAK;AACV,WAAO,MAAM,OAAO,cAAc,MAAM,GAAG,CAAC;AAAA,EAC9C;AACF;AAEO,IAAM,YAAN,cAAwB,IAAI;AAAA,EACjC,YAAY,QAAQ,MAAM,OAAO;AAC/B,UAAM;AACN,WAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,QAAI,UAAU,KAAM,YAAW,SAAS,OAAQ,MAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,IAAI,OAAO;AACT,WAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,MAAM,OAAO,cAAc,MAAM,KAAK,CAAC;AAAA,EAChD;AACF;AAEA,SAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,QAAM,MAAM,KAAK,KAAK;AACtB,SAAO,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,IAAI;AAC/C;AAEA,SAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,QAAM,MAAM,KAAK,KAAK;AACtB,MAAI,QAAQ,IAAI,GAAG,EAAG,QAAO,QAAQ,IAAI,GAAG;AAC5C,UAAQ,IAAI,KAAK,KAAK;AACtB,SAAO;AACT;AAEA,SAAS,cAAc,EAAC,SAAS,KAAI,GAAG,OAAO;AAC7C,QAAM,MAAM,KAAK,KAAK;AACtB,MAAI,QAAQ,IAAI,GAAG,GAAG;AACpB,YAAQ,QAAQ,IAAI,GAAG;AACvB,YAAQ,OAAO,GAAG;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,MAAM,OAAO;AACpB,SAAO,UAAU,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ,IAAI;AACzE;;;AC5De,SAAR,SAA0BC,IAAG;AAClC,SAAOA;AACT;;;ACCe,SAAR,MAAuB,WAAW,MAAM;AAC7C,SAAO,KAAK,QAAQ,UAAU,UAAU,IAAI;AAC9C;AAEO,SAAS,OAAO,WAAW,MAAM;AACtC,SAAO,KAAK,QAAQ,MAAM,MAAM,UAAU,IAAI;AAChD;AAEA,SAAS,QAAQC,SAAQ,MAAM;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,IAAAA,UAASA,QAAO,QAAQ,OAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,EAChF;AACA,SAAOA;AACT;AAEO,SAAS,UAAU,WAAW,MAAM;AACzC,SAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,GAAG,IAAI;AAC9C;AAEO,SAAS,WAAW,QAAQC,YAAW,MAAM;AAClD,SAAO,QAAQ,QAAQ,QAAQA,SAAQ,GAAG,IAAI,GAAG,IAAI;AACvD;AAEO,SAAS,OAAO,QAAQA,YAAW,MAAM;AAC9C,SAAO,KAAK,QAAQ,UAAUA,SAAQ,IAAI;AAC5C;AAEO,SAAS,QAAQ,QAAQA,YAAW,MAAM;AAC/C,SAAO,KAAK,QAAQ,MAAM,MAAMA,SAAQ,IAAI;AAC9C;AAEO,SAAS,MAAM,WAAW,MAAM;AACrC,SAAO,KAAK,QAAQ,UAAU,QAAQ,IAAI;AAC5C;AAEO,SAAS,QAAQ,WAAW,MAAM;AACvC,SAAO,KAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAC9C;AAEA,SAAS,OAAO,QAAQ;AACtB,MAAI,OAAO,WAAW,EAAG,OAAM,IAAI,MAAM,eAAe;AACxD,SAAO,OAAO,CAAC;AACjB;AAEA,SAAS,KAAK,QAAQC,MAAKD,SAAQ,MAAM;AACvC,SAAQ,SAAS,QAAQE,SAAQ,GAAG;AAClC,QAAI,KAAK,KAAK,OAAQ,QAAOF,QAAOE,OAAM;AAC1C,UAAMH,UAAS,IAAI,UAAU;AAC7B,UAAMI,SAAQ,KAAK,GAAG;AACtB,QAAIC,SAAQ;AACZ,eAAW,SAASF,SAAQ;AAC1B,YAAM,MAAMC,OAAM,OAAO,EAAEC,QAAOF,OAAM;AACxC,YAAMG,SAAQN,QAAO,IAAI,GAAG;AAC5B,UAAIM,OAAO,CAAAA,OAAM,KAAK,KAAK;AAAA,UACtB,CAAAN,QAAO,IAAI,KAAK,CAAC,KAAK,CAAC;AAAA,IAC9B;AACA,eAAW,CAAC,KAAKG,OAAM,KAAKH,SAAQ;AAClC,MAAAA,QAAO,IAAI,KAAK,QAAQG,SAAQ,CAAC,CAAC;AAAA,IACpC;AACA,WAAOD,KAAIF,OAAM;AAAA,EACnB,EAAG,QAAQ,CAAC;AACd;;;AChEe,SAAR,QAAyB,QAAQ,MAAM;AAC5C,SAAO,MAAM,KAAK,MAAM,SAAO,OAAO,GAAG,CAAC;AAC5C;;;ACCe,SAAR,KAAsB,WAAW,GAAG;AACzC,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,WAAS,MAAM,KAAK,MAAM;AAC1B,MAAI,CAAC,CAAC,IAAI;AACV,MAAK,KAAK,EAAE,WAAW,KAAM,EAAE,SAAS,GAAG;AACzC,UAAMO,SAAQ,YAAY,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC;AAClD,QAAI,EAAE,SAAS,GAAG;AAChB,UAAI,EAAE,IAAI,CAAAC,OAAK,OAAO,IAAIA,EAAC,CAAC;AAC5B,MAAAD,OAAM,KAAK,CAAC,GAAG,MAAM;AACnB,mBAAWC,MAAK,GAAG;AACjB,gBAAMC,KAAI,iBAAiBD,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACrC,cAAIC,GAAG,QAAOA;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,OAAO,IAAI,CAAC;AAChB,MAAAF,OAAM,KAAK,CAAC,GAAG,MAAM,iBAAiB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,IACnD;AACA,WAAO,QAAQ,QAAQA,MAAK;AAAA,EAC9B;AACA,SAAO,OAAO,KAAK,eAAe,CAAC,CAAC;AACtC;AAEO,SAAS,eAAe,UAAU,WAAW;AAClD,MAAI,YAAY,UAAW,QAAO;AAClC,MAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,2BAA2B;AAClF,SAAO,CAACG,IAAG,MAAM;AACf,UAAMC,KAAI,QAAQD,IAAG,CAAC;AACtB,QAAIC,MAAKA,OAAM,EAAG,QAAOA;AACzB,YAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,QAAQD,IAAGA,EAAC,MAAM;AAAA,EACpD;AACF;AAEO,SAAS,iBAAiBA,IAAG,GAAG;AACrC,UAAQA,MAAK,QAAQ,EAAEA,MAAKA,QAAO,KAAK,QAAQ,EAAE,KAAK,QAAQA,KAAI,IAAI,KAAKA,KAAI,IAAI,IAAI;AAC1F;;;AClCe,SAAR,UAA2B,QAAQE,SAAQ,KAAK;AACrD,UAAQA,QAAO,WAAW,IACtB,KAAK,OAAO,QAAQA,SAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,UAAU,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,IAClG,KAAK,MAAM,QAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAMA,QAAO,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,GACvF,IAAI,CAAC,CAACC,IAAG,MAAMA,IAAG;AACvB;;;ACTA,IAAM,MAAM,KAAK,KAAK,EAAE;AAAxB,IACI,KAAK,KAAK,KAAK,EAAE;AADrB,IAEI,KAAK,KAAK,KAAK,CAAC;AAEpB,SAAS,SAAS,OAAO,MAAMC,QAAO;AACpC,QAAM,QAAQ,OAAO,SAAS,KAAK,IAAI,GAAGA,MAAK,GAC3C,QAAQ,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,GACnC,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK,GACjC,SAAS,SAAS,MAAM,KAAK,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AACrE,MAAI,IAAI,IAAI;AACZ,MAAI,QAAQ,GAAG;AACb,UAAM,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI;AAC7B,SAAK,KAAK,MAAM,QAAQ,GAAG;AAC3B,SAAK,KAAK,MAAM,OAAO,GAAG;AAC1B,QAAI,KAAK,MAAM,MAAO,GAAE;AACxB,QAAI,KAAK,MAAM,KAAM,GAAE;AACvB,UAAM,CAAC;AAAA,EACT,OAAO;AACL,UAAM,KAAK,IAAI,IAAI,KAAK,IAAI;AAC5B,SAAK,KAAK,MAAM,QAAQ,GAAG;AAC3B,SAAK,KAAK,MAAM,OAAO,GAAG;AAC1B,QAAI,KAAK,MAAM,MAAO,GAAE;AACxB,QAAI,KAAK,MAAM,KAAM,GAAE;AAAA,EACzB;AACA,MAAI,KAAK,MAAM,OAAOA,UAASA,SAAQ,EAAG,QAAO,SAAS,OAAO,MAAMA,SAAQ,CAAC;AAChF,SAAO,CAAC,IAAI,IAAI,GAAG;AACrB;AAEe,SAAR,MAAuB,OAAO,MAAMA,QAAO;AAChD,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,MAAI,EAAEA,SAAQ,GAAI,QAAO,CAAC;AAC1B,MAAI,UAAU,KAAM,QAAO,CAAC,KAAK;AACjC,QAAMC,WAAU,OAAO,OAAO,CAAC,IAAI,IAAI,GAAG,IAAIA,WAAU,SAAS,MAAM,OAAOD,MAAK,IAAI,SAAS,OAAO,MAAMA,MAAK;AAClH,MAAI,EAAE,MAAM,IAAK,QAAO,CAAC;AACzB,QAAM,IAAI,KAAK,KAAK,GAAGE,SAAQ,IAAI,MAAM,CAAC;AAC1C,MAAID,UAAS;AACX,QAAI,MAAM,EAAG,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAC,OAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,QAC3D,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,EACzD,OAAO;AACL,QAAI,MAAM,EAAG,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,QAC3D,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,EACzD;AACA,SAAOA;AACT;AAEO,SAAS,cAAc,OAAO,MAAMF,QAAO;AAChD,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,SAAO,SAAS,OAAO,MAAMA,MAAK,EAAE,CAAC;AACvC;AAEO,SAAS,SAAS,OAAO,MAAMA,QAAO;AAC3C,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,QAAMC,WAAU,OAAO,OAAO,MAAMA,WAAU,cAAc,MAAM,OAAOD,MAAK,IAAI,cAAc,OAAO,MAAMA,MAAK;AAClH,UAAQC,WAAU,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM;AACpD;;;ACpDe,SAAR,KAAsB,OAAO,MAAME,QAAO;AAC/C,MAAI;AACJ,SAAO,MAAM;AACX,UAAM,OAAO,cAAc,OAAO,MAAMA,MAAK;AAC7C,QAAI,SAAS,WAAW,SAAS,KAAK,CAAC,SAAS,IAAI,GAAG;AACrD,aAAO,CAAC,OAAO,IAAI;AAAA,IACrB,WAAW,OAAO,GAAG;AACnB,cAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI;AACnC,aAAO,KAAK,KAAK,OAAO,IAAI,IAAI;AAAA,IAClC,WAAW,OAAO,GAAG;AACnB,cAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AAClC,aAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,IACnC;AACA,cAAU;AAAA,EACZ;AACF;;;ACfe,SAAR,iBAAkC,QAAQ;AAC/C,SAAO,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;AACtE;;;ACJA,IAAI,QAAQ,MAAM;AAEX,IAAI,QAAQ,MAAM;AAClB,IAAI,MAAM,MAAM;;;ACHR,SAAR,SAA0BC,IAAG;AAClC,SAAO,MAAMA;AACf;;;ACOe,SAAR,MAAuB;AAC5B,MAAI,QAAQ,UACR,SAAS,QACTC,aAAY;AAEhB,WAAS,UAAU,MAAM;AACvB,QAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO,MAAM,KAAK,IAAI;AAEhD,QAAI,GACA,IAAI,KAAK,QACTC,IACA,MACA,SAAS,IAAI,MAAM,CAAC;AAExB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,CAAC,IAAI,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI;AAAA,IACpC;AAEA,QAAI,KAAK,OAAO,MAAM,GAClB,KAAK,GAAG,CAAC,GACT,KAAK,GAAG,CAAC,GACT,KAAKD,WAAU,QAAQ,IAAI,EAAE;AAIjC,QAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,YAAME,OAAM,IAAI,KAAK,CAAC;AACtB,UAAI,WAAW,OAAQ,EAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACjD,WAAK,MAAM,IAAI,IAAI,EAAE;AAKrB,UAAI,GAAG,CAAC,KAAK,GAAI,QAAO,cAAc,IAAI,IAAI,EAAE;AAShD,UAAI,GAAG,GAAG,SAAS,CAAC,KAAK,IAAI;AAC3B,YAAIA,QAAO,MAAM,WAAW,QAAQ;AAClC,gBAAMC,QAAO,cAAc,IAAI,IAAI,EAAE;AACrC,cAAI,SAASA,KAAI,GAAG;AAClB,gBAAIA,QAAO,GAAG;AACZ,oBAAM,KAAK,MAAM,KAAKA,KAAI,IAAI,KAAKA;AAAA,YACrC,WAAWA,QAAO,GAAG;AACnB,oBAAM,KAAK,KAAK,KAAK,CAACA,KAAI,IAAI,KAAK,CAACA;AAAA,YACtC;AAAA,UACF;AAAA,QACF,OAAO;AACL,aAAG,IAAI;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAIA,QAAI,IAAI,GAAG,QAAQC,KAAI,GAAG,IAAI;AAC9B,WAAO,GAAGA,EAAC,KAAK,GAAI,GAAEA;AACtB,WAAO,GAAG,IAAI,CAAC,IAAI,GAAI,GAAE;AACzB,QAAIA,MAAK,IAAI,EAAG,MAAK,GAAG,MAAMA,IAAG,CAAC,GAAG,IAAI,IAAIA;AAE7C,QAAI,OAAO,IAAI,MAAM,IAAI,CAAC,GACtBC;AAGJ,SAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,MAAAA,OAAM,KAAK,CAAC,IAAI,CAAC;AACjB,MAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;AAC7B,MAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,IAC3B;AAGA,QAAI,SAAS,IAAI,GAAG;AAClB,UAAI,OAAO,GAAG;AACZ,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAKJ,KAAI,OAAO,CAAC,MAAM,QAAQ,MAAMA,MAAKA,MAAK,IAAI;AACjD,iBAAK,KAAK,IAAI,GAAG,KAAK,OAAOA,KAAI,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF,WAAW,OAAO,GAAG;AACnB,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAKA,KAAI,OAAO,CAAC,MAAM,QAAQ,MAAMA,MAAKA,MAAK,IAAI;AACjD,kBAAM,IAAI,KAAK,OAAO,KAAKA,MAAK,IAAI;AACpC,iBAAK,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,KAAKA,GAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAKA,KAAI,OAAO,CAAC,MAAM,QAAQ,MAAMA,MAAKA,MAAK,IAAI;AACjD,eAAK,eAAO,IAAIA,IAAG,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,aAAa;AAAA,EAC7F;AAEA,YAAU,SAAS,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,aAAa;AAAA,EACzG;AAEA,YAAU,aAAa,SAAS,GAAG;AACjC,WAAO,UAAU,UAAUD,aAAY,OAAO,MAAM,aAAa,IAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,aAAaA;AAAA,EACpI;AAEA,SAAO;AACT;;;AC5He,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIM;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCD,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIE;AACJ,MAAIC,YAAW;AACf,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,QAAEA;AACF,UAAI,SAAS,SACLF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACrBe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIE;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCD,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIE;AACJ,MAAIC,YAAW;AACf,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,QAAEA;AACF,UAAI,SAAS,SACLF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACjBe,SAAR,YAA6BE,QAAOC,IAAG,OAAO,GAAG,QAAQ,UAAU,SAAS;AACjF,EAAAA,KAAI,KAAK,MAAMA,EAAC;AAChB,SAAO,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACnC,UAAQ,KAAK,MAAM,KAAK,IAAID,OAAM,SAAS,GAAG,KAAK,CAAC;AAEpD,MAAI,EAAE,QAAQC,MAAKA,MAAK,OAAQ,QAAOD;AAEvC,YAAU,YAAY,SAAY,mBAAmB,eAAe,OAAO;AAE3E,SAAO,QAAQ,MAAM;AACnB,QAAI,QAAQ,OAAO,KAAK;AACtB,YAAM,IAAI,QAAQ,OAAO;AACzB,YAAM,IAAIC,KAAI,OAAO;AACrB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAMC,KAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,YAAM,KAAK,MAAM,KAAK,KAAK,IAAIA,MAAK,IAAIA,MAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxE,YAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAMD,KAAI,IAAIC,KAAI,IAAI,EAAE,CAAC;AAC7D,YAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAMD,MAAK,IAAI,KAAKC,KAAI,IAAI,EAAE,CAAC;AACrE,kBAAYF,QAAOC,IAAG,SAAS,UAAU,OAAO;AAAA,IAClD;AAEA,UAAM,IAAID,OAAMC,EAAC;AACjB,QAAI,IAAI;AACR,QAAI,IAAI;AAER,SAAKD,QAAO,MAAMC,EAAC;AACnB,QAAI,QAAQD,OAAM,KAAK,GAAG,CAAC,IAAI,EAAG,MAAKA,QAAO,MAAM,KAAK;AAEzD,WAAO,IAAI,GAAG;AACZ,WAAKA,QAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,aAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AACnC,aAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AAAA,IACrC;AAEA,QAAI,QAAQA,OAAM,IAAI,GAAG,CAAC,MAAM,EAAG,MAAKA,QAAO,MAAM,CAAC;AAAA,QACjD,GAAE,GAAG,KAAKA,QAAO,GAAG,KAAK;AAE9B,QAAI,KAAKC,GAAG,QAAO,IAAI;AACvB,QAAIA,MAAK,EAAG,SAAQ,IAAI;AAAA,EAC1B;AAEA,SAAOD;AACT;AAEA,SAAS,KAAKA,QAAO,GAAG,GAAG;AACzB,QAAM,IAAIA,OAAM,CAAC;AACjB,EAAAA,OAAM,CAAC,IAAIA,OAAM,CAAC;AAClB,EAAAA,OAAM,CAAC,IAAI;AACb;;;AClDe,SAAR,SAA0B,QAAQ,UAAU,WAAW;AAC5D,MAAIG;AACJ,MAAI,UAAU;AACd,MAAI,QAAQ,WAAW,GAAG;AACxB,QAAI;AACJ,eAAW,WAAW,QAAQ;AAC5B,YAAM,QAAQ,QAAQ,OAAO;AAC7B,UAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,QAAAA,OAAM;AACN,mBAAW;AACX,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,SAAS,QAAQ;AAC1B,UAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,QAAAA,OAAM;AACN,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,GAAG,SAAS;AACnD,WAAS,aAAa,KAAK,QAAQ,QAAQ,OAAO,CAAC;AACnD,MAAI,EAAE,IAAI,OAAO,WAAW,MAAM,IAAI,CAAC,CAAC,EAAG;AAC3C,MAAI,KAAK,KAAK,IAAI,EAAG,QAAO,IAAI,MAAM;AACtC,MAAI,KAAK,EAAG,QAAO,IAAI,MAAM;AAC7B,MAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,IAAI,YAAY,QAAQ,EAAE,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC,GACxD,SAAS,IAAI,OAAO,SAAS,KAAK,CAAC,CAAC;AACxC,SAAO,UAAU,SAAS,WAAW,IAAI;AAC3C;AAEO,SAAS,eAAe,QAAQ,GAAG,UAAU,QAAQ;AAC1D,MAAI,EAAE,IAAI,OAAO,WAAW,MAAM,IAAI,CAAC,CAAC,EAAG;AAC3C,MAAI,KAAK,KAAK,IAAI,EAAG,QAAO,CAAC,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM;AACzD,MAAI,KAAK,EAAG,QAAO,CAAC,QAAQ,OAAO,IAAI,CAAC,GAAG,IAAI,GAAG,MAAM;AACxD,MAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,CAAC,QAAQ,OAAO,EAAE,GAAG,IAAI,MAAM,GACxC,SAAS,CAAC,QAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;AACpD,SAAO,UAAU,SAAS,WAAW,IAAI;AAC3C;AAEO,SAAS,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACzD,MAAI,MAAM,IAAI,CAAC,CAAC,EAAG;AACnB,EAAAC,WAAU,aAAa,KAAK,QAAQ,CAAC,GAAGC,OAAM,OAAO,QAAQ,OAAOA,EAAC,GAAGA,IAAG,MAAM,CAAC,CAAC;AACnF,MAAI,KAAK,EAAG,QAAO,SAASD,QAAO;AACnC,MAAI,KAAK,EAAG,QAAO,SAASA,QAAO;AACnC,MAAIA,UACAE,SAAQ,YAAY,KAAK,QAAQ,CAAC,GAAGD,OAAMA,EAAC,GAC5C,IAAID,SAAQ,SAAS,GACrB,IAAI,KAAK,MAAM,IAAI,CAAC;AACxB,cAAYE,QAAO,GAAG,GAAG,GAAG,CAACD,IAAGE,OAAM,iBAAiBH,SAAQC,EAAC,GAAGD,SAAQG,EAAC,CAAC,CAAC;AAC9E,MAAI,SAASD,OAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAACD,OAAMD,SAAQC,EAAC,CAAC;AACxD,SAAO,KAAK,IAAI,IAAI;AACtB;;;AC3Ce,SAAR,0BAA2C,QAAQG,MAAKC,MAAK;AAClE,QAAMC,KAAI,MAAM,MAAM,GAAG,IAAI,SAAS,QAAQ,IAAI,IAAI,SAAS,QAAQ,IAAI;AAC3E,SAAOA,MAAK,IAAI,KAAK,MAAMD,OAAMD,SAAQ,IAAI,IAAI,KAAK,IAAIE,IAAG,KAAK,CAAC,EAAE,IAAI;AAC3E;;;ACHe,SAAR,eAAgC,QAAQC,MAAKC,MAAK;AACvD,QAAMC,KAAI,MAAM,MAAM,GAAG,IAAI,UAAU,MAAM;AAC7C,SAAOA,MAAK,IAAI,KAAK,MAAMD,OAAMD,QAAO,KAAK,KAAKE,EAAC,KAAK,OAAO,EAAE,IAAI;AACvE;;;ACNe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,MAAIC,SAAQ;AACZ,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,UAAED,QAAOC,QAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,UAAEF,QAAOC,QAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAID,OAAO,QAAOC,OAAMD;AAC1B;;;AChBe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,SAAO,SAAS,QAAQ,KAAK,OAAO;AACtC;AAEO,SAAS,YAAY,QAAQ,SAAS;AAC3C,SAAO,cAAc,QAAQ,KAAK,OAAO;AAC3C;;;ACRA,UAAUG,SAAQ,QAAQ;AACxB,aAAWC,UAAS,QAAQ;AAC1B,WAAOA;AAAA,EACT;AACF;AAEe,SAAR,MAAuB,QAAQ;AACpC,SAAO,MAAM,KAAKD,SAAQ,MAAM,CAAC;AACnC;;;ACNe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,QAAM,SAAS,IAAI,UAAU;AAC7B,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,QAAQ,SAAS,OAAO;AACnC,eAAO,IAAI,QAAQ,OAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,QAAQ,SAAS,OAAO;AACvE,eAAO,IAAI,QAAQ,OAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACJ,MAAI,YAAY;AAChB,aAAW,CAAC,OAAOC,MAAK,KAAK,QAAQ;AACnC,QAAIA,SAAQ,WAAW;AACrB,kBAAYA;AACZ,kBAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;;;AC3Be,SAAR,MAAuB,QAAQ,SAAS,MAAM;AACnD,QAAMC,SAAQ,CAAC;AACf,MAAI;AACJ,MAAI,QAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAO,CAAAA,OAAM,KAAK,OAAO,UAAU,KAAK,CAAC;AAC7C,eAAW;AACX,YAAQ;AAAA,EACV;AACA,SAAOA;AACT;AAEO,SAAS,KAAKC,IAAG,GAAG;AACzB,SAAO,CAACA,IAAG,CAAC;AACd;;;ACde,SAAR,MAAuB,OAAO,MAAM,MAAM;AAC/C,UAAQ,CAAC,OAAO,OAAO,CAAC,MAAM,QAAQ,IAAI,UAAU,UAAU,KAAK,OAAO,OAAO,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC;AAE9G,MAAI,IAAI,IACJ,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,GACpDC,SAAQ,IAAI,MAAM,CAAC;AAEvB,SAAO,EAAE,IAAI,GAAG;AACd,IAAAA,OAAM,CAAC,IAAI,QAAQ,IAAI;AAAA,EACzB;AAEA,SAAOA;AACT;;;ACTe,SAAR,KAAsB,QAAQ,UAAU,WAAW;AACxD,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,MAAI,IAAI,MAAM,KAAK,MAAM;AACzB,QAAM,IAAI,IAAI,aAAa,EAAE,MAAM;AACnC,MAAI,QAAQ,WAAW,EAAG,KAAI,EAAE,IAAI,OAAO,GAAG,UAAU;AACxD,QAAM,eAAe,CAAC,GAAG,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACjD,MAAIC,IAAG;AACP,WAAS,YAAY,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;AAExC,SAAO,KAAK,YAAY,YAAY,CAAC,GAAG,MAAM,iBAAiB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,eAAe,YAAY,CAAC;AACzG,SAAO,QAAQ,CAAC,GAAG,MAAM;AACrB,UAAMC,KAAI,aAAa,GAAGD,OAAM,SAAY,IAAIA,EAAC;AACjD,QAAIC,MAAK,GAAG;AACV,UAAID,OAAM,UAAaC,KAAI,EAAG,CAAAD,KAAI,GAAG,IAAI;AACzC,QAAE,CAAC,IAAI;AAAA,IACT,OAAO;AACL,QAAE,CAAC,IAAI;AAAA,IACT;AAAA,EACF,CAAC;AACH,SAAO;AACT;;;ACrBe,SAAR,MAAuB,QAAQ,UAAU,WAAW;AACzD,MAAIE;AACJ,MAAI,UAAU;AACd,MAAI,QAAQ,WAAW,GAAG;AACxB,QAAI;AACJ,eAAW,WAAW,QAAQ;AAC5B,YAAM,QAAQ,QAAQ,OAAO;AAC7B,UAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,QAAAA,OAAM;AACN,mBAAW;AACX,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,SAAS,QAAQ;AAC1B,UAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,QAAAA,OAAM;AACN,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACzBe,SAAR,WAA4B,QAAQ,UAAU,WAAW;AAC9D,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,MAAI;AACJ,MAAIC,OAAM;AACV,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,MAAEA;AACF,QAAID,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,iBAAW;AACX,MAAAA,OAAMC;AAAA,IACR;AAAA,EACF;AACA,SAAOD;AACT;;;ACfe,SAAR,cAA+B,QAAQ,UAAU,WAAW;AACjE,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,MAAI;AACJ,MAAIE,OAAM;AACV,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,MAAEA;AACF,QAAID,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,iBAAW;AACX,MAAAA,OAAMC;AAAA,IACR;AAAA,EACF;AACA,SAAOD;AACT;;;AChBe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,QAAME,SAAQ,WAAW,QAAQ,OAAO;AACxC,SAAOA,SAAQ,IAAI,SAAYA;AACjC;;;ACLA,IAAO,kBAAQ,SAAS,KAAK,MAAM;AAE5B,SAAS,SAAS,QAAQ;AAC/B,SAAO,SAAS,QAAQC,QAAO,KAAK,GAAG,KAAKA,OAAM,QAAQ;AACxD,QAAI,IAAI,MAAM,KAAK,CAAC;AACpB,WAAO,GAAG;AACR,YAAM,IAAI,OAAO,IAAI,MAAM,GAAG,IAAIA,OAAM,IAAI,EAAE;AAC9C,MAAAA,OAAM,IAAI,EAAE,IAAIA,OAAM,IAAI,EAAE;AAC5B,MAAAA,OAAM,IAAI,EAAE,IAAI;AAAA,IAClB;AACA,WAAOA;AAAA,EACT;AACF;;;ACZe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,OAAO;AAClB,QAAAA,QAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,QAAAD,QAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACfe,SAAR,UAA2B,QAAQ;AACxC,MAAI,EAAE,IAAI,OAAO,QAAS,QAAO,CAAC;AAClC,WAAS,IAAI,IAAI,IAAI,IAAI,QAAQE,OAAM,GAAGC,aAAY,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC5E,aAAS,IAAI,IAAI,GAAG,MAAMA,WAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC/D,UAAI,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAASD,QAAO,GAAG;AACjB,SAAO,EAAE;AACX;;;ACZe,SAAR,MAAuB;AAC5B,SAAO,UAAU,SAAS;AAC5B;;;ACJe,SAAR,MAAuB,QAAQ,MAAM;AAC1C,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,MAAIE,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,CAAC,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACTe,SAAR,KAAsB,QAAQ,MAAM;AACzC,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACTe,SAAR,OAAwB,QAAQ,MAAM;AAC3C,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,QAAMC,SAAQ,CAAC;AACf,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,MAAAD,OAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AACA,SAAOA;AACT;;;ACVe,SAARE,KAAqB,QAAQ,QAAQ;AAC1C,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,MAAI,OAAO,WAAW,WAAY,OAAM,IAAI,UAAU,0BAA0B;AAChF,SAAO,MAAM,KAAK,QAAQ,CAAC,OAAOC,WAAU,OAAO,OAAOA,QAAO,MAAM,CAAC;AAC1E;;;ACJe,SAAR,OAAwB,QAAQC,UAAS,OAAO;AACrD,MAAI,OAAOA,aAAY,WAAY,OAAM,IAAI,UAAU,2BAA2B;AAClF,QAAM,WAAW,OAAO,OAAO,QAAQ,EAAE;AACzC,MAAI,MAAM,MAAMC,SAAQ;AACxB,MAAI,UAAU,SAAS,GAAG;AACxB,KAAC,EAAC,MAAM,MAAK,IAAI,SAAS,KAAK;AAC/B,QAAI,KAAM;AACV,MAAEA;AAAA,EACJ;AACA,SAAQ,EAAC,MAAM,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI,CAAC,MAAM;AACrD,YAAQD,SAAQ,OAAO,MAAM,EAAEC,QAAO,MAAM;AAAA,EAC9C;AACA,SAAO;AACT;;;ACbe,SAAR,QAAyB,QAAQ;AACtC,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,SAAO,MAAM,KAAK,MAAM,EAAE,QAAQ;AACpC;;;ACDe,SAAR,WAA4B,WAAW,QAAQ;AACpD,WAAS,IAAI,UAAU,MAAM;AAC7B,aAAW,SAAS,QAAQ;AAC1B,eAAW,SAAS,OAAO;AACzB,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;;;ACRe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,QAAM,WAAW,MAAM,OAAO,QAAQ,EAAE,GAAGC,OAAM,IAAI,UAAU;AAC/D,aAAW,KAAK,QAAQ;AACtB,QAAIA,KAAI,IAAI,CAAC,EAAG,QAAO;AACvB,QAAI,OAAO;AACX,WAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,UAAI,KAAM;AACV,UAAI,OAAO,GAAG,GAAG,KAAK,EAAG,QAAO;AAChC,MAAAA,KAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;;;ACZe,SAAR,aAA8B,WAAW,QAAQ;AACtD,WAAS,IAAI,UAAU,MAAM;AAC7B,WAAS,OAAO,IAAI,GAAG;AACvB,MAAK,YAAW,SAAS,QAAQ;AAC/B,eAAW,SAAS,QAAQ;AAC1B,UAAI,CAAC,MAAM,IAAI,KAAK,GAAG;AACrB,eAAO,OAAO,KAAK;AACnB,iBAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,IAAI,QAAQ;AACnB,SAAO,kBAAkB,YAAY,SAAS,IAAI,UAAU,MAAM;AACpE;;;AClBe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,QAAM,WAAW,OAAO,OAAO,QAAQ,EAAE,GAAGC,OAAM,oBAAI,IAAI;AAC1D,aAAW,KAAK,OAAO;AACrB,UAAM,KAAK,OAAO,CAAC;AACnB,QAAIA,KAAI,IAAI,EAAE,EAAG;AACjB,QAAI,OAAO;AACX,WAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,UAAI,KAAM,QAAO;AACjB,YAAM,SAAS,OAAO,KAAK;AAC3B,MAAAA,KAAI,IAAI,MAAM;AACd,UAAI,OAAO,GAAG,IAAI,MAAM,EAAG;AAAA,IAC7B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,UAAU,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ,IAAI;AACzE;;;AChBe,SAAR,OAAwB,QAAQ,OAAO;AAC5C,SAAO,SAAS,OAAO,MAAM;AAC/B;;;ACFe,SAAR,SAA0B,QAAQ;AACvC,QAAMC,OAAM,IAAI,UAAU;AAC1B,aAAW,SAAS,QAAQ;AAC1B,eAAW,KAAK,OAAO;AACrB,MAAAA,KAAI,IAAI,CAAC;AAAA,IACX;AAAA,EACF;AACA,SAAOA;AACT;;;ACVe,SAAR,eAAiB,aAAa,SAAS,WAAW;AACvD,cAAY,YAAY,QAAQ,YAAY;AAC5C,YAAU,cAAc;AAC1B;AAEO,SAAS,OAAO,QAAQ,YAAY;AACzC,MAAI,YAAY,OAAO,OAAO,OAAO,SAAS;AAC9C,WAAS,OAAO,WAAY,WAAU,GAAG,IAAI,WAAW,GAAG;AAC3D,SAAO;AACT;;;ACPO,SAAS,QAAQ;AAAC;AAElB,IAAI,SAAS;AACb,IAAI,WAAW,IAAI;AAE1B,IAAI,MAAM;AAAV,IACI,MAAM;AADV,IAEI,MAAM;AAFV,IAGI,QAAQ;AAHZ,IAII,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAJ/D,IAKI,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAL/D,IAMI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AANxE,IAOI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAPxE,IAQI,eAAe,IAAI,OAAO,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAR/D,IASI,gBAAgB,IAAI,OAAO,WAAW,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM;AAExE,IAAI,QAAQ;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AAEA,eAAO,OAAO,OAAO;AAAA,EACnB,KAAK,UAAU;AACb,WAAO,OAAO,OAAO,IAAI,KAAK,eAAa,MAAM,QAAQ;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,IAAI,EAAE,YAAY;AAAA,EAChC;AAAA,EACA,KAAK;AAAA;AAAA,EACL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AAED,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,IAAI,EAAE,WAAW;AAC/B;AAEA,SAAS,kBAAkB;AACzB,SAAO,WAAW,IAAI,EAAE,UAAU;AACpC;AAEA,SAAS,kBAAkB;AACzB,SAAO,KAAK,IAAI,EAAE,UAAU;AAC9B;AAEe,SAAR,MAAuBC,SAAQ;AACpC,MAAI,GAAG;AACP,EAAAA,WAAUA,UAAS,IAAI,KAAK,EAAE,YAAY;AAC1C,UAAQ,IAAI,MAAM,KAAKA,OAAM,MAAM,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,IACtF,MAAM,IAAI,IAAI,IAAK,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,IAAI,MAAS,IAAI,OAAQ,IAAM,IAAI,IAAM,CAAC,IAChH,MAAM,IAAI,KAAK,KAAK,KAAK,KAAM,KAAK,KAAK,KAAM,KAAK,IAAI,MAAO,IAAI,OAAQ,GAAI,IAC/E,MAAM,IAAI,KAAM,KAAK,KAAK,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,KAAK,IAAI,KAAQ,IAAI,OAAU,IAAI,OAAQ,IAAM,IAAI,MAAQ,GAAI,IACtJ,SACC,IAAI,aAAa,KAAKA,OAAM,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAC5D,IAAI,aAAa,KAAKA,OAAM,KAAK,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,CAAC,KAChG,IAAI,cAAc,KAAKA,OAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAC7D,IAAI,cAAc,KAAKA,OAAM,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC,KACjG,IAAI,aAAa,KAAKA,OAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,CAAC,KACrE,IAAI,cAAc,KAAKA,OAAM,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,IAC1E,MAAM,eAAeA,OAAM,IAAI,KAAK,MAAMA,OAAM,CAAC,IACjDA,YAAW,gBAAgB,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,IACnD;AACR;AAEA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,KAAM,KAAK,IAAI,KAAM,IAAI,KAAM,CAAC;AAC3D;AAEA,SAAS,KAAK,GAAG,GAAG,GAAGC,IAAG;AACxB,MAAIA,MAAK,EAAG,KAAI,IAAI,IAAI;AACxB,SAAO,IAAI,IAAI,GAAG,GAAG,GAAGA,EAAC;AAC3B;AAEO,SAAS,WAAW,GAAG;AAC5B,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI;AACnB,MAAI,EAAE,IAAI;AACV,SAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AACzC;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAG,GAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAASC,IAAG;AACV,IAAAA,KAAIA,MAAK,OAAO,WAAW,KAAK,IAAI,UAAUA,EAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,IAAIA,IAAG,KAAK,IAAIA,IAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,OAAOA,IAAG;AACR,IAAAA,KAAIA,MAAK,OAAO,SAAS,KAAK,IAAI,QAAQA,EAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,IAAIA,IAAG,KAAK,IAAIA,IAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EACjE;AAAA,EACA,MAAM;AACJ,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,WAAQ,QAAQ,KAAK,KAAK,KAAK,IAAI,UAC3B,QAAQ,KAAK,KAAK,KAAK,IAAI,WAC3B,QAAQ,KAAK,KAAK,KAAK,IAAI,WAC3B,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,KAAK;AAAA;AAAA,EACL,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AACZ,CAAC,CAAC;AAEF,SAAS,gBAAgB;AACvB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACpD;AAEA,SAAS,iBAAiB;AACxB,SAAO,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,WAAW,GAAG,CAAC;AAC1G;AAEA,SAAS,gBAAgB;AACvB,QAAMD,KAAI,OAAO,KAAK,OAAO;AAC7B,SAAO,GAAGA,OAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,GAAGA,OAAM,IAAI,MAAM,KAAKA,EAAC,GAAG;AACzH;AAEA,SAAS,OAAO,SAAS;AACvB,SAAO,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,OAAO,CAAC;AAC9D;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC1D;AAEA,SAAS,IAAI,OAAO;AAClB,UAAQ,OAAO,KAAK;AACpB,UAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,SAAS,EAAE;AACpD;AAEA,SAAS,KAAK,GAAGE,IAAG,GAAGF,IAAG;AACxB,MAAIA,MAAK,EAAG,KAAIE,KAAI,IAAI;AAAA,WACf,KAAK,KAAK,KAAK,EAAG,KAAIA,KAAI;AAAA,WAC1BA,MAAK,EAAG,KAAI;AACrB,SAAO,IAAI,IAAI,GAAGA,IAAG,GAAGF,EAAC;AAC3B;AAEO,SAAS,WAAW,GAAG;AAC5B,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,EAAE,aAAa,OAAQ,KAAI,MAAM,CAAC;AACtC,MAAI,CAAC,EAAG,QAAO,IAAI;AACnB,MAAI,aAAa,IAAK,QAAO;AAC7B,MAAI,EAAE,IAAI;AACV,MAAI,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACVG,OAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtBC,OAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,IAAI,KACJF,KAAIE,OAAMD,MACV,KAAKC,OAAMD,QAAO;AACtB,MAAID,IAAG;AACL,QAAI,MAAME,KAAK,MAAK,IAAI,KAAKF,MAAK,IAAI,KAAK;AAAA,aAClC,MAAME,KAAK,MAAK,IAAI,KAAKF,KAAI;AAAA,QACjC,MAAK,IAAI,KAAKA,KAAI;AACvB,IAAAA,MAAK,IAAI,MAAME,OAAMD,OAAM,IAAIC,OAAMD;AACrC,SAAK;AAAA,EACP,OAAO;AACL,IAAAD,KAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,EAC3B;AACA,SAAO,IAAI,IAAI,GAAGA,IAAG,GAAG,EAAE,OAAO;AACnC;AAEO,SAAS,IAAI,GAAGA,IAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAGA,IAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEA,SAAS,IAAI,GAAGA,IAAG,GAAG,SAAS;AAC7B,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAACA;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAASD,IAAG;AACV,IAAAA,KAAIA,MAAK,OAAO,WAAW,KAAK,IAAI,UAAUA,EAAC;AAC/C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,OAAOA,IAAG;AACR,IAAAA,KAAIA,MAAK,OAAO,SAAS,KAAK,IAAI,QAAQA,EAAC;AAC3C,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EACzD;AAAA,EACA,MAAM;AACJ,QAAI,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAClCC,KAAI,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,GACzC,IAAI,KAAK,GACT,KAAK,KAAK,IAAI,MAAM,IAAI,IAAI,KAAKA,IACjC,KAAK,IAAI,IAAI;AACjB,WAAO,IAAI;AAAA,MACT,QAAQ,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC5C,QAAQ,GAAG,IAAI,EAAE;AAAA,MACjB,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,MAC3C,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC;AAAA,EACrF;AAAA,EACA,cAAc;AACZ,YAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC,OAC1C,KAAK,KAAK,KAAK,KAAK,KAAK,OACzB,KAAK,KAAK,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,YAAY;AACV,UAAMF,KAAI,OAAO,KAAK,OAAO;AAC7B,WAAO,GAAGA,OAAM,IAAI,SAAS,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,IAAIA,OAAM,IAAI,MAAM,KAAKA,EAAC,GAAG;AAAA,EACvI;AACF,CAAC,CAAC;AAEF,SAAS,OAAO,OAAO;AACrB,WAAS,SAAS,KAAK;AACvB,SAAO,QAAQ,IAAI,QAAQ,MAAM;AACnC;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAC5C;AAGA,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC1B,UAAQ,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAChC,IAAI,MAAM,KACV,IAAI,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,KACvC,MAAM;AACd;;;AC3YO,IAAM,UAAU,KAAK,KAAK;AAC1B,IAAM,UAAU,MAAM,KAAK;;;ACIlC,IAAM,IAAI;AAAV,IACI,KAAK;AADT,IAEI,KAAK;AAFT,IAGI,KAAK;AAHT,IAII,KAAK,IAAI;AAJb,IAKI,KAAK,IAAI;AALb,IAMI,KAAK,IAAI,KAAK;AANlB,IAOI,KAAK,KAAK,KAAK;AAEnB,SAAS,WAAW,GAAG;AACrB,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,aAAa,IAAK,QAAO,QAAQ,CAAC;AACtC,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,IAAI,SAAS,EAAE,CAAC,GAChB,IAAI,SAAS,EAAE,CAAC,GAChB,IAAI,SAAS,EAAE,CAAC,GAChBK,KAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE,GAAGC,IAAG;AAC1E,MAAI,MAAM,KAAK,MAAM,EAAG,CAAAA,KAAI,IAAID;AAAA,OAAQ;AACtC,IAAAC,KAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE;AAChE,QAAI,SAAS,YAAY,IAAI,YAAY,IAAI,YAAY,KAAK,EAAE;AAAA,EAClE;AACA,SAAO,IAAI,IAAI,MAAMD,KAAI,IAAI,OAAOC,KAAID,KAAI,OAAOA,KAAI,IAAI,EAAE,OAAO;AACtE;AAEO,SAAS,KAAK,GAAG,SAAS;AAC/B,SAAO,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AACvD;AAEe,SAAR,IAAqB,GAAGE,IAAG,GAAG,SAAS;AAC5C,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAGA,IAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAGA,IAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAACA;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAASC,IAAG;AACV,WAAO,IAAI,IAAI,KAAK,IAAI,KAAKA,MAAK,OAAO,IAAIA,KAAI,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,OAAOA,IAAG;AACR,WAAO,IAAI,IAAI,KAAK,IAAI,KAAKA,MAAK,OAAO,IAAIA,KAAI,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM;AACJ,QAAIH,MAAK,KAAK,IAAI,MAAM,KACpBC,KAAI,MAAM,KAAK,CAAC,IAAID,KAAIA,KAAI,KAAK,IAAI,KACrC,IAAI,MAAM,KAAK,CAAC,IAAIA,KAAIA,KAAI,KAAK,IAAI;AACzC,IAAAC,KAAI,KAAK,QAAQA,EAAC;AAClB,IAAAD,KAAI,KAAK,QAAQA,EAAC;AAClB,QAAI,KAAK,QAAQ,CAAC;AAClB,WAAO,IAAI;AAAA,MACT,SAAU,YAAYC,KAAI,YAAYD,KAAI,YAAY,CAAC;AAAA,MACvD,SAAS,aAAaC,KAAI,YAAYD,KAAI,WAAY,CAAC;AAAA,MACvD,SAAU,YAAYC,KAAI,YAAYD,KAAI,YAAY,CAAC;AAAA,MACvD,KAAK;AAAA,IACP;AAAA,EACF;AACF,CAAC,CAAC;AAEF,SAAS,QAAQ,GAAG;AAClB,SAAO,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK;AAChD;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI;AACxC;AAEA,SAAS,SAASC,IAAG;AACnB,SAAO,OAAOA,MAAK,WAAY,QAAQA,KAAI,QAAQ,KAAK,IAAIA,IAAG,IAAI,GAAG,IAAI;AAC5E;AAEA,SAAS,SAASA,IAAG;AACnB,UAAQA,MAAK,QAAQ,UAAUA,KAAI,QAAQ,KAAK,KAAKA,KAAI,SAAS,OAAO,GAAG;AAC9E;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,aAAa,IAAK,QAAO,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AAC7D,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAG,QAAO,IAAI,IAAI,KAAK,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,IAAI,KAAK,EAAE,GAAG,EAAE,OAAO;AAC9F,MAAI,IAAI,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI;AAC/B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,GAAG,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO;AACtF;AAEO,SAAS,IAAI,GAAGG,IAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAGA,IAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAGA,IAAG,GAAG,SAAS;AACpC,SAAO,UAAU,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,GAAGA,IAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAChG;AAEO,SAAS,IAAI,GAAGA,IAAG,GAAG,SAAS;AACpC,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAACA;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,MAAM,EAAE,CAAC,EAAG,QAAO,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,OAAO;AACnD,MAAI,IAAI,EAAE,IAAI;AACd,SAAO,IAAI,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO;AACrE;AAEA,eAAO,KAAK,KAAK,OAAO,OAAO;AAAA,EAC7B,SAASD,IAAG;AACV,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAKA,MAAK,OAAO,IAAIA,KAAI,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,OAAOA,IAAG;AACR,WAAO,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAKA,MAAK,OAAO,IAAIA,KAAI,KAAK,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM;AACJ,WAAO,QAAQ,IAAI,EAAE,IAAI;AAAA,EAC3B;AACF,CAAC,CAAC;;;ACtHF,IAAI,IAAI;AAAR,IACI,IAAI;AADR,IAEI,IAAI;AAFR,IAGI,IAAI;AAHR,IAII,IAAI;AAJR,IAKI,KAAK,IAAI;AALb,IAMI,KAAK,IAAI;AANb,IAOI,QAAQ,IAAI,IAAI,IAAI;AAExB,SAAS,iBAAiB,GAAG;AAC3B,MAAI,aAAa,UAAW,QAAO,IAAI,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO;AACzE,MAAI,EAAE,aAAa,KAAM,KAAI,WAAW,CAAC;AACzC,MAAI,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,IAAI,EAAE,IAAI,KACV,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,KAClD,KAAK,IAAI,GACTE,MAAK,KAAK,IAAI,KAAK,IAAI,MAAM,GAC7BC,KAAI,KAAK,KAAKD,KAAIA,KAAI,KAAK,EAAE,KAAK,IAAI,KAAK,IAAI,KAC/C,IAAIC,KAAI,KAAK,MAAMD,IAAG,EAAE,IAAI,UAAU,MAAM;AAChD,SAAO,IAAI,UAAU,IAAI,IAAI,IAAI,MAAM,GAAGC,IAAG,GAAG,EAAE,OAAO;AAC3D;AAEe,SAAR,UAA2B,GAAGA,IAAG,GAAG,SAAS;AAClD,SAAO,UAAU,WAAW,IAAI,iBAAiB,CAAC,IAAI,IAAI,UAAU,GAAGA,IAAG,GAAG,WAAW,OAAO,IAAI,OAAO;AAC5G;AAEO,SAAS,UAAU,GAAGA,IAAG,GAAG,SAAS;AAC1C,OAAK,IAAI,CAAC;AACV,OAAK,IAAI,CAACA;AACV,OAAK,IAAI,CAAC;AACV,OAAK,UAAU,CAAC;AAClB;AAEA,eAAO,WAAW,WAAW,OAAO,OAAO;AAAA,EACzC,SAASD,IAAG;AACV,IAAAA,KAAIA,MAAK,OAAO,WAAW,KAAK,IAAI,UAAUA,EAAC;AAC/C,WAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EAC/D;AAAA,EACA,OAAOA,IAAG;AACR,IAAAA,KAAIA,MAAK,OAAO,SAAS,KAAK,IAAI,QAAQA,EAAC;AAC3C,WAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,IAAIA,IAAG,KAAK,OAAO;AAAA,EAC/D;AAAA,EACA,MAAM;AACJ,QAAI,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,OAAO,SACzC,IAAI,CAAC,KAAK,GACVE,KAAI,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAC1CC,QAAO,KAAK,IAAI,CAAC,GACjBC,QAAO,KAAK,IAAI,CAAC;AACrB,WAAO,IAAI;AAAA,MACT,OAAO,IAAIF,MAAK,IAAIC,QAAO,IAAIC;AAAA,MAC/B,OAAO,IAAIF,MAAK,IAAIC,QAAO,IAAIC;AAAA,MAC/B,OAAO,IAAIF,MAAK,IAAIC;AAAA,MACpB,KAAK;AAAA,IACP;AAAA,EACF;AACF,CAAC,CAAC;;;AC5DK,SAAS,MAAME,KAAI,IAAI,IAAI,IAAI,IAAI;AACxC,MAAIC,MAAKD,MAAKA,KAAIE,MAAKD,MAAKD;AAC5B,WAAS,IAAI,IAAIA,MAAK,IAAIC,MAAKC,OAAM,MAC9B,IAAI,IAAID,MAAK,IAAIC,OAAM,MACvB,IAAI,IAAIF,MAAK,IAAIC,MAAK,IAAIC,OAAM,KACjCA,MAAK,MAAM;AACnB;AAEe,SAAR,cAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO,SAAS;AACxB,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,IAAK,IAAI,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,GACjE,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK,IACtC,KAAK,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK;AAC9C,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;AChBe,SAAR,oBAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO;AACf,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,GAC3C,KAAK,QAAQ,IAAI,IAAI,KAAK,CAAC,GAC3B,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,QAAQ,IAAI,KAAK,CAAC,GACvB,KAAK,QAAQ,IAAI,KAAK,CAAC;AAC3B,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;ACZA,IAAO,mBAAQ,CAAAC,OAAK,MAAMA;;;ACE1B,SAAS,OAAOC,IAAG,GAAG;AACpB,SAAO,SAAS,GAAG;AACjB,WAAOA,KAAI,IAAI;AAAA,EACjB;AACF;AAEA,SAAS,YAAYA,IAAG,GAAGC,IAAG;AAC5B,SAAOD,KAAI,KAAK,IAAIA,IAAGC,EAAC,GAAG,IAAI,KAAK,IAAI,GAAGA,EAAC,IAAID,IAAGC,KAAI,IAAIA,IAAG,SAAS,GAAG;AACxE,WAAO,KAAK,IAAID,KAAI,IAAI,GAAGC,EAAC;AAAA,EAC9B;AACF;AAEO,SAAS,IAAID,IAAG,GAAG;AACxB,MAAI,IAAI,IAAIA;AACZ,SAAO,IAAI,OAAOA,IAAG,IAAI,OAAO,IAAI,OAAO,IAAI,MAAM,KAAK,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,iBAAS,MAAMA,EAAC,IAAI,IAAIA,EAAC;AAC3G;AAEO,SAAS,MAAMC,IAAG;AACvB,UAAQA,KAAI,CAACA,QAAO,IAAI,UAAU,SAASD,IAAG,GAAG;AAC/C,WAAO,IAAIA,KAAI,YAAYA,IAAG,GAAGC,EAAC,IAAI,iBAAS,MAAMD,EAAC,IAAI,IAAIA,EAAC;AAAA,EACjE;AACF;AAEe,SAAR,QAAyBA,IAAG,GAAG;AACpC,MAAI,IAAI,IAAIA;AACZ,SAAO,IAAI,OAAOA,IAAG,CAAC,IAAI,iBAAS,MAAMA,EAAC,IAAI,IAAIA,EAAC;AACrD;;;ACvBA,IAAO,cAAS,SAAS,SAASE,IAAG;AACnC,MAAIC,SAAQ,MAAMD,EAAC;AAEnB,WAASE,KAAI,OAAO,KAAK;AACvB,QAAI,IAAID,QAAO,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC9D,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAQ,MAAM,SAAS,IAAI,OAAO;AAChD,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,EAAAC,KAAI,QAAQ;AAEZ,SAAOA;AACT,EAAG,CAAC;AAEJ,SAAS,UAAU,QAAQ;AACzB,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,OAAO,QACX,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,GAAGD;AACP,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,MAAAA,SAAQ,IAAS,OAAO,CAAC,CAAC;AAC1B,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAAA,IACpB;AACA,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,IAAAA,OAAM,UAAU;AAChB,WAAO,SAAS,GAAG;AACjB,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,aAAOA,SAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEO,IAAI,WAAW,UAAU,aAAK;AAC9B,IAAI,iBAAiB,UAAU,mBAAW;;;ACtDlC,SAAR,oBAAiBE,IAAG,GAAG;AAC5B,MAAI,CAAC,EAAG,KAAI,CAAC;AACb,MAAI,IAAIA,KAAI,KAAK,IAAI,EAAE,QAAQA,GAAE,MAAM,IAAI,GACvCC,KAAI,EAAE,MAAM,GACZ;AACJ,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,GAAE,CAAC,IAAID,GAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,IAAI;AACvD,WAAOC;AAAA,EACT;AACF;AAEO,SAAS,cAAcC,IAAG;AAC/B,SAAO,YAAY,OAAOA,EAAC,KAAK,EAAEA,cAAa;AACjD;;;ACbe,SAAR,aAAiBC,IAAG,GAAG;AAC5B,MAAI,IAAI,oBAAI;AACZ,SAAOA,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,EAAE,QAAQA,MAAK,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,EACzC;AACF;;;ACLe,SAAR,eAAiBC,IAAG,GAAG;AAC5B,SAAOA,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAOA,MAAK,IAAI,KAAK,IAAI;AAAA,EAC3B;AACF;;;ACFe,SAAR,eAAiBC,IAAG,GAAG;AAC5B,MAAI,IAAI,CAAC,GACLC,KAAI,CAAC,GACLC;AAEJ,MAAIF,OAAM,QAAQ,OAAOA,OAAM,SAAU,CAAAA,KAAI,CAAC;AAC9C,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,KAAI,CAAC;AAE9C,OAAKE,MAAK,GAAG;AACX,QAAIA,MAAKF,IAAG;AACV,QAAEE,EAAC,IAAI,cAAMF,GAAEE,EAAC,GAAG,EAAEA,EAAC,CAAC;AAAA,IACzB,OAAO;AACL,MAAAD,GAAEC,EAAC,IAAI,EAAEA,EAAC;AAAA,IACZ;AAAA,EACF;AAEA,SAAO,SAAS,GAAG;AACjB,SAAKA,MAAK,EAAG,CAAAD,GAAEC,EAAC,IAAI,EAAEA,EAAC,EAAE,CAAC;AAC1B,WAAOD;AAAA,EACT;AACF;;;ACpBA,IAAI,MAAM;AAAV,IACI,MAAM,IAAI,OAAO,IAAI,QAAQ,GAAG;AAEpC,SAASE,MAAK,GAAG;AACf,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,IAAI,GAAG;AACd,SAAO,SAAS,GAAG;AACjB,WAAO,EAAE,CAAC,IAAI;AAAA,EAChB;AACF;AAEe,SAAR,eAAiBC,IAAG,GAAG;AAC5B,MAAI,KAAK,IAAI,YAAY,IAAI,YAAY,GACrC,IACA,IACA,IACA,IAAI,IACJC,KAAI,CAAC,GACL,IAAI,CAAC;AAGT,EAAAD,KAAIA,KAAI,IAAI,IAAI,IAAI;AAGpB,UAAQ,KAAK,IAAI,KAAKA,EAAC,OACf,KAAK,IAAI,KAAK,CAAC,IAAI;AACzB,SAAK,KAAK,GAAG,SAAS,IAAI;AACxB,WAAK,EAAE,MAAM,IAAI,EAAE;AACnB,UAAIC,GAAE,CAAC,EAAG,CAAAA,GAAE,CAAC,KAAK;AAAA,UACb,CAAAA,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB;AACA,SAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI;AACjC,UAAIA,GAAE,CAAC,EAAG,CAAAA,GAAE,CAAC,KAAK;AAAA,UACb,CAAAA,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB,OAAO;AACL,MAAAA,GAAE,EAAE,CAAC,IAAI;AACT,QAAE,KAAK,EAAC,GAAM,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IAClC;AACA,SAAK,IAAI;AAAA,EACX;AAGA,MAAI,KAAK,EAAE,QAAQ;AACjB,SAAK,EAAE,MAAM,EAAE;AACf,QAAIA,GAAE,CAAC,EAAG,CAAAA,GAAE,CAAC,KAAK;AAAA,QACb,CAAAA,GAAE,EAAE,CAAC,IAAI;AAAA,EAChB;AAIA,SAAOA,GAAE,SAAS,IAAK,EAAE,CAAC,IACpB,IAAI,EAAE,CAAC,EAAE,CAAC,IACVF,MAAK,CAAC,KACL,IAAI,EAAE,QAAQ,SAAS,GAAG;AACzB,aAASG,KAAI,GAAG,GAAGA,KAAI,GAAG,EAAEA,GAAG,CAAAD,IAAG,IAAI,EAAEC,EAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACtD,WAAOD,GAAE,KAAK,EAAE;AAAA,EAClB;AACR;;;ACrDe,SAAR,cAAiBE,IAAG,GAAG;AAC5B,MAAI,IAAI,OAAO,GAAGC;AAClB,SAAO,KAAK,QAAQ,MAAM,YAAY,iBAAS,CAAC,KACzC,MAAM,WAAW,iBAClB,MAAM,YAAaA,KAAI,MAAM,CAAC,MAAM,IAAIA,IAAG,eAAO,iBAClD,aAAa,QAAQ,cACrB,aAAa,OAAO,eACpB,cAAc,CAAC,IAAI,sBACnB,MAAM,QAAQ,CAAC,IAAI,eACnB,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,aAAa,cAAc,MAAM,CAAC,IAAI,iBAClF,gBAAQD,IAAG,CAAC;AACpB;;;AClBe,SAAR,cAAiBE,IAAG,GAAG;AAC5B,UAAQ,cAAc,CAAC,IAAI,sBAAc,cAAcA,IAAG,CAAC;AAC7D;AAEO,SAAS,aAAaA,IAAG,GAAG;AACjC,MAAI,KAAK,IAAI,EAAE,SAAS,GACpB,KAAKA,KAAI,KAAK,IAAI,IAAIA,GAAE,MAAM,IAAI,GAClCC,KAAI,IAAI,MAAM,EAAE,GAChBC,KAAI,IAAI,MAAM,EAAE,GAChB;AAEJ,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,CAAAD,GAAE,CAAC,IAAI,cAAMD,GAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChD,SAAO,IAAI,IAAI,EAAE,EAAG,CAAAE,GAAE,CAAC,IAAI,EAAE,CAAC;AAE9B,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,CAAAA,GAAE,CAAC,IAAID,GAAE,CAAC,EAAE,CAAC;AACtC,WAAOC;AAAA,EACT;AACF;;;ACrBe,SAAR,iBAAiBC,QAAO;AAC7B,MAAI,IAAIA,OAAM;AACd,SAAO,SAAS,GAAG;AACjB,WAAOA,OAAM,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,EAC9D;AACF;;;ACHe,SAAR,YAAiBC,IAAG,GAAG;AAC5B,MAAI,IAAI,IAAI,CAACA,IAAG,CAAC,CAAC;AAClB,SAAO,SAAS,GAAG;AACjB,QAAIC,KAAI,EAAE,CAAC;AACX,WAAOA,KAAI,MAAM,KAAK,MAAMA,KAAI,GAAG;AAAA,EACrC;AACF;;;ACRe,SAAR,cAAiBC,IAAG,GAAG;AAC5B,SAAOA,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,MAAMA,MAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EACvC;AACF;;;ACJA,IAAIC,WAAU,MAAM,KAAK;AAElB,IAAIC,YAAW;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AAEe,SAAR,kBAAiBC,IAAG,GAAGC,IAAG,GAAG,GAAG,GAAG;AACxC,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,KAAK,KAAKD,KAAIA,KAAI,IAAI,CAAC,EAAG,CAAAA,MAAK,QAAQ,KAAK;AACzD,MAAI,QAAQA,KAAIC,KAAI,IAAI,EAAG,CAAAA,MAAKD,KAAI,OAAO,KAAK,IAAI;AACpD,MAAI,SAAS,KAAK,KAAKC,KAAIA,KAAI,IAAI,CAAC,EAAG,CAAAA,MAAK,QAAQ,KAAK,QAAQ,SAAS;AAC1E,MAAID,KAAI,IAAI,IAAIC,GAAG,CAAAD,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC;AAC7D,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ,KAAK,MAAM,GAAGA,EAAC,IAAIF;AAAA,IAC3B,OAAO,KAAK,KAAK,KAAK,IAAIA;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACF;;;ACvBA,IAAI;AAGG,SAAS,SAAS,OAAO;AAC9B,QAAM,IAAI,KAAK,OAAO,cAAc,aAAa,YAAY,iBAAiB,QAAQ,EAAE;AACxF,SAAO,EAAE,aAAaI,YAAW,kBAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzE;AAEO,SAAS,SAAS,OAAO;AAC9B,MAAI,SAAS,KAAM,QAAOA;AAC1B,MAAI,CAAC,QAAS,WAAU,SAAS,gBAAgB,8BAA8B,GAAG;AAClF,UAAQ,aAAa,aAAa,KAAK;AACvC,MAAI,EAAE,QAAQ,QAAQ,UAAU,QAAQ,YAAY,GAAI,QAAOA;AAC/D,UAAQ,MAAM;AACd,SAAO,kBAAU,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvE;;;ACdA,SAAS,qBAAqB,OAAO,SAAS,SAAS,UAAU;AAE/D,WAAS,IAAIC,IAAG;AACd,WAAOA,GAAE,SAASA,GAAE,IAAI,IAAI,MAAM;AAAA,EACpC;AAEA,WAAS,UAAU,IAAI,IAAI,IAAI,IAAIA,IAAG,GAAG;AACvC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAIA,GAAE,KAAK,cAAc,MAAM,SAAS,MAAM,OAAO;AACzD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,MAAM,IAAI;AACnB,MAAAA,GAAE,KAAK,eAAe,KAAK,UAAU,KAAK,OAAO;AAAA,IACnD;AAAA,EACF;AAEA,WAAS,OAAOC,IAAG,GAAGD,IAAG,GAAG;AAC1B,QAAIC,OAAM,GAAG;AACX,UAAIA,KAAI,IAAI,IAAK,MAAK;AAAA,eAAc,IAAIA,KAAI,IAAK,CAAAA,MAAK;AACtD,QAAE,KAAK,EAAC,GAAGD,GAAE,KAAK,IAAIA,EAAC,IAAI,WAAW,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAOC,IAAG,CAAC,EAAC,CAAC;AAAA,IAC7E,WAAW,GAAG;AACZ,MAAAD,GAAE,KAAK,IAAIA,EAAC,IAAI,YAAY,IAAI,QAAQ;AAAA,IAC1C;AAAA,EACF;AAEA,WAAS,MAAMC,IAAG,GAAGD,IAAG,GAAG;AACzB,QAAIC,OAAM,GAAG;AACX,QAAE,KAAK,EAAC,GAAGD,GAAE,KAAK,IAAIA,EAAC,IAAI,UAAU,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAOC,IAAG,CAAC,EAAC,CAAC;AAAA,IAC5E,WAAW,GAAG;AACZ,MAAAD,GAAE,KAAK,IAAIA,EAAC,IAAI,WAAW,IAAI,QAAQ;AAAA,IACzC;AAAA,EACF;AAEA,WAAS,MAAM,IAAI,IAAI,IAAI,IAAIA,IAAG,GAAG;AACnC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAIA,GAAE,KAAK,IAAIA,EAAC,IAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AACtD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,MAAAA,GAAE,KAAK,IAAIA,EAAC,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG;AAAA,IAChD;AAAA,EACF;AAEA,SAAO,SAASC,IAAG,GAAG;AACpB,QAAID,KAAI,CAAC,GACL,IAAI,CAAC;AACT,IAAAC,KAAI,MAAMA,EAAC,GAAG,IAAI,MAAM,CAAC;AACzB,cAAUA,GAAE,YAAYA,GAAE,YAAY,EAAE,YAAY,EAAE,YAAYD,IAAG,CAAC;AACtE,WAAOC,GAAE,QAAQ,EAAE,QAAQD,IAAG,CAAC;AAC/B,UAAMC,GAAE,OAAO,EAAE,OAAOD,IAAG,CAAC;AAC5B,UAAMC,GAAE,QAAQA,GAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQD,IAAG,CAAC;AAClD,IAAAC,KAAI,IAAI;AACR,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI,IAAI,EAAE,QAAQ;AAC1B,aAAO,EAAE,IAAI,EAAG,CAAAD,IAAG,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACvC,aAAOA,GAAE,KAAK,EAAE;AAAA,IAClB;AAAA,EACF;AACF;AAEO,IAAI,0BAA0B,qBAAqB,UAAU,QAAQ,OAAO,MAAM;AAClF,IAAI,0BAA0B,qBAAqB,UAAU,MAAM,KAAK,GAAG;;;AC9DlF,IAAI,WAAW;AAEf,SAAS,KAAKE,IAAG;AACf,WAASA,KAAI,KAAK,IAAIA,EAAC,KAAK,IAAIA,MAAK;AACvC;AAEA,SAAS,KAAKA,IAAG;AACf,WAASA,KAAI,KAAK,IAAIA,EAAC,KAAK,IAAIA,MAAK;AACvC;AAEA,SAAS,KAAKA,IAAG;AACf,WAASA,KAAI,KAAK,IAAI,IAAIA,EAAC,KAAK,MAAMA,KAAI;AAC5C;AAEA,IAAO,eAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAIhD,WAAS,KAAK,IAAI,IAAI;AACpB,QAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,GACA;AAGJ,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,IAAI,KAAK,EAAE,IAAI;AACxB,UAAI,SAAS,GAAG;AACd,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAGK;AACH,UAAI,KAAK,KAAK,KAAK,EAAE,GACjB,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,GACzC,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;AAC7C,WAAK,KAAK,MAAM;AAChB,UAAI,SAAS,GAAG;AACd,YAAIC,KAAI,IAAI,GACR,SAAS,KAAK,EAAE,GAChB,IAAI,MAAM,OAAO,OAAO,SAAS,KAAK,MAAMA,KAAI,EAAE,IAAI,KAAK,EAAE;AACjE,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,SAAS,KAAK,MAAMA,KAAI,EAAE;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAEA,MAAE,WAAW,IAAI,MAAO,MAAM,KAAK;AAEnC,WAAO;AAAA,EACT;AAEA,OAAK,MAAM,SAAS,GAAG;AACrB,QAAI,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACrD,WAAO,QAAQ,IAAI,IAAI,EAAE;AAAA,EAC3B;AAEA,SAAO;AACT,EAAG,KAAK,OAAO,GAAG,CAAC;;;ACnEnB,SAASC,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5DC,KAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAIA,GAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQF,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjBf,SAARG,KAAqB,OAAO,KAAK;AACtC,MAAI,IAAI,SAAO,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC9DC,KAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,SAAO,SAAS,GAAG;AACjB,UAAM,IAAI,EAAE,CAAC;AACb,UAAM,IAAIA,GAAE,CAAC;AACb,UAAM,IAAI,EAAE,CAAC;AACb,UAAM,UAAU,QAAQ,CAAC;AACzB,WAAO,QAAQ;AAAA,EACjB;AACF;;;ACZA,SAASC,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5DC,KAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAIA,GAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQF,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjB9B,SAASG,WAAUC,MAAK;AACtB,SAAQ,SAAS,eAAeC,IAAG;AACjC,IAAAA,KAAI,CAACA;AAEL,aAASF,WAAU,OAAO,KAAK;AAC7B,UAAI,IAAIC,MAAK,QAAQ,UAAe,KAAK,GAAG,IAAI,MAAM,UAAe,GAAG,GAAG,CAAC,GACxEE,KAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,aAAO,SAAS,GAAG;AACjB,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAIA,GAAE,CAAC;AACb,cAAM,IAAI,EAAE,KAAK,IAAI,GAAGD,EAAC,CAAC;AAC1B,cAAM,UAAU,QAAQ,CAAC;AACzB,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAEA,IAAAF,WAAU,QAAQ;AAElB,WAAOA;AAAA,EACT,EAAG,CAAC;AACN;AAEA,IAAO,oBAAQA,WAAU,GAAG;AACrB,IAAI,gBAAgBA,WAAU,OAAK;;;AC1B3B,SAAR,UAA2B,aAAa,QAAQ;AACrD,MAAI,WAAW,OAAW,UAAS,aAAa,cAAc;AAC9D,MAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAC5E,SAAO,IAAI,EAAG,GAAE,CAAC,IAAI,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC;AACnD,SAAO,SAAS,GAAG;AACjB,QAAII,KAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AACvD,WAAO,EAAEA,EAAC,EAAE,IAAIA,EAAC;AAAA,EACnB;AACF;;;ACVe,SAAR,iBAAiB,cAAc,GAAG;AACvC,MAAI,UAAU,IAAI,MAAM,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,SAAQ,CAAC,IAAI,aAAa,KAAK,IAAI,EAAE;AACjE,SAAO;AACT;;;ACJA,IAAM,KAAK,KAAK;AAAhB,IACI,MAAM,IAAI;AADd,IAEI,UAAU;AAFd,IAGI,aAAa,MAAM;AAEvB,SAAS,OAAO,SAAS;AACvB,OAAK,KAAK,QAAQ,CAAC;AACnB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,KAAK,UAAU,CAAC,IAAI,QAAQ,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,MAAI,IAAI,KAAK,MAAM,MAAM;AACzB,MAAI,EAAE,KAAK,GAAI,OAAM,IAAI,MAAM,mBAAmB,MAAM,EAAE;AAC1D,MAAI,IAAI,GAAI,QAAO;AACnB,QAAMC,KAAI,MAAM;AAChB,SAAO,SAAS,SAAS;AACvB,SAAK,KAAK,QAAQ,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,WAAK,KAAK,KAAK,MAAM,UAAU,CAAC,IAAIA,EAAC,IAAIA,KAAI,QAAQ,CAAC;AAAA,IACxD;AAAA,EACF;AACF;AAEO,IAAM,OAAN,MAAW;AAAA,EAChB,YAAY,QAAQ;AAClB,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AACT,SAAK,UAAU,UAAU,OAAO,SAAS,YAAY,MAAM;AAAA,EAC7D;AAAA,EACA,OAAOC,IAAGC,IAAG;AACX,SAAK,WAAW,KAAK,MAAM,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAACC,EAAC;AAAA,EACtE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAOD,IAAGC,IAAG;AACX,SAAK,WAAW,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,CAACC,EAAC;AAAA,EAChD;AAAA,EACA,iBAAiB,IAAI,IAAID,IAAGC,IAAG;AAC7B,SAAK,WAAW,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,CAACC,EAAC;AAAA,EAC9D;AAAA,EACA,cAAc,IAAI,IAAI,IAAI,IAAID,IAAGC,IAAG;AAClC,SAAK,WAAW,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,CAACC,EAAC;AAAA,EAC5E;AAAA,EACA,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG;AACvB,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAG7C,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AAElD,QAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAChD,WAGS,EAAE,QAAQ,SAAS;AAAA,aAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,WAAW,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAChD,OAGK;AACH,UAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,WAAW,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG;AAAA,MAClD;AAEA,WAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAClH;AAAA,EACF;AAAA,EACA,IAAID,IAAGC,IAAG,GAAG,IAAI,IAAI,KAAK;AACxB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAGhC,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AAElD,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAKD,KAAI,IACT,KAAKC,KAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW,EAAE,IAAI,EAAE;AAAA,IAC1B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,WAAW,EAAE,IAAI,EAAE;AAAA,IAC1B;AAGA,QAAI,CAAC,EAAG;AAGR,QAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAID,KAAI,EAAE,IAAIC,KAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAC5G,WAGS,KAAK,SAAS;AACrB,WAAK,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,KAAK,MAAMD,KAAI,IAAI,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,MAAMC,KAAI,IAAI,KAAK,IAAI,EAAE,CAAC;AAAA,IACrH;AAAA,EACF;AAAA,EACA,KAAKD,IAAGC,IAAG,GAAG,GAAG;AACf,SAAK,WAAW,KAAK,MAAM,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAACC,EAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAEO,SAAS,OAAO;AACrB,SAAO,IAAI;AACb;AAGA,KAAK,YAAY,KAAK;AAEf,SAAS,UAAU,SAAS,GAAG;AACpC,SAAO,IAAI,KAAK,CAAC,MAAM;AACzB;;;AC1JA,IAAI,KAAK;AAEM,SAAR,gBAAiC,WAAW;AACjD,MAAI,EAAE,QAAQ,GAAG,KAAK,SAAS,GAAI,OAAM,IAAI,MAAM,qBAAqB,SAAS;AACjF,MAAI;AACJ,SAAO,IAAI,gBAAgB;AAAA,IACzB,MAAM,MAAM,CAAC;AAAA,IACb,OAAO,MAAM,CAAC;AAAA,IACd,MAAM,MAAM,CAAC;AAAA,IACb,QAAQ,MAAM,CAAC;AAAA,IACf,MAAM,MAAM,CAAC;AAAA,IACb,OAAO,MAAM,CAAC;AAAA,IACd,OAAO,MAAM,CAAC;AAAA,IACd,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC;AAAA,IACvC,MAAM,MAAM,CAAC;AAAA,IACb,MAAM,MAAM,EAAE;AAAA,EAChB,CAAC;AACH;AAEA,gBAAgB,YAAY,gBAAgB;AAErC,SAAS,gBAAgB,WAAW;AACzC,OAAK,OAAO,UAAU,SAAS,SAAY,MAAM,UAAU,OAAO;AAClE,OAAK,QAAQ,UAAU,UAAU,SAAY,MAAM,UAAU,QAAQ;AACrE,OAAK,OAAO,UAAU,SAAS,SAAY,MAAM,UAAU,OAAO;AAClE,OAAK,SAAS,UAAU,WAAW,SAAY,KAAK,UAAU,SAAS;AACvE,OAAK,OAAO,CAAC,CAAC,UAAU;AACxB,OAAK,QAAQ,UAAU,UAAU,SAAY,SAAY,CAAC,UAAU;AACpE,OAAK,QAAQ,CAAC,CAAC,UAAU;AACzB,OAAK,YAAY,UAAU,cAAc,SAAY,SAAY,CAAC,UAAU;AAC5E,OAAK,OAAO,CAAC,CAAC,UAAU;AACxB,OAAK,OAAO,UAAU,SAAS,SAAY,KAAK,UAAU,OAAO;AACnE;AAEA,gBAAgB,UAAU,WAAW,WAAW;AAC9C,SAAO,KAAK,OACN,KAAK,QACL,KAAK,OACL,KAAK,UACJ,KAAK,OAAO,MAAM,OAClB,KAAK,UAAU,SAAY,KAAK,KAAK,IAAI,GAAG,KAAK,QAAQ,CAAC,MAC1D,KAAK,QAAQ,MAAM,OACnB,KAAK,cAAc,SAAY,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,YAAY,CAAC,MACxE,KAAK,OAAO,MAAM,MACnB,KAAK;AACb;;;AC9Ce,SAAR,sBAAiBC,IAAG;AACzB,SAAO,KAAK,IAAIA,KAAI,KAAK,MAAMA,EAAC,CAAC,KAAK,OAChCA,GAAE,eAAe,IAAI,EAAE,QAAQ,MAAM,EAAE,IACvCA,GAAE,SAAS,EAAE;AACrB;AAKO,SAAS,mBAAmBA,IAAG,GAAG;AACvC,OAAK,KAAKA,KAAI,IAAIA,GAAE,cAAc,IAAI,CAAC,IAAIA,GAAE,cAAc,GAAG,QAAQ,GAAG,KAAK,EAAG,QAAO;AACxF,MAAI,GAAG,cAAcA,GAAE,MAAM,GAAG,CAAC;AAIjC,SAAO;AAAA,IACL,YAAY,SAAS,IAAI,YAAY,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI;AAAA,IACjE,CAACA,GAAE,MAAM,IAAI,CAAC;AAAA,EAChB;AACF;;;ACjBe,SAAR,iBAAiBC,IAAG;AACzB,SAAOA,KAAI,mBAAmB,KAAK,IAAIA,EAAC,CAAC,GAAGA,KAAIA,GAAE,CAAC,IAAI;AACzD;;;ACJe,SAAR,oBAAiB,UAAU,WAAW;AAC3C,SAAO,SAAS,OAAO,OAAO;AAC5B,QAAI,IAAI,MAAM,QACV,IAAI,CAAC,GACL,IAAI,GACJ,IAAI,SAAS,CAAC,GACdC,UAAS;AAEb,WAAO,IAAI,KAAK,IAAI,GAAG;AACrB,UAAIA,UAAS,IAAI,IAAI,MAAO,KAAI,KAAK,IAAI,GAAG,QAAQA,OAAM;AAC1D,QAAE,KAAK,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC,CAAC;AACrC,WAAKA,WAAU,IAAI,KAAK,MAAO;AAC/B,UAAI,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM;AAAA,IAC5C;AAEA,WAAO,EAAE,QAAQ,EAAE,KAAK,SAAS;AAAA,EACnC;AACF;;;ACjBe,SAAR,uBAAiB,UAAU;AAChC,SAAO,SAAS,OAAO;AACrB,WAAO,MAAM,QAAQ,UAAU,SAAS,GAAG;AACzC,aAAO,SAAS,CAAC,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AACF;;;ACLe,SAAR,mBAAiBC,IAAG;AACzB,MAAK,UAAS,IAAIA,GAAE,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC1D,YAAQA,GAAE,CAAC,GAAG;AAAA,MACZ,KAAK;AAAK,aAAK,KAAK;AAAG;AAAA,MACvB,KAAK;AAAK,YAAI,OAAO,EAAG,MAAK;AAAG,aAAK;AAAG;AAAA,MACxC;AAAS,YAAI,CAAC,CAACA,GAAE,CAAC,EAAG,OAAM;AAAK,YAAI,KAAK,EAAG,MAAK;AAAG;AAAA,IACtD;AAAA,EACF;AACA,SAAO,KAAK,IAAIA,GAAE,MAAM,GAAG,EAAE,IAAIA,GAAE,MAAM,KAAK,CAAC,IAAIA;AACrD;;;ACRO,IAAI;AAEI,SAAR,yBAAiBC,IAAG,GAAG;AAC5B,MAAI,IAAI,mBAAmBA,IAAG,CAAC;AAC/B,MAAI,CAAC,EAAG,QAAOA,KAAI;AACnB,MAAI,cAAc,EAAE,CAAC,GACjB,WAAW,EAAE,CAAC,GACd,IAAI,YAAY,iBAAiB,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,GAC5F,IAAI,YAAY;AACpB,SAAO,MAAM,IAAI,cACX,IAAI,IAAI,cAAc,IAAI,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,GAAG,IACnD,IAAI,IAAI,YAAY,MAAM,GAAG,CAAC,IAAI,MAAM,YAAY,MAAM,CAAC,IAC3D,OAAO,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,mBAAmBA,IAAG,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;AAC3F;;;ACbe,SAAR,sBAAiBC,IAAG,GAAG;AAC5B,MAAI,IAAI,mBAAmBA,IAAG,CAAC;AAC/B,MAAI,CAAC,EAAG,QAAOA,KAAI;AACnB,MAAI,cAAc,EAAE,CAAC,GACjB,WAAW,EAAE,CAAC;AAClB,SAAO,WAAW,IAAI,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI,cACxD,YAAY,SAAS,WAAW,IAAI,YAAY,MAAM,GAAG,WAAW,CAAC,IAAI,MAAM,YAAY,MAAM,WAAW,CAAC,IAC7G,cAAc,IAAI,MAAM,WAAW,YAAY,SAAS,CAAC,EAAE,KAAK,GAAG;AAC3E;;;ACNA,IAAO,sBAAQ;AAAA,EACb,KAAK,CAACC,IAAG,OAAOA,KAAI,KAAK,QAAQ,CAAC;AAAA,EAClC,KAAK,CAACA,OAAM,KAAK,MAAMA,EAAC,EAAE,SAAS,CAAC;AAAA,EACpC,KAAK,CAACA,OAAMA,KAAI;AAAA,EAChB,KAAK;AAAA,EACL,KAAK,CAACA,IAAG,MAAMA,GAAE,cAAc,CAAC;AAAA,EAChC,KAAK,CAACA,IAAG,MAAMA,GAAE,QAAQ,CAAC;AAAA,EAC1B,KAAK,CAACA,IAAG,MAAMA,GAAE,YAAY,CAAC;AAAA,EAC9B,KAAK,CAACA,OAAM,KAAK,MAAMA,EAAC,EAAE,SAAS,CAAC;AAAA,EACpC,KAAK,CAACA,IAAG,MAAM,sBAAcA,KAAI,KAAK,CAAC;AAAA,EACvC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK,CAACA,OAAM,KAAK,MAAMA,EAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EACnD,KAAK,CAACA,OAAM,KAAK,MAAMA,EAAC,EAAE,SAAS,EAAE;AACvC;;;AClBe,SAAR,iBAAiBC,IAAG;AACzB,SAAOA;AACT;;;ACOA,IAAIC,OAAM,MAAM,UAAU;AAA1B,IACI,WAAW,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG;AAEnE,SAAR,eAAiBC,SAAQ;AAC9B,MAAIC,SAAQD,QAAO,aAAa,UAAaA,QAAO,cAAc,SAAY,mBAAW,oBAAYD,KAAI,KAAKC,QAAO,UAAU,MAAM,GAAGA,QAAO,YAAY,EAAE,GACzJ,iBAAiBA,QAAO,aAAa,SAAY,KAAKA,QAAO,SAAS,CAAC,IAAI,IAC3E,iBAAiBA,QAAO,aAAa,SAAY,KAAKA,QAAO,SAAS,CAAC,IAAI,IAC3E,UAAUA,QAAO,YAAY,SAAY,MAAMA,QAAO,UAAU,IAChE,WAAWA,QAAO,aAAa,SAAY,mBAAW,uBAAeD,KAAI,KAAKC,QAAO,UAAU,MAAM,CAAC,GACtG,UAAUA,QAAO,YAAY,SAAY,MAAMA,QAAO,UAAU,IAChE,QAAQA,QAAO,UAAU,SAAY,MAAMA,QAAO,QAAQ,IAC1D,MAAMA,QAAO,QAAQ,SAAY,QAAQA,QAAO,MAAM;AAE1D,WAAS,UAAU,WAAW;AAC5B,gBAAY,gBAAgB,SAAS;AAErC,QAAI,OAAO,UAAU,MACjB,QAAQ,UAAU,OAClBE,QAAO,UAAU,MACjB,SAAS,UAAU,QACnBC,QAAO,UAAU,MACjB,QAAQ,UAAU,OAClB,QAAQ,UAAU,OAClB,YAAY,UAAU,WACtB,OAAO,UAAU,MACjB,OAAO,UAAU;AAGrB,QAAI,SAAS,IAAK,SAAQ,MAAM,OAAO;AAAA,aAG9B,CAAC,oBAAY,IAAI,EAAG,eAAc,WAAc,YAAY,KAAK,OAAO,MAAM,OAAO;AAG9F,QAAIA,SAAS,SAAS,OAAO,UAAU,IAAM,CAAAA,QAAO,MAAM,OAAO,KAAK,QAAQ;AAI9E,QAAI,SAAS,WAAW,MAAM,iBAAiB,WAAW,OAAO,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,IAC9G,SAAS,WAAW,MAAM,iBAAiB,OAAO,KAAK,IAAI,IAAI,UAAU;AAK7E,QAAI,aAAa,oBAAY,IAAI,GAC7B,cAAc,aAAa,KAAK,IAAI;AAMxC,gBAAY,cAAc,SAAY,IAChC,SAAS,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC,IACzD,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC;AAEzC,aAASC,QAAO,OAAO;AACrB,UAAI,cAAc,QACd,cAAc,QACd,GAAG,GAAGC;AAEV,UAAI,SAAS,KAAK;AAChB,sBAAc,WAAW,KAAK,IAAI;AAClC,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ,CAAC;AAGT,YAAI,gBAAgB,QAAQ,KAAK,IAAI,QAAQ;AAG7C,gBAAQ,MAAM,KAAK,IAAI,MAAM,WAAW,KAAK,IAAI,KAAK,GAAG,SAAS;AAGlE,YAAI,KAAM,SAAQ,mBAAW,KAAK;AAGlC,YAAI,iBAAiB,CAAC,UAAU,KAAKH,UAAS,IAAK,iBAAgB;AAGnE,uBAAe,gBAAiBA,UAAS,MAAMA,QAAO,QAASA,UAAS,OAAOA,UAAS,MAAM,KAAKA,SAAQ;AAC3G,uBAAe,SAAS,MAAM,SAAS,IAAI,iBAAiB,CAAC,IAAI,MAAM,eAAe,iBAAiBA,UAAS,MAAM,MAAM;AAI5H,YAAI,aAAa;AACf,cAAI,IAAI,IAAI,MAAM;AAClB,iBAAO,EAAE,IAAI,GAAG;AACd,gBAAIG,KAAI,MAAM,WAAW,CAAC,GAAG,KAAKA,MAAKA,KAAI,IAAI;AAC7C,6BAAeA,OAAM,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,IAAI,MAAM,MAAM,CAAC,KAAK;AAC3E,sBAAQ,MAAM,MAAM,GAAG,CAAC;AACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,SAAS,CAACF,MAAM,SAAQF,OAAM,OAAO,QAAQ;AAGjD,UAAIK,UAAS,YAAY,SAAS,MAAM,SAAS,YAAY,QACzD,UAAUA,UAAS,QAAQ,IAAI,MAAM,QAAQA,UAAS,CAAC,EAAE,KAAK,IAAI,IAAI;AAG1E,UAAI,SAASH,MAAM,SAAQF,OAAM,UAAU,OAAO,QAAQ,SAAS,QAAQ,YAAY,SAAS,QAAQ,GAAG,UAAU;AAGrH,cAAQ,OAAO;AAAA,QACb,KAAK;AAAK,kBAAQ,cAAc,QAAQ,cAAc;AAAS;AAAA,QAC/D,KAAK;AAAK,kBAAQ,cAAc,UAAU,QAAQ;AAAa;AAAA,QAC/D,KAAK;AAAK,kBAAQ,QAAQ,MAAM,GAAGK,UAAS,QAAQ,UAAU,CAAC,IAAI,cAAc,QAAQ,cAAc,QAAQ,MAAMA,OAAM;AAAG;AAAA,QAC9H;AAAS,kBAAQ,UAAU,cAAc,QAAQ;AAAa;AAAA,MAChE;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,IAAAF,QAAO,WAAW,WAAW;AAC3B,aAAO,YAAY;AAAA,IACrB;AAEA,WAAOA;AAAA,EACT;AAEA,WAASG,cAAa,WAAW,OAAO;AACtC,QAAI,IAAI,WAAW,YAAY,gBAAgB,SAAS,GAAG,UAAU,OAAO,KAAK,UAAU,GACvF,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,iBAAS,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,GACjEC,KAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GACnB,SAAS,SAAS,IAAI,IAAI,CAAC;AAC/B,WAAO,SAASC,QAAO;AACrB,aAAO,EAAED,KAAIC,MAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,cAAcF;AAAA,EAChB;AACF;;;ACjJA,IAAI;AACG,IAAI;AACJ,IAAI;AAEX,cAAc;AAAA,EACZ,WAAW;AAAA,EACX,UAAU,CAAC,CAAC;AAAA,EACZ,UAAU,CAAC,KAAK,EAAE;AACpB,CAAC;AAEc,SAAR,cAA+B,YAAY;AAChD,WAAS,eAAa,UAAU;AAChC,WAAS,OAAO;AAChB,iBAAe,OAAO;AACtB,SAAO;AACT;;;ACfe,SAAR,uBAAiB,MAAM;AAC5B,SAAO,KAAK,IAAI,GAAG,CAAC,iBAAS,KAAK,IAAI,IAAI,CAAC,CAAC;AAC9C;;;ACFe,SAAR,wBAAiB,MAAM,OAAO;AACnC,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,iBAAS,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,iBAAS,KAAK,IAAI,IAAI,CAAC,CAAC;AAC9G;;;ACFe,SAAR,uBAAiB,MAAMG,MAAK;AACjC,SAAO,KAAK,IAAI,IAAI,GAAGA,OAAM,KAAK,IAAIA,IAAG,IAAI;AAC7C,SAAO,KAAK,IAAI,GAAG,iBAASA,IAAG,IAAI,iBAAS,IAAI,CAAC,IAAI;AACvD;;;ACLO,SAAS,UAAU,QAAQC,QAAO;AACvC,UAAQ,UAAU,QAAQ;AAAA,IACxB,KAAK;AAAG;AAAA,IACR,KAAK;AAAG,WAAK,MAAM,MAAM;AAAG;AAAA,IAC5B;AAAS,WAAK,MAAMA,MAAK,EAAE,OAAO,MAAM;AAAG;AAAA,EAC7C;AACA,SAAO;AACT;AAEO,SAAS,iBAAiB,QAAQ,cAAc;AACrD,UAAQ,UAAU,QAAQ;AAAA,IACxB,KAAK;AAAG;AAAA,IACR,KAAK,GAAG;AACN,UAAI,OAAO,WAAW,WAAY,MAAK,aAAa,MAAM;AAAA,UACrD,MAAK,MAAM,MAAM;AACtB;AAAA,IACF;AAAA,IACA,SAAS;AACP,WAAK,OAAO,MAAM;AAClB,UAAI,OAAO,iBAAiB,WAAY,MAAK,aAAa,YAAY;AAAA,UACjE,MAAK,MAAM,YAAY;AAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACtBO,IAAM,WAAW,OAAO,UAAU;AAE1B,SAAR,UAA2B;AAChC,MAAIC,SAAQ,IAAI,UAAU,GACtB,SAAS,CAAC,GACVC,SAAQ,CAAC,GACT,UAAU;AAEd,WAAS,MAAM,GAAG;AAChB,QAAI,IAAID,OAAM,IAAI,CAAC;AACnB,QAAI,MAAM,QAAW;AACnB,UAAI,YAAY,SAAU,QAAO;AACjC,MAAAA,OAAM,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC;AAAA,IACrC;AACA,WAAOC,OAAM,IAAIA,OAAM,MAAM;AAAA,EAC/B;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO,OAAO,MAAM;AAC3C,aAAS,CAAC,GAAGD,SAAQ,IAAI,UAAU;AACnC,eAAW,SAAS,GAAG;AACrB,UAAIA,OAAM,IAAI,KAAK,EAAG;AACtB,MAAAA,OAAM,IAAI,OAAO,OAAO,KAAK,KAAK,IAAI,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUC,SAAQ,MAAM,KAAK,CAAC,GAAG,SAASA,OAAM,MAAM;AAAA,EACzE;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,QAAQ,QAAQA,MAAK,EAAE,QAAQ,OAAO;AAAA,EAC/C;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO;AACT;;;ACzCe,SAAR,OAAwB;AAC7B,MAAI,QAAQ,QAAQ,EAAE,QAAQ,MAAS,GACnC,SAAS,MAAM,QACf,eAAe,MAAM,OACrB,KAAK,GACL,KAAK,GACL,MACA,WACA,QAAQ,OACR,eAAe,GACf,eAAe,GACf,QAAQ;AAEZ,SAAO,MAAM;AAEb,WAAS,UAAU;AACjB,QAAI,IAAI,OAAO,EAAE,QACbC,WAAU,KAAK,IACf,QAAQA,WAAU,KAAK,IACvB,OAAOA,WAAU,KAAK;AAC1B,YAAQ,OAAO,SAAS,KAAK,IAAI,GAAG,IAAI,eAAe,eAAe,CAAC;AACvE,QAAI,MAAO,QAAO,KAAK,MAAM,IAAI;AACjC,cAAU,OAAO,QAAQ,QAAQ,IAAI,iBAAiB;AACtD,gBAAY,QAAQ,IAAI;AACxB,QAAI,MAAO,SAAQ,KAAK,MAAM,KAAK,GAAG,YAAY,KAAK,MAAM,SAAS;AACtE,QAAI,SAAS,MAAS,CAAC,EAAE,IAAI,SAAS,GAAG;AAAE,aAAO,QAAQ,OAAO;AAAA,IAAG,CAAC;AACrE,WAAO,aAAaA,WAAU,OAAO,QAAQ,IAAI,MAAM;AAAA,EACzD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK,OAAO;AAAA,EAC5D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE;AAAA,EACnF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,MAAM,QAAQ;AAAA,EACjE;AAEA,QAAM,YAAY,WAAW;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACvD;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,eAAe,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzF;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzE;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,CAAC,GAAG,QAAQ,KAAK;AAAA,EAC7D;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,KAAK;AAAA,EAC/E;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,EACzB,MAAM,KAAK,EACX,aAAa,YAAY,EACzB,aAAa,YAAY,EACzB,MAAM,KAAK;AAAA,EAClB;AAEA,SAAO,UAAU,MAAM,QAAQ,GAAG,SAAS;AAC7C;AAEA,SAAS,SAAS,OAAO;AACvB,MAAIC,QAAO,MAAM;AAEjB,QAAM,UAAU,MAAM;AACtB,SAAO,MAAM;AACb,SAAO,MAAM;AAEb,QAAM,OAAO,WAAW;AACtB,WAAO,SAASA,MAAK,CAAC;AAAA,EACxB;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ;AACtB,SAAO,SAAS,KAAK,MAAM,MAAM,SAAS,EAAE,aAAa,CAAC,CAAC;AAC7D;;;ACjGe,SAAR,WAA4B,OAAO,MAAMC,QAAO,WAAW;AAChE,MAAI,OAAO,SAAS,OAAO,MAAMA,MAAK,GAClC;AACJ,cAAY,gBAAgB,aAAa,OAAO,OAAO,SAAS;AAChE,UAAQ,UAAU,MAAM;AAAA,IACtB,KAAK,KAAK;AACR,UAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AACpD,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,wBAAgB,MAAM,KAAK,CAAC,EAAG,WAAU,YAAY;AAC3G,aAAO,aAAa,WAAW,KAAK;AAAA,IACtC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,KAAK;AACR,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,uBAAe,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,EAAG,WAAU,YAAY,aAAa,UAAU,SAAS;AAC9K;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,KAAK;AACR,UAAI,UAAU,aAAa,QAAQ,CAAC,MAAM,YAAY,uBAAe,IAAI,CAAC,EAAG,WAAU,YAAY,aAAa,UAAU,SAAS,OAAO;AAC1I;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,SAAS;AACzB;;;AC5Be,SAAR,UAA2BC,IAAG;AACnC,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;ACJe,SAARC,QAAwBC,IAAG;AAChC,SAAO,CAACA;AACV;;;ACGA,IAAI,OAAO,CAAC,GAAG,CAAC;AAET,SAASC,UAASC,IAAG;AAC1B,SAAOA;AACT;AAEA,SAAS,UAAUC,IAAG,GAAG;AACvB,UAAQ,KAAMA,KAAI,CAACA,MACb,SAASD,IAAG;AAAE,YAAQA,KAAIC,MAAK;AAAA,EAAG,IAClC,UAAS,MAAM,CAAC,IAAI,MAAM,GAAG;AACrC;AAEA,SAAS,QAAQA,IAAG,GAAG;AACrB,MAAI;AACJ,MAAIA,KAAI,EAAG,KAAIA,IAAGA,KAAI,GAAG,IAAI;AAC7B,SAAO,SAASD,IAAG;AAAE,WAAO,KAAK,IAAIC,IAAG,KAAK,IAAI,GAAGD,EAAC,CAAC;AAAA,EAAG;AAC3D;AAIA,SAAS,MAAM,QAAQE,QAAO,aAAa;AACzC,MAAI,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,KAAKA,OAAM,CAAC,GAAG,KAAKA,OAAM,CAAC;AAC/D,MAAI,KAAK,GAAI,MAAK,UAAU,IAAI,EAAE,GAAG,KAAK,YAAY,IAAI,EAAE;AAAA,MACvD,MAAK,UAAU,IAAI,EAAE,GAAG,KAAK,YAAY,IAAI,EAAE;AACpD,SAAO,SAASF,IAAG;AAAE,WAAO,GAAG,GAAGA,EAAC,CAAC;AAAA,EAAG;AACzC;AAEA,SAAS,QAAQ,QAAQE,QAAO,aAAa;AAC3C,MAAI,IAAI,KAAK,IAAI,OAAO,QAAQA,OAAM,MAAM,IAAI,GAC5C,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI;AAGR,MAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACzB,aAAS,OAAO,MAAM,EAAE,QAAQ;AAChC,IAAAA,SAAQA,OAAM,MAAM,EAAE,QAAQ;AAAA,EAChC;AAEA,SAAO,EAAE,IAAI,GAAG;AACd,MAAE,CAAC,IAAI,UAAU,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACzC,MAAE,CAAC,IAAI,YAAYA,OAAM,CAAC,GAAGA,OAAM,IAAI,CAAC,CAAC;AAAA,EAC3C;AAEA,SAAO,SAASF,IAAG;AACjB,QAAIG,KAAI,eAAO,QAAQH,IAAG,GAAG,CAAC,IAAI;AAClC,WAAO,EAAEG,EAAC,EAAE,EAAEA,EAAC,EAAEH,EAAC,CAAC;AAAA,EACrB;AACF;AAEO,SAAS,KAAK,QAAQ,QAAQ;AACnC,SAAO,OACF,OAAO,OAAO,OAAO,CAAC,EACtB,MAAM,OAAO,MAAM,CAAC,EACpB,YAAY,OAAO,YAAY,CAAC,EAChC,MAAM,OAAO,MAAM,CAAC,EACpB,QAAQ,OAAO,QAAQ,CAAC;AAC/B;AAEO,SAAS,cAAc;AAC5B,MAAI,SAAS,MACTE,SAAQ,MACR,cAAc,eACd,WACA,aACA,SACA,QAAQH,WACRK,YACA,QACA;AAEJ,WAAS,UAAU;AACjB,QAAI,IAAI,KAAK,IAAI,OAAO,QAAQF,OAAM,MAAM;AAC5C,QAAI,UAAUH,UAAU,SAAQ,QAAQ,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAChE,IAAAK,aAAY,IAAI,IAAI,UAAU;AAC9B,aAAS,QAAQ;AACjB,WAAO;AAAA,EACT;AAEA,WAAS,MAAMJ,IAAG;AAChB,WAAOA,MAAK,QAAQ,MAAMA,KAAI,CAACA,EAAC,IAAI,WAAW,WAAW,SAASI,WAAU,OAAO,IAAI,SAAS,GAAGF,QAAO,WAAW,IAAI,UAAU,MAAMF,EAAC,CAAC,CAAC;AAAA,EAC/I;AAEA,QAAM,SAAS,SAASK,IAAG;AACzB,WAAO,MAAM,aAAa,UAAU,QAAQD,WAAUF,QAAO,OAAO,IAAI,SAAS,GAAG,cAAiB,IAAIG,EAAC,CAAC,CAAC;AAAA,EAC9G;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,GAAGC,OAAM,GAAG,QAAQ,KAAK,OAAO,MAAM;AAAA,EACvF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUJ,SAAQ,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAKA,OAAM,MAAM;AAAA,EAC7E;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAOA,SAAQ,MAAM,KAAK,CAAC,GAAG,cAAc,eAAkB,QAAQ;AAAA,EACxE;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,IAAI,OAAOH,WAAU,QAAQ,KAAK,UAAUA;AAAA,EACjF;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,GAAG,QAAQ,KAAK;AAAA,EAC3D;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG,GAAG;AACpB,gBAAY,GAAG,cAAc;AAC7B,WAAO,QAAQ;AAAA,EACjB;AACF;AAEe,SAAR,aAA8B;AACnC,SAAO,YAAY,EAAEA,WAAUA,SAAQ;AACzC;;;ACvHO,SAAS,UAAU,OAAO;AAC/B,MAAI,SAAS,MAAM;AAEnB,QAAM,QAAQ,SAASQ,QAAO;AAC5B,QAAI,IAAI,OAAO;AACf,WAAO,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAGA,UAAS,OAAO,KAAKA,MAAK;AAAA,EAChE;AAEA,QAAM,aAAa,SAASA,QAAO,WAAW;AAC5C,QAAI,IAAI,OAAO;AACf,WAAO,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAGA,UAAS,OAAO,KAAKA,QAAO,SAAS;AAAA,EAChF;AAEA,QAAM,OAAO,SAASA,QAAO;AAC3B,QAAIA,UAAS,KAAM,CAAAA,SAAQ;AAE3B,QAAI,IAAI,OAAO;AACf,QAAI,KAAK;AACT,QAAI,KAAK,EAAE,SAAS;AACpB,QAAI,QAAQ,EAAE,EAAE;AAChB,QAAI,OAAO,EAAE,EAAE;AACf,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU;AAEd,QAAI,OAAO,OAAO;AAChB,aAAO,OAAO,QAAQ,MAAM,OAAO;AACnC,aAAO,IAAI,KAAK,IAAI,KAAK;AAAA,IAC3B;AAEA,WAAO,YAAY,GAAG;AACpB,aAAO,cAAc,OAAO,MAAMA,MAAK;AACvC,UAAI,SAAS,SAAS;AACpB,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,eAAO,OAAO,CAAC;AAAA,MACjB,WAAW,OAAO,GAAG;AACnB,gBAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI;AACnC,eAAO,KAAK,KAAK,OAAO,IAAI,IAAI;AAAA,MAClC,WAAW,OAAO,GAAG;AACnB,gBAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AAClC,eAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA,gBAAU;AAAA,IACZ;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEe,SAARC,UAA0B;AAC/B,MAAI,QAAQ,WAAW;AAEvB,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAOA,QAAO,CAAC;AAAA,EAC7B;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO,UAAU,KAAK;AACxB;;;AClEe,SAARC,UAA0B,QAAQ;AACvC,MAAI;AAEJ,WAAS,MAAMC,IAAG;AAChB,WAAOA,MAAK,QAAQ,MAAMA,KAAI,CAACA,EAAC,IAAI,UAAUA;AAAA,EAChD;AAEA,QAAM,SAAS;AAEf,QAAM,SAAS,MAAM,QAAQ,SAAS,GAAG;AACvC,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,GAAGC,OAAM,GAAG,SAAS,OAAO,MAAM;AAAA,EACnF;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAOF,UAAS,MAAM,EAAE,QAAQ,OAAO;AAAA,EACzC;AAEA,WAAS,UAAU,SAAS,MAAM,KAAK,QAAQE,OAAM,IAAI,CAAC,GAAG,CAAC;AAE9D,SAAO,UAAU,KAAK;AACxB;;;AC3Be,SAARC,MAAsB,QAAQ,UAAU;AAC7C,WAAS,OAAO,MAAM;AAEtB,MAAI,KAAK,GACL,KAAK,OAAO,SAAS,GACrB,KAAK,OAAO,EAAE,GACd,KAAK,OAAO,EAAE,GACd;AAEJ,MAAI,KAAK,IAAI;AACX,QAAI,IAAI,KAAK,IAAI,KAAK;AACtB,QAAI,IAAI,KAAK,IAAI,KAAK;AAAA,EACxB;AAEA,SAAO,EAAE,IAAI,SAAS,MAAM,EAAE;AAC9B,SAAO,EAAE,IAAI,SAAS,KAAK,EAAE;AAC7B,SAAO;AACT;;;ACXA,SAAS,aAAaC,IAAG;AACvB,SAAO,KAAK,IAAIA,EAAC;AACnB;AAEA,SAAS,aAAaA,IAAG;AACvB,SAAO,KAAK,IAAIA,EAAC;AACnB;AAEA,SAAS,cAAcA,IAAG;AACxB,SAAO,CAAC,KAAK,IAAI,CAACA,EAAC;AACrB;AAEA,SAAS,cAAcA,IAAG;AACxB,SAAO,CAAC,KAAK,IAAI,CAACA,EAAC;AACrB;AAEA,SAAS,MAAMA,IAAG;AAChB,SAAO,SAASA,EAAC,IAAI,EAAE,OAAOA,MAAKA,KAAI,IAAI,IAAIA;AACjD;AAEA,SAAS,KAAK,MAAM;AAClB,SAAO,SAAS,KAAK,QACf,SAAS,KAAK,IAAI,KAAK,MACvB,CAAAA,OAAK,KAAK,IAAI,MAAMA,EAAC;AAC7B;AAEA,SAAS,KAAK,MAAM;AAClB,SAAO,SAAS,KAAK,IAAI,KAAK,MACxB,SAAS,MAAM,KAAK,SACnB,SAAS,KAAK,KAAK,SAClB,OAAO,KAAK,IAAI,IAAI,GAAG,CAAAA,OAAK,KAAK,IAAIA,EAAC,IAAI;AACpD;AAEA,SAAS,QAAQ,GAAG;AAClB,SAAO,CAACA,IAAGC,OAAM,CAAC,EAAE,CAACD,IAAGC,EAAC;AAC3B;AAEO,SAAS,QAAQ,WAAW;AACjC,QAAM,QAAQ,UAAU,cAAc,YAAY;AAClD,QAAM,SAAS,MAAM;AACrB,MAAI,OAAO;AACX,MAAI;AACJ,MAAI;AAEJ,WAAS,UAAU;AACjB,WAAO,KAAK,IAAI,GAAG,OAAO,KAAK,IAAI;AACnC,QAAI,OAAO,EAAE,CAAC,IAAI,GAAG;AACnB,aAAO,QAAQ,IAAI,GAAG,OAAO,QAAQ,IAAI;AACzC,gBAAU,eAAe,aAAa;AAAA,IACxC,OAAO;AACL,gBAAU,cAAc,YAAY;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK;AAAA,EACrD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,QAAQ,KAAK,OAAO;AAAA,EAC5D;AAEA,QAAM,QAAQ,CAAAC,WAAS;AACrB,UAAM,IAAI,OAAO;AACjB,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,EAAE,SAAS,CAAC;AACtB,UAAM,IAAI,IAAI;AAEd,QAAI,EAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;AAEtB,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,KAAK,CAAC;AACd,QAAID;AACJ,QAAI;AACJ,UAAM,IAAIC,UAAS,OAAO,KAAK,CAACA;AAChC,QAAI,IAAI,CAAC;AAET,QAAI,EAAE,OAAO,MAAM,IAAI,IAAI,GAAG;AAC5B,UAAI,KAAK,MAAM,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC;AAClC,UAAI,IAAI,EAAG,QAAO,KAAK,GAAG,EAAE,GAAG;AAC7B,aAAKD,KAAI,GAAGA,KAAI,MAAM,EAAEA,IAAG;AACzB,cAAI,IAAI,IAAIA,KAAI,KAAK,CAAC,CAAC,IAAIA,KAAI,KAAK,CAAC;AACrC,cAAI,IAAI,EAAG;AACX,cAAI,IAAI,EAAG;AACX,YAAE,KAAK,CAAC;AAAA,QACV;AAAA,MACF;AAAA,UAAO,QAAO,KAAK,GAAG,EAAE,GAAG;AACzB,aAAKA,KAAI,OAAO,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC9B,cAAI,IAAI,IAAIA,KAAI,KAAK,CAAC,CAAC,IAAIA,KAAI,KAAK,CAAC;AACrC,cAAI,IAAI,EAAG;AACX,cAAI,IAAI,EAAG;AACX,YAAE,KAAK,CAAC;AAAA,QACV;AAAA,MACF;AACA,UAAI,EAAE,SAAS,IAAI,EAAG,KAAI,MAAM,GAAG,GAAG,CAAC;AAAA,IACzC,OAAO;AACL,UAAI,MAAM,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI;AAAA,IAC9C;AACA,WAAO,IAAI,EAAE,QAAQ,IAAI;AAAA,EAC3B;AAEA,QAAM,aAAa,CAACC,QAAO,cAAc;AACvC,QAAIA,UAAS,KAAM,CAAAA,SAAQ;AAC3B,QAAI,aAAa,KAAM,aAAY,SAAS,KAAK,MAAM;AACvD,QAAI,OAAO,cAAc,YAAY;AACnC,UAAI,EAAE,OAAO,OAAO,YAAY,gBAAgB,SAAS,GAAG,aAAa,KAAM,WAAU,OAAO;AAChG,kBAAY,OAAO,SAAS;AAAA,IAC9B;AACA,QAAIA,WAAU,SAAU,QAAO;AAC/B,UAAMD,KAAI,KAAK,IAAI,GAAG,OAAOC,SAAQ,MAAM,MAAM,EAAE,MAAM;AACzD,WAAO,OAAK;AACV,UAAI,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AACpC,UAAI,IAAI,OAAO,OAAO,IAAK,MAAK;AAChC,aAAO,KAAKD,KAAI,UAAU,CAAC,IAAI;AAAA,IACjC;AAAA,EACF;AAEA,QAAM,OAAO,MAAM;AACjB,WAAO,OAAOE,MAAK,OAAO,GAAG;AAAA,MAC3B,OAAO,CAAAH,OAAK,KAAK,KAAK,MAAM,KAAKA,EAAC,CAAC,CAAC;AAAA,MACpC,MAAM,CAAAA,OAAK,KAAK,KAAK,KAAK,KAAKA,EAAC,CAAC,CAAC;AAAA,IACpC,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO;AACT;AAEe,SAAR,MAAuB;AAC5B,QAAM,QAAQ,QAAQ,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AACnD,QAAM,OAAO,MAAM,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AACvD,YAAU,MAAM,OAAO,SAAS;AAChC,SAAO;AACT;;;ACvIA,SAAS,gBAAgBI,IAAG;AAC1B,SAAO,SAASC,IAAG;AACjB,WAAO,KAAK,KAAKA,EAAC,IAAI,KAAK,MAAM,KAAK,IAAIA,KAAID,EAAC,CAAC;AAAA,EAClD;AACF;AAEA,SAAS,gBAAgBA,IAAG;AAC1B,SAAO,SAASC,IAAG;AACjB,WAAO,KAAK,KAAKA,EAAC,IAAI,KAAK,MAAM,KAAK,IAAIA,EAAC,CAAC,IAAID;AAAA,EAClD;AACF;AAEO,SAAS,UAAU,WAAW;AACnC,MAAIA,KAAI,GAAG,QAAQ,UAAU,gBAAgBA,EAAC,GAAG,gBAAgBA,EAAC,CAAC;AAEnE,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,SAAS,UAAU,gBAAgBA,KAAI,CAAC,CAAC,GAAG,gBAAgBA,EAAC,CAAC,IAAIA;AAAA,EACrF;AAEA,SAAO,UAAU,KAAK;AACxB;AAEe,SAAR,SAA0B;AAC/B,MAAI,QAAQ,UAAU,YAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,OAAO,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACxD;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;AC9BA,SAAS,aAAa,UAAU;AAC9B,SAAO,SAASE,IAAG;AACjB,WAAOA,KAAI,IAAI,CAAC,KAAK,IAAI,CAACA,IAAG,QAAQ,IAAI,KAAK,IAAIA,IAAG,QAAQ;AAAA,EAC/D;AACF;AAEA,SAAS,cAAcA,IAAG;AACxB,SAAOA,KAAI,IAAI,CAAC,KAAK,KAAK,CAACA,EAAC,IAAI,KAAK,KAAKA,EAAC;AAC7C;AAEA,SAAS,gBAAgBA,IAAG;AAC1B,SAAOA,KAAI,IAAI,CAACA,KAAIA,KAAIA,KAAIA;AAC9B;AAEO,SAAS,OAAO,WAAW;AAChC,MAAI,QAAQ,UAAUC,WAAUA,SAAQ,GACpC,WAAW;AAEf,WAAS,UAAU;AACjB,WAAO,aAAa,IAAI,UAAUA,WAAUA,SAAQ,IAC9C,aAAa,MAAM,UAAU,eAAe,eAAe,IAC3D,UAAU,aAAa,QAAQ,GAAG,aAAa,IAAI,QAAQ,CAAC;AAAA,EACpE;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,QAAQ,KAAK;AAAA,EACzD;AAEA,SAAO,UAAU,KAAK;AACxB;AAEe,SAAR,MAAuB;AAC5B,MAAI,QAAQ,OAAO,YAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,IAAI,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACrD;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO;AACT;AAEO,SAAS,OAAO;AACrB,SAAO,IAAI,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AAChD;;;AC5CA,SAAS,OAAOC,IAAG;AACjB,SAAO,KAAK,KAAKA,EAAC,IAAIA,KAAIA;AAC5B;AAEA,SAAS,SAASA,IAAG;AACnB,SAAO,KAAK,KAAKA,EAAC,IAAI,KAAK,KAAK,KAAK,IAAIA,EAAC,CAAC;AAC7C;AAEe,SAAR,SAA0B;AAC/B,MAAI,UAAU,WAAW,GACrBC,SAAQ,CAAC,GAAG,CAAC,GACb,QAAQ,OACR;AAEJ,WAAS,MAAMD,IAAG;AAChB,QAAIE,KAAI,SAAS,QAAQF,EAAC,CAAC;AAC3B,WAAO,MAAME,EAAC,IAAI,UAAU,QAAQ,KAAK,MAAMA,EAAC,IAAIA;AAAA,EACtD;AAEA,QAAM,SAAS,SAASA,IAAG;AACzB,WAAO,QAAQ,OAAO,OAAOA,EAAC,CAAC;AAAA,EACjC;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,QAAQ,OAAO,CAAC,GAAG,SAAS,QAAQ,OAAO;AAAA,EACxE;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,OAAOD,SAAQ,MAAM,KAAK,GAAGE,OAAM,GAAG,IAAI,MAAM,CAAC,GAAG,SAASF,OAAM,MAAM;AAAA,EAC9G;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,MAAM,MAAM,CAAC,EAAE,MAAM,IAAI;AAAA,EAClC;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,MAAM,CAAC,GAAG,SAAS,QAAQ,MAAM;AAAA,EACtE;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,OAAO,QAAQ,OAAO,GAAGA,MAAK,EAChC,MAAM,KAAK,EACX,MAAM,QAAQ,MAAM,CAAC,EACrB,QAAQ,OAAO;AAAA,EACtB;AAEA,YAAU,MAAM,OAAO,SAAS;AAEhC,SAAO,UAAU,KAAK;AACxB;;;AC3De,SAARG,YAA4B;AACjC,MAAI,SAAS,CAAC,GACVC,SAAQ,CAAC,GACT,aAAa,CAAC,GACd;AAEJ,WAAS,UAAU;AACjB,QAAI,IAAI,GAAG,IAAI,KAAK,IAAI,GAAGA,OAAM,MAAM;AACvC,iBAAa,IAAI,MAAM,IAAI,CAAC;AAC5B,WAAO,EAAE,IAAI,EAAG,YAAW,IAAI,CAAC,IAAI,eAAU,QAAQ,IAAI,CAAC;AAC3D,WAAO;AAAA,EACT;AAEA,WAAS,MAAMC,IAAG;AAChB,WAAOA,MAAK,QAAQ,MAAMA,KAAI,CAACA,EAAC,IAAI,UAAUD,OAAM,eAAO,YAAYC,EAAC,CAAC;AAAA,EAC3E;AAEA,QAAM,eAAe,SAASC,IAAG;AAC/B,QAAI,IAAIF,OAAM,QAAQE,EAAC;AACvB,WAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;AAAA,MAC1B,IAAI,IAAI,WAAW,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACpC,IAAI,WAAW,SAAS,WAAW,CAAC,IAAI,OAAO,OAAO,SAAS,CAAC;AAAA,IAClE;AAAA,EACF;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO,OAAO,MAAM;AAC3C,aAAS,CAAC;AACV,aAAS,KAAK,EAAG,KAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,EAAG,QAAO,KAAK,CAAC;AAC/D,WAAO,KAAK,SAAS;AACrB,WAAO,QAAQ;AAAA,EACjB;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUF,SAAQ,MAAM,KAAK,CAAC,GAAG,QAAQ,KAAKA,OAAM,MAAM;AAAA,EAC7E;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,YAAY,WAAW;AAC3B,WAAO,WAAW,MAAM;AAAA,EAC1B;AAEA,QAAM,OAAO,WAAW;AACtB,WAAOD,UAAS,EACX,OAAO,MAAM,EACb,MAAMC,MAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;ACpDe,SAAR,WAA4B;AACjC,MAAI,KAAK,GACL,KAAK,GACL,IAAI,GACJ,SAAS,CAAC,GAAG,GACbG,SAAQ,CAAC,GAAG,CAAC,GACb;AAEJ,WAAS,MAAMC,IAAG;AAChB,WAAOA,MAAK,QAAQA,MAAKA,KAAID,OAAM,eAAO,QAAQC,IAAG,GAAG,CAAC,CAAC,IAAI;AAAA,EAChE;AAEA,WAAS,UAAU;AACjB,QAAI,IAAI;AACR,aAAS,IAAI,MAAM,CAAC;AACpB,WAAO,EAAE,IAAI,EAAG,QAAO,CAAC,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI;AACjE,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE;AAAA,EACnF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAKD,SAAQ,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,QAAQ,KAAKA,OAAM,MAAM;AAAA,EAC9F;AAEA,QAAM,eAAe,SAASE,IAAG;AAC/B,QAAI,IAAIF,OAAM,QAAQE,EAAC;AACvB,WAAO,IAAI,IAAI,CAAC,KAAK,GAAG,IAClB,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,IACtB,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,IAC3B,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EACjC;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,aAAa,WAAW;AAC5B,WAAO,OAAO,MAAM;AAAA,EACtB;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,SAAS,EACX,OAAO,CAAC,IAAI,EAAE,CAAC,EACf,MAAMF,MAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,UAAU,KAAK,GAAG,SAAS;AACpD;;;ACpDe,SAAR,YAA6B;AAClC,MAAI,SAAS,CAAC,GAAG,GACbG,SAAQ,CAAC,GAAG,CAAC,GACb,SACA,IAAI;AAER,WAAS,MAAMC,IAAG;AAChB,WAAOA,MAAK,QAAQA,MAAKA,KAAID,OAAM,eAAO,QAAQC,IAAG,GAAG,CAAC,CAAC,IAAI;AAAA,EAChE;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQD,OAAM,SAAS,CAAC,GAAG,SAAS,OAAO,MAAM;AAAA,EAC1H;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUA,SAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQA,OAAM,SAAS,CAAC,GAAG,SAASA,OAAM,MAAM;AAAA,EACxH;AAEA,QAAM,eAAe,SAASE,IAAG;AAC/B,QAAI,IAAIF,OAAM,QAAQE,EAAC;AACvB,WAAO,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EAClC;AAEA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,UAAU,EACZ,OAAO,MAAM,EACb,MAAMF,MAAK,EACX,QAAQ,OAAO;AAAA,EACtB;AAEA,SAAO,UAAU,MAAM,OAAO,SAAS;AACzC;;;ACtCA,IAAMG,MAAK,oBAAI;AAAf,IAAqBC,MAAK,oBAAI;AAEvB,SAAS,aAAa,QAAQ,SAASC,QAAO,OAAO;AAE1D,WAAS,SAASC,OAAM;AACtB,WAAO,OAAOA,QAAO,UAAU,WAAW,IAAI,oBAAI,SAAO,oBAAI,KAAK,CAACA,KAAI,CAAC,GAAGA;AAAA,EAC7E;AAEA,WAAS,QAAQ,CAACA,UAAS;AACzB,WAAO,OAAOA,QAAO,oBAAI,KAAK,CAACA,KAAI,CAAC,GAAGA;AAAA,EACzC;AAEA,WAAS,OAAO,CAACA,UAAS;AACxB,WAAO,OAAOA,QAAO,IAAI,KAAKA,QAAO,CAAC,CAAC,GAAG,QAAQA,OAAM,CAAC,GAAG,OAAOA,KAAI,GAAGA;AAAA,EAC5E;AAEA,WAAS,QAAQ,CAACA,UAAS;AACzB,UAAM,KAAK,SAASA,KAAI,GAAG,KAAK,SAAS,KAAKA,KAAI;AAClD,WAAOA,QAAO,KAAK,KAAKA,QAAO,KAAK;AAAA,EACtC;AAEA,WAAS,SAAS,CAACA,OAAM,SAAS;AAChC,WAAO,QAAQA,QAAO,oBAAI,KAAK,CAACA,KAAI,GAAG,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,GAAGA;AAAA,EAC/E;AAEA,WAAS,QAAQ,CAAC,OAAO,MAAM,SAAS;AACtC,UAAMC,SAAQ,CAAC;AACf,YAAQ,SAAS,KAAK,KAAK;AAC3B,WAAO,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI;AACzC,QAAI,EAAE,QAAQ,SAAS,EAAE,OAAO,GAAI,QAAOA;AAC3C,QAAI;AACJ;AAAG,MAAAA,OAAM,KAAK,WAAW,oBAAI,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;AAAA,WACvE,WAAW,SAAS,QAAQ;AACnC,WAAOA;AAAA,EACT;AAEA,WAAS,SAAS,CAAC,SAAS;AAC1B,WAAO,aAAa,CAACD,UAAS;AAC5B,UAAIA,SAAQA,MAAM,QAAO,OAAOA,KAAI,GAAG,CAAC,KAAKA,KAAI,EAAG,CAAAA,MAAK,QAAQA,QAAO,CAAC;AAAA,IAC3E,GAAG,CAACA,OAAM,SAAS;AACjB,UAAIA,SAAQA,OAAM;AAChB,YAAI,OAAO,EAAG,QAAO,EAAE,QAAQ,GAAG;AAChC,iBAAO,QAAQA,OAAM,EAAE,GAAG,CAAC,KAAKA,KAAI,GAAG;AAAA,UAAC;AAAA,QAC1C;AAAA,YAAO,QAAO,EAAE,QAAQ,GAAG;AACzB,iBAAO,QAAQA,OAAM,CAAE,GAAG,CAAC,KAAKA,KAAI,GAAG;AAAA,UAAC;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAID,QAAO;AACT,aAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,MAAAF,IAAG,QAAQ,CAAC,KAAK,GAAGC,IAAG,QAAQ,CAAC,GAAG;AACnC,aAAOD,GAAE,GAAG,OAAOC,GAAE;AACrB,aAAO,KAAK,MAAMC,OAAMF,KAAIC,GAAE,CAAC;AAAA,IACjC;AAEA,aAAS,QAAQ,CAAC,SAAS;AACzB,aAAO,KAAK,MAAM,IAAI;AACtB,aAAO,CAAC,SAAS,IAAI,KAAK,EAAE,OAAO,KAAK,OAClC,EAAE,OAAO,KAAK,WACd,SAAS,OAAO,QACZ,CAAC,MAAM,MAAM,CAAC,IAAI,SAAS,IAC3B,CAAC,MAAM,SAAS,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,SAAO;AACT;;;AClEO,IAAM,cAAc,aAAa,MAAM;AAE9C,GAAG,CAACI,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,IAAI;AAC3B,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,MAAM;AACf,CAAC;AAGD,YAAY,QAAQ,CAACC,OAAM;AACzB,EAAAA,KAAI,KAAK,MAAMA,EAAC;AAChB,MAAI,CAAC,SAASA,EAAC,KAAK,EAAEA,KAAI,GAAI,QAAO;AACrC,MAAI,EAAEA,KAAI,GAAI,QAAO;AACrB,SAAO,aAAa,CAACD,UAAS;AAC5B,IAAAA,MAAK,QAAQ,KAAK,MAAMA,QAAOC,EAAC,IAAIA,EAAC;AAAA,EACvC,GAAG,CAACD,OAAM,SAAS;AACjB,IAAAA,MAAK,QAAQ,CAACA,QAAO,OAAOC,EAAC;AAAA,EAC/B,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAASA;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,eAAe,YAAY;;;ACxBjC,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,iBAAiB;AACxC,IAAM,eAAe,iBAAiB;AACtC,IAAM,cAAc,eAAe;AACnC,IAAM,eAAe,cAAc;AACnC,IAAM,gBAAgB,cAAc;AACpC,IAAM,eAAe,cAAc;;;ACHnC,IAAM,SAAS,aAAa,CAACC,UAAS;AAC3C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,CAAC;AAC5C,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,UAAU,OAAO;;;ACVvB,IAAM,aAAa,aAAa,CAACC,UAAS;AAC/C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,IAAIA,MAAK,WAAW,IAAI,cAAc;AACjF,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,WAAW;AACzB,CAAC;AAEM,IAAM,cAAc,WAAW;AAE/B,IAAM,YAAY,aAAa,CAACA,UAAS;AAC9C,EAAAA,MAAK,cAAc,GAAG,CAAC;AACzB,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,aAAa,UAAU;;;ACtB7B,IAAM,WAAW,aAAa,CAACC,UAAS;AAC7C,EAAAA,MAAK,QAAQA,QAAOA,MAAK,gBAAgB,IAAIA,MAAK,WAAW,IAAI,iBAAiBA,MAAK,WAAW,IAAI,cAAc;AACtH,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,SAAS;AACvB,CAAC;AAEM,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAACA,UAAS;AAC5C,EAAAA,MAAK,cAAc,GAAG,GAAG,CAAC;AAC5B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,QAAQ,CAACA,QAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,WAAW,QAAQ;;;ACtBzB,IAAM,UAAU;AAAA,EACrB,CAAAC,UAAQA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC,CAACA,OAAM,SAASA,MAAK,QAAQA,MAAK,QAAQ,IAAI,IAAI;AAAA,EAClD,CAAC,OAAO,SAAS,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EACzG,CAAAA,UAAQA,MAAK,QAAQ,IAAI;AAC3B;AAEO,IAAM,WAAW,QAAQ;AAEzB,IAAM,SAAS,aAAa,CAACA,UAAS;AAC3C,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,WAAW,IAAI;AAC7B,CAAC;AAEM,IAAM,UAAU,OAAO;AAEvB,IAAM,UAAU,aAAa,CAACA,UAAS;AAC5C,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAACA,UAAS;AACX,SAAO,KAAK,MAAMA,QAAO,WAAW;AACtC,CAAC;AAEM,IAAM,WAAW,QAAQ;;;AC/BhC,SAAS,YAAY,GAAG;AACtB,SAAO,aAAa,CAACC,UAAS;AAC5B,IAAAA,MAAK,QAAQA,MAAK,QAAQ,KAAKA,MAAK,OAAO,IAAI,IAAI,KAAK,CAAC;AACzD,IAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,QAAQA,MAAK,QAAQ,IAAI,OAAO,CAAC;AAAA,EACxC,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EAClG,CAAC;AACH;AAEO,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,cAAc,YAAY,CAAC;AACjC,IAAM,gBAAgB,YAAY,CAAC;AACnC,IAAM,eAAe,YAAY,CAAC;AAClC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,eAAe,YAAY,CAAC;AAElC,IAAM,cAAc,WAAW;AAC/B,IAAM,cAAc,WAAW;AAC/B,IAAM,eAAe,YAAY;AACjC,IAAM,iBAAiB,cAAc;AACrC,IAAM,gBAAgB,aAAa;AACnC,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AAE1C,SAAS,WAAW,GAAG;AACrB,SAAO,aAAa,CAACA,UAAS;AAC5B,IAAAA,MAAK,WAAWA,MAAK,WAAW,KAAKA,MAAK,UAAU,IAAI,IAAI,KAAK,CAAC;AAClE,IAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,WAAWA,MAAK,WAAW,IAAI,OAAO,CAAC;AAAA,EAC9C,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,aAAa,WAAW,CAAC;AAC/B,IAAM,eAAe,WAAW,CAAC;AACjC,IAAM,cAAc,WAAW,CAAC;AAChC,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,cAAc,WAAW,CAAC;AAEhC,IAAM,aAAa,UAAU;AAC7B,IAAM,aAAa,UAAU;AAC7B,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AACnC,IAAM,eAAe,YAAY;AACjC,IAAM,aAAa,UAAU;AAC7B,IAAM,eAAe,YAAY;;;ACrDjC,IAAM,YAAY,aAAa,CAACC,UAAS;AAC9C,EAAAA,MAAK,QAAQ,CAAC;AACd,EAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,SAASA,MAAK,SAAS,IAAI,IAAI;AACtC,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,SAAS,IAAI,MAAM,SAAS,KAAK,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK;AACzF,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,SAAS;AACvB,CAAC;AAEM,IAAM,aAAa,UAAU;AAE7B,IAAM,WAAW,aAAa,CAACA,UAAS;AAC7C,EAAAA,MAAK,WAAW,CAAC;AACjB,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK,IAAI,eAAe,IAAI,MAAM,eAAe,KAAK;AACrG,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,YAAY,SAAS;;;ACxB3B,IAAM,WAAW,aAAa,CAACC,UAAS;AAC7C,EAAAA,MAAK,SAAS,GAAG,CAAC;AAClB,EAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY;AAC/C,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,YAAY;AAC1B,CAAC;AAGD,SAAS,QAAQ,CAACC,OAAM;AACtB,SAAO,CAAC,SAASA,KAAI,KAAK,MAAMA,EAAC,CAAC,KAAK,EAAEA,KAAI,KAAK,OAAO,aAAa,CAACD,UAAS;AAC9E,IAAAA,MAAK,YAAY,KAAK,MAAMA,MAAK,YAAY,IAAIC,EAAC,IAAIA,EAAC;AACvD,IAAAD,MAAK,SAAS,GAAG,CAAC;AAClB,IAAAA,MAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,YAAYA,MAAK,YAAY,IAAI,OAAOC,EAAC;AAAA,EAChD,CAAC;AACH;AAEO,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAACD,UAAS;AAC5C,EAAAA,MAAK,YAAY,GAAG,CAAC;AACrB,EAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,EAAAA,MAAK,eAAeA,MAAK,eAAe,IAAI,IAAI;AAClD,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,eAAe,IAAI,MAAM,eAAe;AACrD,GAAG,CAACA,UAAS;AACX,SAAOA,MAAK,eAAe;AAC7B,CAAC;AAGD,QAAQ,QAAQ,CAACC,OAAM;AACrB,SAAO,CAAC,SAASA,KAAI,KAAK,MAAMA,EAAC,CAAC,KAAK,EAAEA,KAAI,KAAK,OAAO,aAAa,CAACD,UAAS;AAC9E,IAAAA,MAAK,eAAe,KAAK,MAAMA,MAAK,eAAe,IAAIC,EAAC,IAAIA,EAAC;AAC7D,IAAAD,MAAK,YAAY,GAAG,CAAC;AACrB,IAAAA,MAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAACA,OAAM,SAAS;AACjB,IAAAA,MAAK,eAAeA,MAAK,eAAe,IAAI,OAAOC,EAAC;AAAA,EACtD,CAAC;AACH;AAEO,IAAM,WAAW,QAAQ;;;ACrChC,SAAS,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ;AAEpD,QAAM,gBAAgB;AAAA,IACpB,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAM,IAAI,KAAK,YAAc;AAAA,IAChC,CAAI,KAAM,GAAQ,WAAc;AAAA,IAChC,CAAI,KAAM,GAAI,IAAI,WAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAE,OAAQ,GAAQ,aAAc;AAAA,IAChC,CAAE,OAAQ,GAAI,IAAI,aAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,EAClC;AAEA,WAASC,OAAM,OAAO,MAAMC,QAAO;AACjC,UAAMC,WAAU,OAAO;AACvB,QAAIA,SAAS,EAAC,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AACzC,UAAM,WAAWD,UAAS,OAAOA,OAAM,UAAU,aAAaA,SAAQ,aAAa,OAAO,MAAMA,MAAK;AACrG,UAAMD,SAAQ,WAAW,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC7D,WAAOE,WAAUF,OAAM,QAAQ,IAAIA;AAAA,EACrC;AAEA,WAAS,aAAa,OAAO,MAAMC,QAAO;AACxC,UAAM,SAAS,KAAK,IAAI,OAAO,KAAK,IAAIA;AACxC,UAAM,IAAI,SAAS,CAAC,CAAC,EAAC,EAAEE,KAAI,MAAMA,KAAI,EAAE,MAAM,eAAe,MAAM;AACnE,QAAI,MAAM,cAAc,OAAQ,QAAO,KAAK,MAAM,SAAS,QAAQ,cAAc,OAAO,cAAcF,MAAK,CAAC;AAC5G,QAAI,MAAM,EAAG,QAAO,YAAY,MAAM,KAAK,IAAI,SAAS,OAAO,MAAMA,MAAK,GAAG,CAAC,CAAC;AAC/E,UAAM,CAAC,GAAG,IAAI,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC;AAC3G,WAAO,EAAE,MAAM,IAAI;AAAA,EACrB;AAEA,SAAO,CAACD,QAAO,YAAY;AAC7B;AAEA,IAAM,CAAC,UAAU,eAAe,IAAI,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,SAAS;AACpG,IAAM,CAAC,WAAW,gBAAgB,IAAI,OAAO,UAAU,WAAW,YAAY,SAAS,UAAU,UAAU;;;AC1C3G,SAAS,UAAU,GAAG;AACpB,MAAI,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK;AACzB,QAAII,QAAO,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACpD,IAAAA,MAAK,YAAY,EAAE,CAAC;AACpB,WAAOA;AAAA,EACT;AACA,SAAO,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK;AACzB,QAAIA,QAAO,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC9D,IAAAA,MAAK,eAAe,EAAE,CAAC;AACvB,WAAOA;AAAA,EACT;AACA,SAAO,IAAI,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7D;AAEA,SAAS,QAAQC,IAAG,GAAG,GAAG;AACxB,SAAO,EAAC,GAAGA,IAAG,GAAM,GAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC;AAClD;AAEe,SAAR,aAA8BC,SAAQ;AAC3C,MAAI,kBAAkBA,QAAO,UACzB,cAAcA,QAAO,MACrB,cAAcA,QAAO,MACrB,iBAAiBA,QAAO,SACxB,kBAAkBA,QAAO,MACzB,uBAAuBA,QAAO,WAC9B,gBAAgBA,QAAO,QACvB,qBAAqBA,QAAO;AAEhC,MAAI,WAAW,SAAS,cAAc,GAClC,eAAe,aAAa,cAAc,GAC1C,YAAY,SAAS,eAAe,GACpC,gBAAgB,aAAa,eAAe,GAC5C,iBAAiB,SAAS,oBAAoB,GAC9C,qBAAqB,aAAa,oBAAoB,GACtD,UAAU,SAAS,aAAa,GAChC,cAAc,aAAa,aAAa,GACxC,eAAe,SAAS,kBAAkB,GAC1C,mBAAmB,aAAa,kBAAkB;AAEtD,MAAI,UAAU;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,aAAa;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,SAAS;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAGA,UAAQ,IAAI,UAAU,aAAa,OAAO;AAC1C,UAAQ,IAAI,UAAU,aAAa,OAAO;AAC1C,UAAQ,IAAI,UAAU,iBAAiB,OAAO;AAC9C,aAAW,IAAI,UAAU,aAAa,UAAU;AAChD,aAAW,IAAI,UAAU,aAAa,UAAU;AAChD,aAAW,IAAI,UAAU,iBAAiB,UAAU;AAEpD,WAAS,UAAU,WAAWC,UAAS;AACrC,WAAO,SAASH,OAAM;AACpB,UAAI,SAAS,CAAC,GACV,IAAI,IACJ,IAAI,GACJ,IAAI,UAAU,QACdI,IACAC,MACAC;AAEJ,UAAI,EAAEN,iBAAgB,MAAO,CAAAA,QAAO,oBAAI,KAAK,CAACA,KAAI;AAElD,aAAO,EAAE,IAAI,GAAG;AACd,YAAI,UAAU,WAAW,CAAC,MAAM,IAAI;AAClC,iBAAO,KAAK,UAAU,MAAM,GAAG,CAAC,CAAC;AACjC,eAAKK,OAAM,KAAKD,KAAI,UAAU,OAAO,EAAE,CAAC,CAAC,MAAM,KAAM,CAAAA,KAAI,UAAU,OAAO,EAAE,CAAC;AAAA,cACxE,CAAAC,OAAMD,OAAM,MAAM,MAAM;AAC7B,cAAIE,UAASH,SAAQC,EAAC,EAAG,CAAAA,KAAIE,QAAON,OAAMK,IAAG;AAC7C,iBAAO,KAAKD,EAAC;AACb,cAAI,IAAI;AAAA,QACV;AAAA,MACF;AAEA,aAAO,KAAK,UAAU,MAAM,GAAG,CAAC,CAAC;AACjC,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAAA,EACF;AAEA,WAAS,SAAS,WAAW,GAAG;AAC9B,WAAO,SAAS,QAAQ;AACtB,UAAI,IAAI,QAAQ,MAAM,QAAW,CAAC,GAC9B,IAAI,eAAe,GAAG,WAAW,UAAU,IAAI,CAAC,GAChD,MAAM;AACV,UAAI,KAAK,OAAO,OAAQ,QAAO;AAG/B,UAAI,OAAO,EAAG,QAAO,IAAI,KAAK,EAAE,CAAC;AACjC,UAAI,OAAO,EAAG,QAAO,IAAI,KAAK,EAAE,IAAI,OAAQ,OAAO,IAAI,EAAE,IAAI,EAAE;AAG/D,UAAI,KAAK,EAAE,OAAO,GAAI,GAAE,IAAI;AAG5B,UAAI,OAAO,EAAG,GAAE,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAGrC,UAAI,EAAE,MAAM,OAAW,GAAE,IAAI,OAAO,IAAI,EAAE,IAAI;AAG9C,UAAI,OAAO,GAAG;AACZ,YAAI,EAAE,IAAI,KAAK,EAAE,IAAI,GAAI,QAAO;AAChC,YAAI,EAAE,OAAO,GAAI,GAAE,IAAI;AACvB,YAAI,OAAO,GAAG;AACZ,iBAAO,QAAQ,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,UAAU;AACzD,iBAAO,MAAM,KAAK,QAAQ,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,IAAI;AACnE,iBAAO,OAAO,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC;AACxC,YAAE,IAAI,KAAK,eAAe;AAC1B,YAAE,IAAI,KAAK,YAAY;AACvB,YAAE,IAAI,KAAK,WAAW,KAAK,EAAE,IAAI,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,UAAU,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,KAAK,OAAO;AACxD,iBAAO,MAAM,KAAK,QAAQ,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,IAAI;AACrE,iBAAO,QAAQ,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC;AACzC,YAAE,IAAI,KAAK,YAAY;AACvB,YAAE,IAAI,KAAK,SAAS;AACpB,YAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK;AAAA,QACrC;AAAA,MACF,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,YAAI,EAAE,OAAO,GAAI,GAAE,IAAI,OAAO,IAAI,EAAE,IAAI,IAAI,OAAO,IAAI,IAAI;AAC3D,cAAM,OAAO,IAAI,QAAQ,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,UAAU,IAAI,UAAU,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO;AAChG,UAAE,IAAI;AACN,UAAE,IAAI,OAAO,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,MAAM,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,MAAM,KAAK;AAAA,MACzF;AAIA,UAAI,OAAO,GAAG;AACZ,UAAE,KAAK,EAAE,IAAI,MAAM;AACnB,UAAE,KAAK,EAAE,IAAI;AACb,eAAO,QAAQ,CAAC;AAAA,MAClB;AAGA,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,eAAe,GAAG,WAAW,QAAQ,GAAG;AAC/C,QAAI,IAAI,GACJ,IAAI,UAAU,QACd,IAAI,OAAO,QACXA,IACA;AAEJ,WAAO,IAAI,GAAG;AACZ,UAAI,KAAK,EAAG,QAAO;AACnB,MAAAA,KAAI,UAAU,WAAW,GAAG;AAC5B,UAAIA,OAAM,IAAI;AACZ,QAAAA,KAAI,UAAU,OAAO,GAAG;AACxB,gBAAQ,OAAOA,MAAK,OAAO,UAAU,OAAO,GAAG,IAAIA,EAAC;AACpD,YAAI,CAAC,UAAW,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAI,QAAO;AAAA,MACxD,WAAWA,MAAK,OAAO,WAAW,GAAG,GAAG;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,GAAG,QAAQ,GAAG;AACjC,QAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,WAAO,KAAK,EAAE,IAAI,aAAa,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC7E;AAEA,WAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,QAAI,IAAI,eAAe,KAAK,OAAO,MAAM,CAAC,CAAC;AAC3C,WAAO,KAAK,EAAE,IAAI,mBAAmB,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EACnF;AAEA,WAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,QAAI,IAAI,UAAU,KAAK,OAAO,MAAM,CAAC,CAAC;AACtC,WAAO,KAAK,EAAE,IAAI,cAAc,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC9E;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,QAAI,IAAI,aAAa,KAAK,OAAO,MAAM,CAAC,CAAC;AACzC,WAAO,KAAK,EAAE,IAAI,iBAAiB,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EACjF;AAEA,WAAS,WAAW,GAAG,QAAQ,GAAG;AAChC,QAAI,IAAI,QAAQ,KAAK,OAAO,MAAM,CAAC,CAAC;AACpC,WAAO,KAAK,EAAE,IAAI,YAAY,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAAA,EAC5E;AAEA,WAAS,oBAAoB,GAAG,QAAQ,GAAG;AACzC,WAAO,eAAe,GAAG,iBAAiB,QAAQ,CAAC;AAAA,EACrD;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,WAAO,eAAe,GAAG,aAAa,QAAQ,CAAC;AAAA,EACjD;AAEA,WAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,WAAO,eAAe,GAAG,aAAa,QAAQ,CAAC;AAAA,EACjD;AAEA,WAAS,mBAAmB,GAAG;AAC7B,WAAO,qBAAqB,EAAE,OAAO,CAAC;AAAA,EACxC;AAEA,WAAS,cAAc,GAAG;AACxB,WAAO,gBAAgB,EAAE,OAAO,CAAC;AAAA,EACnC;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,mBAAmB,EAAE,SAAS,CAAC;AAAA,EACxC;AAEA,WAAS,YAAY,GAAG;AACtB,WAAO,cAAc,EAAE,SAAS,CAAC;AAAA,EACnC;AAEA,WAAS,aAAa,GAAG;AACvB,WAAO,eAAe,EAAE,EAAE,SAAS,KAAK,GAAG;AAAA,EAC7C;AAEA,WAAS,cAAc,GAAG;AACxB,WAAO,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI;AAAA,EAC/B;AAEA,WAAS,sBAAsB,GAAG;AAChC,WAAO,qBAAqB,EAAE,UAAU,CAAC;AAAA,EAC3C;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,gBAAgB,EAAE,UAAU,CAAC;AAAA,EACtC;AAEA,WAAS,oBAAoB,GAAG;AAC9B,WAAO,mBAAmB,EAAE,YAAY,CAAC;AAAA,EAC3C;AAEA,WAAS,eAAe,GAAG;AACzB,WAAO,cAAc,EAAE,YAAY,CAAC;AAAA,EACtC;AAEA,WAAS,gBAAgB,GAAG;AAC1B,WAAO,eAAe,EAAE,EAAE,YAAY,KAAK,GAAG;AAAA,EAChD;AAEA,WAAS,iBAAiB,GAAG;AAC3B,WAAO,IAAI,CAAC,EAAE,EAAE,YAAY,IAAI;AAAA,EAClC;AAEA,SAAO;AAAA,IACL,QAAQ,SAAS,WAAW;AAC1B,UAAI,IAAI,UAAU,aAAa,IAAI,OAAO;AAC1C,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,WAAW;AACzB,UAAI,IAAI,SAAS,aAAa,IAAI,KAAK;AACvC,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,WAAW;AAC7B,UAAI,IAAI,UAAU,aAAa,IAAI,UAAU;AAC7C,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,IACA,UAAU,SAAS,WAAW;AAC5B,UAAI,IAAI,SAAS,aAAa,IAAI,IAAI;AACtC,QAAE,WAAW,WAAW;AAAE,eAAO;AAAA,MAAW;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,OAAO,EAAC,KAAK,IAAI,KAAK,KAAK,KAAK,IAAG;AAAvC,IACI,WAAW;AADf,IAEI,YAAY;AAFhB,IAGI,YAAY;AAEhB,SAAS,IAAI,OAAO,MAAM,OAAO;AAC/B,MAAIG,QAAO,QAAQ,IAAI,MAAM,IACzB,UAAUA,QAAO,CAAC,QAAQ,SAAS,IACnCC,UAAS,OAAO;AACpB,SAAOD,SAAQC,UAAS,QAAQ,IAAI,MAAM,QAAQA,UAAS,CAAC,EAAE,KAAK,IAAI,IAAI,SAAS;AACtF;AAEA,SAAS,QAAQC,IAAG;AAClB,SAAOA,GAAE,QAAQ,WAAW,MAAM;AACpC;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,IAAI,OAAO,SAAS,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,IAAI,KAAK,GAAG;AACpE;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,YAAY,GAAG,CAAC,CAAC,CAAC;AAChE;AAEA,SAAS,yBAAyB,GAAG,QAAQ,GAAG;AAC9C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,yBAAyB,GAAG,QAAQ,GAAG;AAC9C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,sBAAsB,GAAG,QAAQ,GAAG;AAC3C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,mBAAmB,GAAG,QAAQ,GAAG;AACxC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,sBAAsB,GAAG,QAAQ,GAAG;AAC3C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,cAAc,GAAG,QAAQ,GAAG;AACnC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,UAAU,GAAG,QAAQ,GAAG;AAC/B,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,MAAO,IAAI,EAAE,CAAC,EAAE,UAAU;AAC3E;AAEA,SAAS,UAAU,GAAG,QAAQ,GAAG;AAC/B,MAAI,IAAI,+BAA+B,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAClE,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,IAAI,EAAE,CAAC,EAAE,UAAU;AAC5E;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACrD;AAEA,SAAS,iBAAiB,GAAG,QAAQ,GAAG;AACtC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACjD;AAEA,SAAS,gBAAgB,GAAG,QAAQ,GAAG;AACrC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,eAAe,GAAG,QAAQ,GAAG;AACpC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AACvD;AAEA,SAAS,YAAY,GAAG,QAAQ,GAAG;AACjC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,aAAa,GAAG,QAAQ,GAAG;AAClC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,kBAAkB,GAAG,QAAQ,GAAG;AACvC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC5C,SAAO,KAAK,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC,IAAI,GAAI,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAChE;AAEA,SAAS,oBAAoB,GAAG,QAAQ,GAAG;AACzC,MAAI,IAAI,UAAU,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAC7C,SAAO,IAAI,IAAI,EAAE,CAAC,EAAE,SAAS;AAC/B;AAEA,SAAS,mBAAmB,GAAG,QAAQ,GAAG;AACxC,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,0BAA0B,GAAG,QAAQ,GAAG;AAC/C,MAAI,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,CAAC;AACrC,SAAO,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,UAAU;AAC9C;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,QAAQ,GAAG,GAAG,CAAC;AAC9B;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,IAAI,EAAE,SAAS,GAAG,GAAG,CAAC;AAC/B;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,IAAI,EAAE,SAAS,IAAI,MAAM,IAAI,GAAG,CAAC;AAC1C;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,IAAI,QAAQ,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACpD;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,IAAI,EAAE,gBAAgB,GAAG,GAAG,CAAC;AACtC;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,mBAAmB,GAAG,CAAC,IAAI;AACpC;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,SAAO,IAAI,EAAE,SAAS,IAAI,GAAG,GAAG,CAAC;AACnC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,0BAA0B,GAAG;AACpC,MAAI,MAAM,EAAE,OAAO;AACnB,SAAO,QAAQ,IAAI,IAAI;AACzB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,IAAI,WAAW,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACvD;AAEA,SAAS,KAAK,GAAG;AACf,MAAI,MAAM,EAAE,OAAO;AACnB,SAAQ,OAAO,KAAK,QAAQ,IAAK,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC;AACxE;AAEA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,KAAK,CAAC;AACV,SAAO,IAAI,aAAa,MAAM,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,EAAE,OAAO,MAAM,IAAI,GAAG,CAAC;AACpF;AAEA,SAAS,0BAA0B,GAAG;AACpC,SAAO,EAAE,OAAO;AAClB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,SAAO,IAAI,WAAW,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACvD;AAEA,SAAS,WAAW,GAAG,GAAG;AACxB,SAAO,IAAI,EAAE,YAAY,IAAI,KAAK,GAAG,CAAC;AACxC;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,KAAK,CAAC;AACV,SAAO,IAAI,EAAE,YAAY,IAAI,KAAK,GAAG,CAAC;AACxC;AAEA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,IAAI,EAAE,YAAY,IAAI,KAAO,GAAG,CAAC;AAC1C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,MAAM,EAAE,OAAO;AACnB,MAAK,OAAO,KAAK,QAAQ,IAAK,aAAa,CAAC,IAAI,aAAa,KAAK,CAAC;AACnE,SAAO,IAAI,EAAE,YAAY,IAAI,KAAO,GAAG,CAAC;AAC1C;AAEA,SAAS,WAAW,GAAG;AACrB,MAAI,IAAI,EAAE,kBAAkB;AAC5B,UAAQ,IAAI,IAAI,OAAO,KAAK,IAAI,QAC1B,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,IACtB,IAAI,IAAI,IAAI,KAAK,CAAC;AAC1B;AAEA,SAAS,oBAAoB,GAAG,GAAG;AACjC,SAAO,IAAI,EAAE,WAAW,GAAG,GAAG,CAAC;AACjC;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,EAAE,YAAY,GAAG,GAAG,CAAC;AAClC;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,IAAI,EAAE,YAAY,IAAI,MAAM,IAAI,GAAG,CAAC;AAC7C;AAEA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,IAAI,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAClD;AAEA,SAAS,sBAAsB,GAAG,GAAG;AACnC,SAAO,IAAI,EAAE,mBAAmB,GAAG,GAAG,CAAC;AACzC;AAEA,SAAS,sBAAsB,GAAG,GAAG;AACnC,SAAO,sBAAsB,GAAG,CAAC,IAAI;AACvC;AAEA,SAAS,qBAAqB,GAAG,GAAG;AAClC,SAAO,IAAI,EAAE,YAAY,IAAI,GAAG,GAAG,CAAC;AACtC;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,cAAc,GAAG,GAAG,CAAC;AACpC;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,IAAI,EAAE,cAAc,GAAG,GAAG,CAAC;AACpC;AAEA,SAAS,6BAA6B,GAAG;AACvC,MAAI,MAAM,EAAE,UAAU;AACtB,SAAO,QAAQ,IAAI,IAAI;AACzB;AAEA,SAAS,0BAA0B,GAAG,GAAG;AACvC,SAAO,IAAI,UAAU,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACrD;AAEA,SAAS,QAAQ,GAAG;AAClB,MAAI,MAAM,EAAE,UAAU;AACtB,SAAQ,OAAO,KAAK,QAAQ,IAAK,YAAY,CAAC,IAAI,YAAY,KAAK,CAAC;AACtE;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,MAAI,QAAQ,CAAC;AACb,SAAO,IAAI,YAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,IAAI,GAAG,CAAC;AACpF;AAEA,SAAS,6BAA6B,GAAG;AACvC,SAAO,EAAE,UAAU;AACrB;AAEA,SAAS,0BAA0B,GAAG,GAAG;AACvC,SAAO,IAAI,UAAU,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACrD;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,IAAI,EAAE,eAAe,IAAI,KAAK,GAAG,CAAC;AAC3C;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,MAAI,QAAQ,CAAC;AACb,SAAO,IAAI,EAAE,eAAe,IAAI,KAAK,GAAG,CAAC;AAC3C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,SAAO,IAAI,EAAE,eAAe,IAAI,KAAO,GAAG,CAAC;AAC7C;AAEA,SAAS,qBAAqB,GAAG,GAAG;AAClC,MAAI,MAAM,EAAE,UAAU;AACtB,MAAK,OAAO,KAAK,QAAQ,IAAK,YAAY,CAAC,IAAI,YAAY,KAAK,CAAC;AACjE,SAAO,IAAI,EAAE,eAAe,IAAI,KAAO,GAAG,CAAC;AAC7C;AAEA,SAAS,gBAAgB;AACvB,SAAO;AACT;AAEA,SAAS,uBAAuB;AAC9B,SAAO;AACT;AAEA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,CAAC;AACV;AAEA,SAAS,2BAA2B,GAAG;AACrC,SAAO,KAAK,MAAM,CAAC,IAAI,GAAI;AAC7B;;;ACtrBA,IAAIC;AACG,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEXC,eAAc;AAAA,EACZ,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS,CAAC,MAAM,IAAI;AAAA,EACpB,MAAM,CAAC,UAAU,UAAU,WAAW,aAAa,YAAY,UAAU,UAAU;AAAA,EACnF,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3D,QAAQ,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;AAAA,EACjI,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAClG,CAAC;AAEc,SAARA,eAA+B,YAAY;AAChD,EAAAD,UAAS,aAAa,UAAU;AAChC,eAAaA,QAAO;AACpB,cAAYA,QAAO;AACnB,cAAYA,QAAO;AACnB,aAAWA,QAAO;AAClB,SAAOA;AACT;;;ACxBO,IAAI,eAAe;AAE1B,SAAS,gBAAgBE,OAAM;AAC7B,SAAOA,MAAK,YAAY;AAC1B;AAEA,IAAI,YAAY,KAAK,UAAU,cACzB,kBACA,UAAU,YAAY;AAE5B,IAAO,oBAAQ;;;ACTf,SAAS,eAAe,QAAQ;AAC9B,MAAIC,QAAO,IAAI,KAAK,MAAM;AAC1B,SAAO,MAAMA,KAAI,IAAI,OAAOA;AAC9B;AAEA,IAAI,WAAW,CAAC,oBAAI,KAAK,0BAA0B,IAC7C,iBACA,SAAS,YAAY;AAE3B,IAAO,mBAAQ;;;ACNf,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,KAAK,CAAC;AACnB;AAEA,SAASC,QAAO,GAAG;AACjB,SAAO,aAAa,OAAO,CAAC,IAAI,CAAC,oBAAI,KAAK,CAAC,CAAC;AAC9C;AAEO,SAAS,SAASC,QAAO,cAAc,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQC,SAAQC,SAAQ;AAClG,MAAI,QAAQ,WAAW,GACnB,SAAS,MAAM,QACf,SAAS,MAAM;AAEnB,MAAI,oBAAoBA,QAAO,KAAK,GAChC,eAAeA,QAAO,KAAK,GAC3B,eAAeA,QAAO,OAAO,GAC7B,aAAaA,QAAO,OAAO,GAC3B,YAAYA,QAAO,OAAO,GAC1B,aAAaA,QAAO,OAAO,GAC3B,cAAcA,QAAO,IAAI,GACzBC,cAAaD,QAAO,IAAI;AAE5B,WAASE,YAAWC,OAAM;AACxB,YAAQJ,QAAOI,KAAI,IAAIA,QAAO,oBACxB,OAAOA,KAAI,IAAIA,QAAO,eACtB,KAAKA,KAAI,IAAIA,QAAO,eACpB,IAAIA,KAAI,IAAIA,QAAO,aACnB,MAAMA,KAAI,IAAIA,QAAQ,KAAKA,KAAI,IAAIA,QAAO,YAAY,aACtD,KAAKA,KAAI,IAAIA,QAAO,cACpBF,aAAYE,KAAI;AAAA,EACxB;AAEA,QAAM,SAAS,SAASC,IAAG;AACzB,WAAO,IAAI,KAAK,OAAOA,EAAC,CAAC;AAAA,EAC3B;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,SAAS,OAAO,MAAM,KAAK,GAAGP,OAAM,CAAC,IAAI,OAAO,EAAE,IAAI,IAAI;AAAA,EAC7E;AAEA,QAAM,QAAQ,SAAS,UAAU;AAC/B,QAAI,IAAI,OAAO;AACf,WAAOC,OAAM,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,YAAY,OAAO,KAAK,QAAQ;AAAA,EACtE;AAEA,QAAM,aAAa,SAASO,QAAO,WAAW;AAC5C,WAAO,aAAa,OAAOH,cAAaF,QAAO,SAAS;AAAA,EAC1D;AAEA,QAAM,OAAO,SAAS,UAAU;AAC9B,QAAI,IAAI,OAAO;AACf,QAAI,CAAC,YAAY,OAAO,SAAS,UAAU,WAAY,YAAW,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,YAAY,OAAO,KAAK,QAAQ;AACtI,WAAO,WAAW,OAAOM,MAAK,GAAG,QAAQ,CAAC,IAAI;AAAA,EAChD;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,KAAK,OAAO,SAASR,QAAO,cAAc,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQC,SAAQC,OAAM,CAAC;AAAA,EACxG;AAEA,SAAO;AACT;AAEe,SAAR,OAAwB;AAC7B,SAAO,UAAU,MAAM,SAAS,WAAW,kBAAkB,UAAU,WAAW,YAAU,SAAS,UAAU,YAAY,QAAY,UAAU,EAAE,OAAO,CAAC,IAAI,KAAK,KAAM,GAAG,CAAC,GAAG,IAAI,KAAK,KAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;AACpN;;;ACjEe,SAAR,UAA2B;AAChC,SAAO,UAAU,MAAM,SAAS,UAAU,iBAAiB,SAAS,UAAU,WAAS,QAAQ,SAAS,WAAW,QAAW,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,KAAM,GAAG,CAAC,GAAG,KAAK,IAAI,KAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;AAC1M;;;ACCA,SAASO,eAAc;AACrB,MAAI,KAAK,GACL,KAAK,GACLC,KACAC,KACA,KACA,WACA,eAAeC,WACf,QAAQ,OACR;AAEJ,WAAS,MAAMC,IAAG;AAChB,WAAOA,MAAK,QAAQ,MAAMA,KAAI,CAACA,EAAC,IAAI,UAAU,aAAa,QAAQ,IAAI,OAAOA,MAAK,UAAUA,EAAC,IAAIH,OAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAGG,EAAC,CAAC,IAAIA,GAAE;AAAA,EACvJ;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAGH,MAAK,UAAU,KAAK,CAAC,EAAE,GAAGC,MAAK,UAAU,KAAK,CAAC,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,KAAKA,MAAKD,MAAK,SAAS,CAAC,IAAI,EAAE;AAAA,EACpJ;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,WAASI,OAAM,aAAa;AAC1B,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI;AACR,aAAO,UAAU,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,eAAe,YAAY,IAAI,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAAA,IACzH;AAAA,EACF;AAEA,QAAM,QAAQA,OAAM,aAAW;AAE/B,QAAM,aAAaA,OAAM,aAAgB;AAEzC,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG;AACjB,gBAAY,GAAGJ,MAAK,EAAE,EAAE,GAAGC,MAAK,EAAE,EAAE,GAAG,MAAMD,QAAOC,MAAK,IAAI,KAAKA,MAAKD;AACvE,WAAO;AAAA,EACT;AACF;AAEO,SAASK,MAAK,QAAQ,QAAQ;AACnC,SAAO,OACF,OAAO,OAAO,OAAO,CAAC,EACtB,aAAa,OAAO,aAAa,CAAC,EAClC,MAAM,OAAO,MAAM,CAAC,EACpB,QAAQ,OAAO,QAAQ,CAAC;AAC/B;AAEe,SAAR,aAA8B;AACnC,MAAI,QAAQ,UAAUN,aAAY,EAAEG,SAAQ,CAAC;AAE7C,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,WAAW,CAAC;AAAA,EACjC;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,MAAI,QAAQ,QAAQN,aAAY,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AAEjD,QAAM,OAAO,WAAW;AACtB,WAAOM,MAAK,OAAO,cAAc,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EACvD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,mBAAmB;AACjC,MAAI,QAAQ,UAAUN,aAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAOM,MAAK,OAAO,iBAAiB,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAClE;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,MAAI,QAAQ,OAAON,aAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAOM,MAAK,OAAO,cAAc,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAC/D;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,iBAAiB;AAC/B,SAAO,cAAc,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AAC1D;;;ACtGe,SAAR,qBAAsC;AAC3C,MAAI,SAAS,CAAC,GACV,eAAeC;AAEnB,WAAS,MAAMC,IAAG;AAChB,QAAIA,MAAK,QAAQ,CAAC,MAAMA,KAAI,CAACA,EAAC,EAAG,QAAO,cAAc,eAAO,QAAQA,IAAG,CAAC,IAAI,MAAM,OAAO,SAAS,EAAE;AAAA,EACvG;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO,OAAO,MAAM;AAC3C,aAAS,CAAC;AACV,aAAS,KAAK,EAAG,KAAI,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,EAAG,QAAO,KAAK,CAAC;AAC/D,WAAO,KAAK,SAAS;AACrB,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,QAAM,QAAQ,WAAW;AACvB,WAAO,OAAO,IAAI,CAAC,GAAG,MAAM,aAAa,KAAK,OAAO,SAAS,EAAE,CAAC;AAAA,EACnE;AAEA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,MAAM,KAAK,EAAC,QAAQ,IAAI,EAAC,GAAG,CAAC,GAAG,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC;AAAA,EACtE;AAEA,QAAM,OAAO,WAAW;AACtB,WAAO,mBAAmB,YAAY,EAAE,OAAO,MAAM;AAAA,EACvD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;;;AC5BA,SAASC,eAAc;AACrB,MAAI,KAAK,GACL,KAAK,KACL,KAAK,GACLC,KAAI,GACJC,KACAC,KACAC,KACA,KACA,KACA,eAAeC,WACf,WACA,QAAQ,OACR;AAEJ,WAAS,MAAMC,IAAG;AAChB,WAAO,MAAMA,KAAI,CAACA,EAAC,IAAI,WAAWA,KAAI,QAAQA,KAAI,CAAC,UAAUA,EAAC,KAAKH,QAAOF,KAAIK,KAAIL,KAAIE,MAAK,MAAM,MAAM,aAAa,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAGG,EAAC,CAAC,IAAIA,EAAC;AAAA,EAC7J;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,CAAC,IAAI,IAAI,EAAE,IAAI,GAAGJ,MAAK,UAAU,KAAK,CAAC,EAAE,GAAGC,MAAK,UAAU,KAAK,CAAC,EAAE,GAAGC,MAAK,UAAU,KAAK,CAAC,EAAE,GAAG,MAAMF,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAK,MAAMC,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAKF,KAAIE,MAAKD,MAAK,KAAK,GAAG,SAAS,CAAC,IAAI,IAAI,EAAE;AAAA,EACrP;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,SAAS;AAAA,EACnD;AAEA,QAAM,eAAe,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,eAAe,GAAG,SAAS;AAAA,EACxD;AAEA,WAASK,OAAM,aAAa;AAC1B,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,UAAU,CAAC,IAAI,IAAI,EAAE,IAAI,GAAG,eAAe,UAAU,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,CAAC;AAAA,IACjK;AAAA,EACF;AAEA,QAAM,QAAQA,OAAM,aAAW;AAE/B,QAAM,aAAaA,OAAM,aAAgB;AAEzC,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,GAAG,SAAS;AAAA,EACnD;AAEA,SAAO,SAAS,GAAG;AACjB,gBAAY,GAAGL,MAAK,EAAE,EAAE,GAAGC,MAAK,EAAE,EAAE,GAAGC,MAAK,EAAE,EAAE,GAAG,MAAMF,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAK,MAAMC,QAAOC,MAAK,IAAI,OAAOA,MAAKD,MAAKF,KAAIE,MAAKD,MAAK,KAAK;AACpJ,WAAO;AAAA,EACT;AACF;AAEe,SAAR,YAA6B;AAClC,MAAI,QAAQ,UAAUF,aAAY,EAAEK,SAAQ,CAAC;AAE7C,QAAM,OAAO,WAAW;AACtB,WAAOG,MAAK,OAAO,UAAU,CAAC;AAAA,EAChC;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,eAAe;AAC7B,MAAI,QAAQ,QAAQR,aAAY,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;AAEtD,QAAM,OAAO,WAAW;AACtB,WAAOQ,MAAK,OAAO,aAAa,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EACtD;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,kBAAkB;AAChC,MAAI,QAAQ,UAAUR,aAAY,CAAC;AAEnC,QAAM,OAAO,WAAW;AACtB,WAAOQ,MAAK,OAAO,gBAAgB,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EACjE;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,eAAe;AAC7B,MAAI,QAAQ,OAAOR,aAAY,CAAC;AAEhC,QAAM,OAAO,WAAW;AACtB,WAAOQ,MAAK,OAAO,aAAa,CAAC,EAAE,SAAS,MAAM,SAAS,CAAC;AAAA,EAC9D;AAEA,SAAO,iBAAiB,MAAM,OAAO,SAAS;AAChD;AAEO,SAAS,gBAAgB;AAC9B,SAAO,aAAa,MAAM,MAAM,SAAS,EAAE,SAAS,GAAG;AACzD;;;ACvGe,SAARC,kBAAiBC,IAAG;AACzB,SAAO,SAASC,YAAW;AACzB,WAAOD;AAAA,EACT;AACF;;;ACJO,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,MAAM,KAAK;AACjB,IAAME,OAAM,KAAK;AACjB,IAAMC,OAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAMC,QAAO,KAAK;AAElB,IAAMC,WAAU;AAChB,IAAMC,MAAK,KAAK;AAChB,IAAM,SAASA,MAAK;AACpB,IAAMC,OAAM,IAAID;AAEhB,SAAS,KAAKE,IAAG;AACtB,SAAOA,KAAI,IAAI,IAAIA,KAAI,KAAKF,MAAK,KAAK,KAAKE,EAAC;AAC9C;AAEO,SAAS,KAAKA,IAAG;AACtB,SAAOA,MAAK,IAAI,SAASA,MAAK,KAAK,CAAC,SAAS,KAAK,KAAKA,EAAC;AAC1D;;;ACjBO,SAAS,SAAS,OAAO;AAC9B,MAAI,SAAS;AAEb,QAAM,SAAS,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,QAAI,KAAK,MAAM;AACb,eAAS;AAAA,IACX,OAAO;AACL,YAAM,IAAI,KAAK,MAAM,CAAC;AACtB,UAAI,EAAE,KAAK,GAAI,OAAM,IAAI,WAAW,mBAAmB,CAAC,EAAE;AAC1D,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,IAAI,KAAK,MAAM;AAC9B;;;ACdA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,cAAc,GAAG;AACxB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,KAAK,EAAE;AAChB;AAEA,SAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjD,MAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,IAAI,MAAM,MAAM,MAAM;AAC1B,MAAI,IAAI,IAAIC,SAAS;AACrB,OAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO;AAC1C,SAAO,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG;AACpC;AAIA,SAAS,eAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,MAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,KAAK,CAAC,MAAMC,MAAK,MAAM,MAAM,MAAM,GAAG,GACjD,KAAK,KAAK,KACV,KAAK,CAAC,KAAK,KACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,OAAO,MAAM,OAAO,GACpB,OAAO,MAAM,OAAO,GACpB,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,IAAI,KAAK,IACTC,KAAI,MAAM,MAAM,MAAM,KACtB,KAAK,KAAK,IAAI,KAAK,KAAKD,MAAKE,KAAI,GAAG,IAAI,IAAI,KAAKD,KAAIA,EAAC,CAAC,GACvD,OAAOA,KAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAACA,KAAI,KAAK,KAAK,KAAK,IAC3B,OAAOA,KAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAACA,KAAI,KAAK,KAAK,KAAK,IAC3B,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM;AAIhB,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAK,OAAM,KAAK,MAAM;AAEpE,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,KAAK,OAAO,KAAK,IAAI;AAAA,IACrB,KAAK,OAAO,KAAK,IAAI;AAAA,EACvB;AACF;AAEe,SAAR,cAAmB;AACxB,MAAI,cAAc,gBACd,cAAc,gBACd,eAAeE,kBAAS,CAAC,GACzB,YAAY,MACZ,aAAa,eACb,WAAW,aACX,WAAW,aACX,UAAU,MACVC,QAAO,SAAS,GAAG;AAEvB,WAAS,MAAM;AACb,QAAI,QACA,GACA,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,WAAW,MAAM,MAAM,SAAS,IAAI,QACzC,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,QACvC,KAAK,IAAI,KAAK,EAAE,GAChB,KAAK,KAAK;AAEd,QAAI,CAAC,QAAS,WAAU,SAASA,MAAK;AAGtC,QAAI,KAAK,GAAI,KAAI,IAAI,KAAK,IAAI,KAAK;AAGnC,QAAI,EAAE,KAAKL,UAAU,SAAQ,OAAO,GAAG,CAAC;AAAA,aAG/B,KAAKM,OAAMN,UAAS;AAC3B,cAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,cAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AACjC,UAAI,KAAKA,UAAS;AAChB,gBAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,gBAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MAClC;AAAA,IACF,OAGK;AACH,UAAI,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,GACvC,KAAM,KAAKA,aAAa,YAAY,CAAC,UAAU,MAAM,MAAM,SAAS,IAAIC,MAAK,KAAK,KAAK,KAAK,EAAE,IAC9F,KAAKM,KAAI,IAAI,KAAK,EAAE,IAAI,GAAG,CAAC,aAAa,MAAM,MAAM,SAAS,CAAC,GAC/D,MAAM,IACN,MAAM,IACNC,KACAC;AAGJ,UAAI,KAAKT,UAAS;AAChB,YAAI,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC,GAC3B,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC;AAC/B,aAAK,OAAO,KAAK,KAAKA,SAAS,OAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;AAAA,YACjE,OAAM,GAAG,MAAM,OAAO,KAAK,MAAM;AACtC,aAAK,OAAO,KAAK,KAAKA,SAAS,OAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;AAAA,YACjE,OAAM,GAAG,MAAM,OAAO,KAAK,MAAM;AAAA,MACxC;AAEA,UAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG;AAGtB,UAAI,KAAKA,UAAS;AAChB,YAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB;AAKJ,YAAI,KAAKU,KAAI;AACX,cAAI,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG;AAC1D,gBAAI,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,OAAOT,MAAK,KAAK,KAAK,KAAK,EAAE,IAAIA,MAAK,KAAK,KAAK,KAAK,EAAE,EAAE,IAAI,CAAC,GAChG,KAAKA,MAAK,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3C,kBAAMM,KAAI,KAAK,KAAK,OAAO,KAAK,EAAE;AAClC,kBAAMA,KAAI,KAAK,KAAK,OAAO,KAAK,EAAE;AAAA,UACpC,OAAO;AACL,kBAAM,MAAM;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAGA,UAAI,EAAE,MAAMP,UAAU,SAAQ,OAAO,KAAK,GAAG;AAAA,eAGpC,MAAMA,UAAS;AACtB,QAAAQ,MAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AACnD,QAAAC,MAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AAEnD,gBAAQ,OAAOD,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG;AAG7C,YAAI,MAAM,GAAI,SAAQ,IAAIA,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMC,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAAA,aAGzF;AACH,kBAAQ,IAAID,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAChF,kBAAQ,IAAI,GAAG,GAAG,IAAI,MAAMA,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMC,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AACvG,kBAAQ,IAAIA,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAAA,QAClF;AAAA,MACF,MAGK,SAAQ,OAAO,KAAK,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,CAAC,EAAE;AAIlE,UAAI,EAAE,KAAKT,aAAY,EAAE,MAAMA,UAAU,SAAQ,OAAO,KAAK,GAAG;AAAA,eAGvD,MAAMA,UAAS;AACtB,QAAAQ,MAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACpD,QAAAC,MAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AAEpD,gBAAQ,OAAOD,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG;AAG7C,YAAI,MAAM,GAAI,SAAQ,IAAIA,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMC,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAAA,aAGzF;AACH,kBAAQ,IAAID,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAChF,kBAAQ,IAAI,GAAG,GAAG,IAAI,MAAMA,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMC,IAAG,KAAKA,IAAG,KAAKA,IAAG,KAAKA,IAAG,GAAG,GAAG,EAAE;AACtG,kBAAQ,IAAIA,IAAG,IAAIA,IAAG,IAAI,KAAK,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,MAAMA,IAAG,KAAKA,IAAG,GAAG,GAAG,CAAC,EAAE;AAAA,QAClF;AAAA,MACF,MAGK,SAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,EAAE;AAAA,IACzC;AAEA,YAAQ,UAAU;AAElB,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AAEA,MAAI,WAAW,WAAW;AACxB,QAAI,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,IAAI,CAAC,YAAY,MAAM,MAAM,SAAS,KAAK,GAClFE,MAAK,CAAC,WAAW,MAAM,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,MAAM,SAAS,KAAK,IAAID,MAAK;AAC3F,WAAO,CAAC,IAAIC,EAAC,IAAI,GAAG,IAAIA,EAAC,IAAI,CAAC;AAAA,EAChC;AAEA,MAAI,cAAc,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAIP,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC9F;AAEA,MAAI,cAAc,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC9F;AAEA,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC/F;AAEA,MAAI,YAAY,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,YAAY,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC/G;AAEA,MAAI,aAAa,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC7F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F;AAEA,MAAI,UAAU,SAAS,GAAG;AACxB,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAI,OAAO;AAAA,EACtE;AAEA,SAAO;AACT;;;AC3QA,SAAS,OAAO,SAAS;AACvB,OAAK,WAAW;AAClB;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASQ,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,aAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,IACvC;AAAA,EACF;AACF;AAEe,SAAR,eAAiB,SAAS;AAC/B,SAAO,IAAI,OAAO,OAAO;AAC3B;;;AC9BO,IAAIC,SAAQ,MAAM,UAAU;AAEpB,SAARC,eAAiBC,IAAG;AACzB,SAAO,OAAOA,OAAM,YAAY,YAAYA,KACxCA,KACA,MAAM,KAAKA,EAAC;AAClB;;;ACNO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAEO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;;;ACAe,SAAR,aAAiBC,IAAGC,IAAG;AAC5B,MAAI,UAAUC,kBAAS,IAAI,GACvB,UAAU,MACV,QAAQ,gBACR,SAAS,MACTC,QAAO,SAAS,IAAI;AAExB,EAAAH,KAAI,OAAOA,OAAM,aAAaA,KAAKA,OAAM,SAAa,IAASE,kBAASF,EAAC;AACzE,EAAAC,KAAI,OAAOA,OAAM,aAAaA,KAAKA,OAAM,SAAa,IAASC,kBAASD,EAAC;AAEzE,WAAS,KAAK,MAAM;AAClB,QAAI,GACA,KAAK,OAAOG,eAAM,IAAI,GAAG,QACzB,GACA,WAAW,OACX;AAEJ,QAAI,WAAW,KAAM,UAAS,MAAM,SAASD,MAAK,CAAC;AAEnD,SAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,UAAI,EAAE,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,OAAO,UAAU;AAC1D,YAAI,WAAW,CAAC,SAAU,QAAO,UAAU;AAAA,YACtC,QAAO,QAAQ;AAAA,MACtB;AACA,UAAI,SAAU,QAAO,MAAM,CAACH,GAAE,GAAG,GAAG,IAAI,GAAG,CAACC,GAAE,GAAG,GAAG,IAAI,CAAC;AAAA,IAC3D;AAEA,QAAI,OAAQ,QAAO,SAAS,MAAM,SAAS,MAAM;AAAA,EACnD;AAEA,OAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAIE,kBAAS,CAAC,CAAC,GAAG,QAAQF;AAAA,EACrF;AAEA,OAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUC,KAAI,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,QAAQD;AAAA,EACrF;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC5F;AAEA,OAAK,QAAQ,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,SAAS,SAAS,MAAM,OAAO,IAAI,QAAQ;AAAA,EAC9F;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,CAAC,GAAG,QAAQ;AAAA,EACxG;AAEA,SAAO;AACT;;;AClDe,SAAR,aAAiB,IAAI,IAAI,IAAI;AAClC,MAAI,KAAK,MACL,UAAUG,kBAAS,IAAI,GACvB,UAAU,MACV,QAAQ,gBACR,SAAS,MACTC,QAAO,SAAS,IAAI;AAExB,OAAK,OAAO,OAAO,aAAa,KAAM,OAAO,SAAa,IAASD,kBAAS,CAAC,EAAE;AAC/E,OAAK,OAAO,OAAO,aAAa,KAAM,OAAO,SAAaA,kBAAS,CAAC,IAAIA,kBAAS,CAAC,EAAE;AACpF,OAAK,OAAO,OAAO,aAAa,KAAM,OAAO,SAAa,IAASA,kBAAS,CAAC,EAAE;AAE/E,WAAS,KAAK,MAAM;AAClB,QAAI,GACA,GACAE,IACA,KAAK,OAAOC,eAAM,IAAI,GAAG,QACzB,GACA,WAAW,OACX,QACA,MAAM,IAAI,MAAM,CAAC,GACjB,MAAM,IAAI,MAAM,CAAC;AAErB,QAAI,WAAW,KAAM,UAAS,MAAM,SAASF,MAAK,CAAC;AAEnD,SAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,UAAI,EAAE,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,OAAO,UAAU;AAC1D,YAAI,WAAW,CAAC,UAAU;AACxB,cAAI;AACJ,iBAAO,UAAU;AACjB,iBAAO,UAAU;AAAA,QACnB,OAAO;AACL,iBAAO,QAAQ;AACf,iBAAO,UAAU;AACjB,eAAKC,KAAI,IAAI,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC3B,mBAAO,MAAM,IAAIA,EAAC,GAAG,IAAIA,EAAC,CAAC;AAAA,UAC7B;AACA,iBAAO,QAAQ;AACf,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AACA,UAAI,UAAU;AACZ,YAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI;AACjD,eAAO,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,MAC3E;AAAA,IACF;AAEA,QAAI,OAAQ,QAAO,SAAS,MAAM,SAAS,MAAM;AAAA,EACnD;AAEA,WAAS,WAAW;AAClB,WAAO,aAAK,EAAE,QAAQ,OAAO,EAAE,MAAM,KAAK,EAAE,QAAQ,OAAO;AAAA,EAC7D;AAEA,OAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAIF,kBAAS,CAAC,CAAC,GAAG,KAAK,MAAM,QAAQ;AAAA,EACjG;AAEA,OAAK,KAAK,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EACtF;AAEA,OAAK,KAAK,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,KAAK,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EACzG;AAEA,OAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,KAAK,MAAM,QAAQ;AAAA,EACjG;AAEA,OAAK,KAAK,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EACtF;AAEA,OAAK,KAAK,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,KAAK,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EACzG;AAEA,OAAK,SACL,KAAK,SAAS,WAAW;AACvB,WAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,EAC9B;AAEA,OAAK,SAAS,WAAW;AACvB,WAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,EAC9B;AAEA,OAAK,SAAS,WAAW;AACvB,WAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,EAC9B;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC5F;AAEA,OAAK,QAAQ,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,SAAS,SAAS,MAAM,OAAO,IAAI,QAAQ;AAAA,EAC9F;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,CAAC,GAAG,QAAQ;AAAA,EACxG;AAEA,SAAO;AACT;;;AC/Ge,SAAR,mBAAiBI,IAAG,GAAG;AAC5B,SAAO,IAAIA,KAAI,KAAK,IAAIA,KAAI,IAAI,KAAKA,KAAI,IAAI;AAC/C;;;ACFe,SAARC,kBAAiB,GAAG;AACzB,SAAO;AACT;;;ACIe,SAAR,cAAmB;AACxB,MAAI,QAAQC,mBACR,aAAa,oBACbC,QAAO,MACP,aAAaC,kBAAS,CAAC,GACvB,WAAWA,kBAASC,IAAG,GACvB,WAAWD,kBAAS,CAAC;AAEzB,WAAS,IAAI,MAAM;AACjB,QAAI,GACA,KAAK,OAAOE,eAAM,IAAI,GAAG,QACzB,GACAC,IACAC,OAAM,GACNC,SAAQ,IAAI,MAAM,CAAC,GACnB,OAAO,IAAI,MAAM,CAAC,GAClB,KAAK,CAAC,WAAW,MAAM,MAAM,SAAS,GACtC,KAAK,KAAK,IAAIJ,MAAK,KAAK,IAAI,CAACA,MAAK,SAAS,MAAM,MAAM,SAAS,IAAI,EAAE,CAAC,GACvE,IACA,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG,SAAS,MAAM,MAAM,SAAS,CAAC,GAC9D,KAAK,KAAK,KAAK,IAAI,KAAK,IACxB;AAEJ,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,WAAK,IAAI,KAAKI,OAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG;AAC3D,QAAAD,QAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,cAAc,KAAM,CAAAC,OAAM,KAAK,SAASC,IAAGC,IAAG;AAAE,aAAO,WAAW,KAAKD,EAAC,GAAG,KAAKC,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,aACjFR,SAAQ,KAAM,CAAAM,OAAM,KAAK,SAASC,IAAGC,IAAG;AAAE,aAAOR,MAAK,KAAKO,EAAC,GAAG,KAAKC,EAAC,CAAC;AAAA,IAAG,CAAC;AAGnF,SAAK,IAAI,GAAGJ,KAAIC,QAAO,KAAK,IAAI,MAAMA,OAAM,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI;AAClE,UAAIC,OAAM,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,IAAIF,KAAI,KAAK,IAAI,KAAK,CAAC,IAAI;AAAA,QACvE,MAAM,KAAK,CAAC;AAAA,QACZ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,SAAS,GAAG;AACtB,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAIH,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EACxF;AAEA,MAAI,aAAa,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,aAAa,GAAGD,QAAO,MAAM,OAAO;AAAA,EACjE;AAEA,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO,UAAU,UAAUA,QAAO,GAAG,aAAa,MAAM,OAAOA;AAAA,EACjE;AAEA,MAAI,aAAa,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC7F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F;AAEA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F;AAEA,SAAO;AACT;;;AC7EO,IAAI,oBAAoB,YAAY,cAAW;AAEtD,SAAS,OAAO,OAAO;AACrB,OAAK,SAAS;AAChB;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAASQ,IAAG,GAAG;AACpB,SAAK,OAAO,MAAM,IAAI,KAAK,IAAIA,EAAC,GAAG,IAAI,CAAC,KAAK,IAAIA,EAAC,CAAC;AAAA,EACrD;AACF;AAEe,SAAR,YAA6B,OAAO;AAEzC,WAASC,QAAO,SAAS;AACvB,WAAO,IAAI,OAAO,MAAM,OAAO,CAAC;AAAA,EAClC;AAEA,EAAAA,QAAO,SAAS;AAEhB,SAAOA;AACT;;;AChCO,SAAS,WAAW,GAAG;AAC5B,MAAIC,KAAI,EAAE;AAEV,IAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;AACxB,IAAE,SAAS,EAAE,GAAG,OAAO,EAAE;AAEzB,IAAE,QAAQ,SAAS,GAAG;AACpB,WAAO,UAAU,SAASA,GAAE,YAAY,CAAC,CAAC,IAAIA,GAAE,EAAE;AAAA,EACpD;AAEA,SAAO;AACT;AAEe,SAAR,qBAAmB;AACxB,SAAO,WAAW,aAAK,EAAE,MAAM,iBAAiB,CAAC;AACnD;;;ACde,SAAR,qBAAmB;AACxB,MAAIC,KAAI,aAAK,EAAE,MAAM,iBAAiB,GAClCC,KAAID,GAAE,OACN,KAAKA,GAAE,QACP,KAAKA,GAAE,QACP,KAAKA,GAAE,QACP,KAAKA,GAAE;AAEX,EAAAA,GAAE,QAAQA,GAAE,GAAG,OAAOA,GAAE;AACxB,EAAAA,GAAE,aAAaA,GAAE,IAAI,OAAOA,GAAE;AAC9B,EAAAA,GAAE,WAAWA,GAAE,IAAI,OAAOA,GAAE;AAC5B,EAAAA,GAAE,SAASA,GAAE,GAAG,OAAOA,GAAE;AACzB,EAAAA,GAAE,cAAcA,GAAE,IAAI,OAAOA,GAAE;AAC/B,EAAAA,GAAE,cAAcA,GAAE,IAAI,OAAOA,GAAE;AAC/B,EAAAA,GAAE,iBAAiB,WAAW;AAAE,WAAO,WAAW,GAAG,CAAC;AAAA,EAAG,GAAG,OAAOA,GAAE;AACrE,EAAAA,GAAE,eAAe,WAAW;AAAE,WAAO,WAAW,GAAG,CAAC;AAAA,EAAG,GAAG,OAAOA,GAAE;AACnE,EAAAA,GAAE,kBAAkB,WAAW;AAAE,WAAO,WAAW,GAAG,CAAC;AAAA,EAAG,GAAG,OAAOA,GAAE;AACtE,EAAAA,GAAE,kBAAkB,WAAW;AAAE,WAAO,WAAW,GAAG,CAAC;AAAA,EAAG,GAAG,OAAOA,GAAE;AAEtE,EAAAA,GAAE,QAAQ,SAAS,GAAG;AACpB,WAAO,UAAU,SAASC,GAAE,YAAY,CAAC,CAAC,IAAIA,GAAE,EAAE;AAAA,EACpD;AAEA,SAAOD;AACT;;;AC5Be,SAAR,oBAAiBE,IAAGC,IAAG;AAC5B,SAAO,EAAEA,KAAI,CAACA,MAAK,KAAK,IAAID,MAAK,KAAK,KAAK,CAAC,GAAGC,KAAI,KAAK,IAAID,EAAC,CAAC;AAChE;;;ACAA,IAAM,OAAN,MAAW;AAAA,EACT,YAAY,SAASE,IAAG;AACtB,SAAK,WAAW;AAChB,SAAK,KAAKA;AAAA,EACZ;AAAA,EACA,YAAY;AACV,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,MAAMA,IAAGC,IAAG;AACV,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS;AACd,YAAI,KAAK,MAAO,MAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,YACpC,MAAK,SAAS,OAAOD,IAAGC,EAAC;AAC9B;AAAA,MACF;AAAA,MACA,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB,SAAS;AACP,YAAI,KAAK,GAAI,MAAK,SAAS,cAAc,KAAK,OAAO,KAAK,MAAMD,MAAK,GAAG,KAAK,KAAK,KAAK,KAAKC,IAAGD,IAAGC,EAAC;AAAA,YAC9F,MAAK,SAAS,cAAc,KAAK,KAAK,KAAK,OAAO,KAAK,MAAMA,MAAK,GAAGD,IAAG,KAAK,KAAKA,IAAGC,EAAC;AAC3F;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAMD,IAAG,KAAK,MAAMC;AAAA,EAC3B;AACF;AAEA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,YAAY;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,EAAC;AAAA,EACX,MAAMD,IAAGC,IAAG;AACV,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,YAAM,KAAK,oBAAY,KAAK,KAAK,KAAK,GAAG;AACzC,YAAM,KAAK,oBAAY,KAAK,KAAK,KAAK,OAAO,KAAK,MAAMA,MAAK,CAAC;AAC9D,YAAM,KAAK,oBAAYD,IAAG,KAAK,GAAG;AAClC,YAAM,KAAK,oBAAYA,IAAGC,EAAC;AAC3B,WAAK,SAAS,OAAO,GAAG,EAAE;AAC1B,WAAK,SAAS,cAAc,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;AAAA,IACjD;AACA,SAAK,MAAMD,IAAG,KAAK,MAAMC;AAAA,EAC3B;AACF;AAEO,SAAS,MAAM,SAAS;AAC7B,SAAO,IAAI,KAAK,SAAS,IAAI;AAC/B;AAEO,SAAS,MAAM,SAAS;AAC7B,SAAO,IAAI,KAAK,SAAS,KAAK;AAChC;AAEO,SAAS,WAAW,SAAS;AAClC,SAAO,IAAI,WAAW,OAAO;AAC/B;;;ACpEA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEO,SAAS,KAAK,OAAO;AAC1B,MAAI,SAAS,YACT,SAAS,YACTC,KAAI,GACJC,KAAI,GACJ,UAAU,MACV,SAAS,MACTC,QAAO,SAASC,KAAI;AAExB,WAASA,QAAO;AACd,QAAI;AACJ,UAAM,OAAOC,OAAM,KAAK,SAAS;AACjC,UAAMC,KAAI,OAAO,MAAM,MAAM,IAAI;AACjC,UAAM,IAAI,OAAO,MAAM,MAAM,IAAI;AACjC,QAAI,WAAW,KAAM,UAAS,MAAM,SAASH,MAAK,CAAC;AACnD,WAAO,UAAU;AACjB,SAAK,CAAC,IAAIG,IAAG,OAAO,MAAM,CAACL,GAAE,MAAM,MAAM,IAAI,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,CAAC;AACpE,SAAK,CAAC,IAAI,GAAG,OAAO,MAAM,CAACD,GAAE,MAAM,MAAM,IAAI,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,CAAC;AACpE,WAAO,QAAQ;AACf,QAAI,OAAQ,QAAO,SAAS,MAAM,SAAS,MAAM;AAAA,EACnD;AAEA,EAAAE,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUH,KAAI,OAAO,MAAM,aAAa,IAAIM,kBAAS,CAAC,CAAC,GAAGH,SAAQH;AAAA,EACrF;AAEA,EAAAG,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUF,KAAI,OAAO,MAAM,aAAa,IAAIK,kBAAS,CAAC,CAAC,GAAGH,SAAQF;AAAA,EACrF;AAEA,EAAAE,MAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,CAAC,GAAGA,SAAQ;AAAA,EACxG;AAEA,SAAOA;AACT;AAEO,SAAS,iBAAiB;AAC/B,SAAO,KAAK,KAAK;AACnB;AAEO,SAAS,eAAe;AAC7B,SAAO,KAAK,KAAK;AACnB;AAEO,SAAS,aAAa;AAC3B,QAAM,IAAI,KAAK,UAAU;AACzB,IAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;AACxB,IAAE,SAAS,EAAE,GAAG,OAAO,EAAE;AACzB,SAAO;AACT;;;ACtEA,IAAM,QAAQI,MAAK,CAAC;AAEpB,IAAO,mBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIA,MAAK,OAAOC,KAAI,OAAO,IAAI,IAAI,CAAC,IAAI;AAC9C,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AACd,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,YAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;AACrB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,EACtB;AACF;;;ACdA,IAAO,iBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,OAAOC,GAAE;AACxB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,IAAI,GAAG,GAAG,GAAG,GAAGC,IAAG;AAAA,EAC7B;AACF;;;ACNA,IAAO,gBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,OAAO,CAAC,IAAI;AAC3B,YAAQ,OAAO,KAAK,GAAG,CAAC,CAAC;AACzB,YAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;AACrB,YAAQ,OAAO,CAAC,GAAG,KAAK,CAAC;AACzB,YAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,YAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,YAAQ,OAAO,IAAI,GAAG,CAAC,CAAC;AACxB,YAAQ,OAAO,IAAI,GAAG,CAAC;AACvB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,YAAQ,OAAO,CAAC,GAAG,IAAI,CAAC;AACxB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,OAAO,KAAK,GAAG,CAAC;AACxB,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACjBA,IAAM,QAAQC,MAAK,IAAI,CAAC;AACxB,IAAM,UAAU,QAAQ;AAExB,IAAO,kBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAMC,KAAID,MAAK,OAAO,OAAO;AAC7B,UAAME,KAAID,KAAI;AACd,YAAQ,OAAO,GAAG,CAACA,EAAC;AACpB,YAAQ,OAAOC,IAAG,CAAC;AACnB,YAAQ,OAAO,GAAGD,EAAC;AACnB,YAAQ,OAAO,CAACC,IAAG,CAAC;AACpB,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACbA,IAAO,mBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,IAAI,IAAI;AACvB,YAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACTA,IAAO,eAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,OAAOC,KAAI,OAAO,GAAG,CAAC,CAAC,IAAI;AAC1C,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,EACtB;AACF;;;ACRA,IAAO,iBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,IAAI;AACnB,UAAMC,KAAI,CAAC,IAAI;AACf,YAAQ,KAAKA,IAAGA,IAAG,GAAG,CAAC;AAAA,EACzB;AACF;;;ACNA,IAAO,kBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,IAAI,IAAI;AACvB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,YAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;AACrB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACTA,IAAM,KAAK;AACX,IAAM,KAAK,IAAIC,MAAK,EAAE,IAAI,IAAI,IAAIA,MAAK,EAAE;AACzC,IAAM,KAAK,IAAIC,OAAM,EAAE,IAAI;AAC3B,IAAM,KAAK,CAAC,IAAIA,OAAM,EAAE,IAAI;AAE5B,IAAO,eAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,OAAO,EAAE;AACxB,UAAMC,KAAI,KAAK;AACf,UAAMC,KAAI,KAAK;AACf,YAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,YAAQ,OAAOD,IAAGC,EAAC;AACnB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAMC,KAAIJ,OAAM,IAAI;AACpB,YAAMK,KAAI,IAAID,EAAC;AACf,YAAME,KAAI,IAAIF,EAAC;AACf,cAAQ,OAAOE,KAAI,GAAG,CAACD,KAAI,CAAC;AAC5B,cAAQ,OAAOA,KAAIH,KAAII,KAAIH,IAAGG,KAAIJ,KAAIG,KAAIF,EAAC;AAAA,IAC7C;AACA,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACrBA,IAAMI,SAAQC,MAAK,CAAC;AAEpB,IAAO,mBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAMC,KAAI,CAACD,MAAK,QAAQD,SAAQ,EAAE;AAClC,YAAQ,OAAO,GAAGE,KAAI,CAAC;AACvB,YAAQ,OAAO,CAACF,SAAQE,IAAG,CAACA,EAAC;AAC7B,YAAQ,OAAOF,SAAQE,IAAG,CAACA,EAAC;AAC5B,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACVA,IAAMC,SAAQC,MAAK,CAAC;AAEpB,IAAO,oBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAMC,KAAID,MAAK,IAAI,IAAI;AACvB,UAAM,IAAIC,KAAK;AACf,UAAM,IAAKA,KAAIF,SAAS;AACxB,YAAQ,OAAO,GAAG,CAACE,EAAC;AACpB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACZA,IAAM,IAAI;AACV,IAAM,IAAIC,MAAK,CAAC,IAAI;AACpB,IAAM,IAAI,IAAIA,MAAK,EAAE;AACrB,IAAM,KAAK,IAAI,IAAI,KAAK;AAExB,IAAO,cAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIA,MAAK,OAAO,CAAC;AACvB,UAAM,KAAK,IAAI,GAAG,KAAK,IAAI;AAC3B,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI;AAC5B,UAAM,KAAK,CAAC,IAAI,KAAK;AACrB,YAAQ,OAAO,IAAI,EAAE;AACrB,YAAQ,OAAO,IAAI,EAAE;AACrB,YAAQ,OAAO,IAAI,EAAE;AACrB,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,YAAQ,UAAU;AAAA,EACpB;AACF;;;ACtBA,IAAO,gBAAQ;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,UAAM,IAAIC,MAAK,OAAOC,KAAI,OAAO,GAAG,GAAG,CAAC,IAAI;AAC5C,YAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;AACrB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,YAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,EACtB;AACF;;;ACOO,IAAM,cAAc;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEe,SAARC,QAAwB,MAAM,MAAM;AACzC,MAAI,UAAU,MACVC,QAAO,SAAS,MAAM;AAE1B,SAAO,OAAO,SAAS,aAAa,OAAOC,kBAAS,QAAQ,cAAM;AAClE,SAAO,OAAO,SAAS,aAAa,OAAOA,kBAAS,SAAS,SAAY,KAAK,CAAC,IAAI;AAEnF,WAAS,SAAS;AAChB,QAAI;AACJ,QAAI,CAAC,QAAS,WAAU,SAASD,MAAK;AACtC,SAAK,MAAM,MAAM,SAAS,EAAE,KAAK,SAAS,CAAC,KAAK,MAAM,MAAM,SAAS,CAAC;AACtE,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AAEA,SAAO,OAAO,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAIC,kBAAS,CAAC,GAAG,UAAU;AAAA,EACzF;AAEA,SAAO,OAAO,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,UAAU;AAAA,EAC1F;AAEA,SAAO,UAAU,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,UAAU,KAAK,OAAO,OAAO,GAAG,UAAU;AAAA,EACvE;AAEA,SAAO;AACT;;;ACjEO,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS;AAAA,KACX,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,KAC3B,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK;AAAA,KAC/B,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK;AAAA,EAClC;AACF;AAEO,SAAS,MAAM,SAAS;AAC7B,OAAK,WAAW;AAClB;AAEA,MAAM,YAAY;AAAA,EAChB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,QAAAF,OAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MACtC,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IACpD;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA;AAAA,MAC1G;AAAS,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;AAEe,SAARC,eAAiB,SAAS;AAC/B,SAAO,IAAI,MAAM,OAAO;AAC1B;;;AClDe,SAAR,eAAmB;AAAC;;;ACG3B,SAAS,YAAY,SAAS;AAC5B,OAAK,WAAW;AAClB;AAEA,YAAY,YAAY;AAAA,EACtB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MACjD,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACvD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK,CAAC;AAAG;AAAA,MACjJ;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;AAEe,SAARE,qBAAiB,SAAS;AAC/B,SAAO,IAAI,YAAY,OAAO;AAChC;;;ACjDA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,YAAI,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,GAAG,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS,OAAO,IAAI,EAAE;AAAG;AAAA,MACvL,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;AAEe,SAAR,kBAAiB,SAAS;AAC/B,SAAO,IAAI,UAAU,OAAO;AAC9B;;;ACpCA,SAAS,OAAO,SAAS,MAAM;AAC7B,OAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,OAAK,QAAQ;AACf;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AACX,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,QAAIE,KAAI,KAAK,IACTC,KAAI,KAAK,IACT,IAAID,GAAE,SAAS;AAEnB,QAAI,IAAI,GAAG;AACT,UAAI,KAAKA,GAAE,CAAC,GACR,KAAKC,GAAE,CAAC,GACR,KAAKD,GAAE,CAAC,IAAI,IACZ,KAAKC,GAAE,CAAC,IAAI,IACZ,IAAI,IACJ;AAEJ,aAAO,EAAE,KAAK,GAAG;AACf,YAAI,IAAI;AACR,aAAK,OAAO;AAAA,UACV,KAAK,QAAQD,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,UACjD,KAAK,QAAQC,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAASD,IAAGC,IAAG;AACpB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AAEA,IAAO,iBAAS,SAAS,OAAO,MAAM;AAEpC,WAAS,OAAO,SAAS;AACvB,WAAO,SAAS,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,EACnE;AAEA,SAAO,OAAO,SAASC,OAAM;AAC3B,WAAO,OAAO,CAACA,KAAI;AAAA,EACrB;AAEA,SAAO;AACT,EAAG,IAAI;;;ACvDA,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS;AAAA,IACZ,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,IACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,IACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMD;AAAA,IACjC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMC;AAAA,IACjC,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AAEO,SAAS,SAAS,SAAS,SAAS;AACzC,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,SAAS,YAAY;AAAA,EACnB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,QAAAF,OAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IAC3C;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,mBAAS,SAASC,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,SAAS,SAAS,OAAO;AAAA,EACtC;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;ACzDG,SAAS,eAAe,SAAS,SAAS;AAC/C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,eAAe,YAAY;AAAA,EACzB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAAG;AAAA,MAC3E,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,yBAAS,SAASE,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,eAAe,SAAS,OAAO;AAAA,EAC5C;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;AC1DG,SAAS,aAAa,SAAS,SAAS;AAC7C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,aAAa,YAAY;AAAA,EACvB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAC3H,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,uBAAS,SAASE,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,aAAa,SAAS,OAAO;AAAA,EAC1C;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;AC7CG,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACVC,MAAK,KAAK,KACVC,MAAK,KAAK;AAEd,MAAI,KAAK,SAASC,UAAS;AACzB,QAAIC,KAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AACpE,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AAAA,EACtE;AAEA,MAAI,KAAK,SAASD,UAAS;AACzB,QAAI,IAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,IAAAF,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAC7D,IAAAG,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAAA,EAC/D;AAEA,OAAK,SAAS,cAAc,IAAI,IAAIC,KAAIC,KAAI,KAAK,KAAK,KAAK,GAAG;AAChE;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,WAAW,YAAY;AAAA,EACrB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IAC1C;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASH,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,qBAAS,SAASK,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,WAAW,SAAS,KAAK,IAAI,IAAI,SAAS,SAAS,CAAC;AAAA,EACzE;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;ACnFN,SAAS,iBAAiB,SAAS,OAAO;AACxC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,iBAAiB,YAAY;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAAG;AAAA,MAC3E,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,2BAAS,SAASE,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,eAAe,SAAS,CAAC;AAAA,EACrF;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;ACtEN,SAAS,eAAe,SAAS,OAAO;AACtC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,eAAe,YAAY;AAAA,EACzB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAC3H,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,yBAAS,SAASE,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,eAAe,SAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,EACjF;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;AC3DN,SAAS,aAAa,SAAS;AAC7B,OAAK,WAAW;AAClB;AAEA,aAAa,YAAY;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,OAAQ,MAAK,SAAS,UAAU;AAAA,EAC3C;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,OAAQ,MAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QACrC,MAAK,SAAS,GAAG,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,EACjD;AACF;AAEe,SAAR,qBAAiB,SAAS;AAC/B,SAAO,IAAI,aAAa,OAAO;AACjC;;;ACxBA,SAAS,KAAKC,IAAG;AACf,SAAOA,KAAI,IAAI,KAAK;AACtB;AAMA,SAAS,OAAO,MAAM,IAAI,IAAI;AAC5B,MAAI,KAAK,KAAK,MAAM,KAAK,KACrB,KAAK,KAAK,KAAK,KACf,MAAM,KAAK,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAC9C,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,KACxC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK;AACpC,UAAQ,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;AAC5F;AAGA,SAAS,OAAO,MAAM,GAAG;AACvB,MAAI,IAAI,KAAK,MAAM,KAAK;AACxB,SAAO,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI;AACvD;AAKA,SAASC,OAAM,MAAMC,KAAIC,KAAI;AAC3B,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,MAAM;AACrB,OAAK,SAAS,cAAc,KAAK,IAAI,KAAK,KAAKD,KAAI,KAAK,IAAI,KAAK,KAAKC,KAAI,IAAI,EAAE;AAClF;AAEA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAChB,KAAK,MAAM;AACX,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,QAAAF,OAAM,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG,CAAC;AAAG;AAAA,IACzD;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASD,IAAGI,IAAG;AACpB,QAAID,MAAK;AAET,IAAAH,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,QAAIJ,OAAM,KAAK,OAAOI,OAAM,KAAK,IAAK;AACtC,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,QAAAH,OAAM,MAAM,OAAO,MAAME,MAAK,OAAO,MAAMH,IAAGI,EAAC,CAAC,GAAGD,GAAE;AAAG;AAAA,MACjF;AAAS,QAAAF,OAAM,MAAM,KAAK,KAAKE,MAAK,OAAO,MAAMH,IAAGI,EAAC,CAAC;AAAG;AAAA,IAC3D;AAEA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAChC,SAAK,MAAMD;AAAA,EACb;AACF;AAEA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW,IAAI,eAAe,OAAO;AAC5C;AAAA,CAEC,UAAU,YAAY,OAAO,OAAO,UAAU,SAAS,GAAG,QAAQ,SAASH,IAAGI,IAAG;AAChF,YAAU,UAAU,MAAM,KAAK,MAAMA,IAAGJ,EAAC;AAC3C;AAEA,SAAS,eAAe,SAAS;AAC/B,OAAK,WAAW;AAClB;AAEA,eAAe,YAAY;AAAA,EACzB,QAAQ,SAASA,IAAGI,IAAG;AAAE,SAAK,SAAS,OAAOA,IAAGJ,EAAC;AAAA,EAAG;AAAA,EACrD,WAAW,WAAW;AAAE,SAAK,SAAS,UAAU;AAAA,EAAG;AAAA,EACnD,QAAQ,SAASA,IAAGI,IAAG;AAAE,SAAK,SAAS,OAAOA,IAAGJ,EAAC;AAAA,EAAG;AAAA,EACrD,eAAe,SAAS,IAAI,IAAI,IAAI,IAAIA,IAAGI,IAAG;AAAE,SAAK,SAAS,cAAc,IAAI,IAAI,IAAI,IAAIA,IAAGJ,EAAC;AAAA,EAAG;AACrG;AAEO,SAAS,UAAU,SAAS;AACjC,SAAO,IAAI,UAAU,OAAO;AAC9B;AAEO,SAAS,UAAU,SAAS;AACjC,SAAO,IAAI,UAAU,OAAO;AAC9B;;;ACvGA,SAAS,QAAQ,SAAS;AACxB,OAAK,WAAW;AAClB;AAEA,QAAQ,YAAY;AAAA,EAClB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AAAA,EACb;AAAA,EACA,SAAS,WAAW;AAClB,QAAIK,KAAI,KAAK,IACTC,KAAI,KAAK,IACT,IAAID,GAAE;AAEV,QAAI,GAAG;AACL,WAAK,QAAQ,KAAK,SAAS,OAAOA,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC,IAAI,KAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAC/E,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAAA,MACjC,OAAO;AACL,YAAI,KAAK,cAAcD,EAAC,GACpB,KAAK,cAAcC,EAAC;AACxB,iBAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI;AAC3C,eAAK,SAAS,cAAc,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAGD,GAAE,EAAE,GAAGC,GAAE,EAAE,CAAC;AAAA,QACtF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,MAAM,EAAI,MAAK,SAAS,UAAU;AACzE,SAAK,QAAQ,IAAI,KAAK;AACtB,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB;AAAA,EACA,OAAO,SAASD,IAAGC,IAAG;AACpB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AAGA,SAAS,cAAcD,IAAG;AACxB,MAAI,GACA,IAAIA,GAAE,SAAS,GACf,GACAE,KAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC;AACnB,EAAAA,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,CAAC;AACzC,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,CAAAE,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,IAAI,CAAC;AAC7E,EAAAE,GAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIA,GAAE,CAAC;AACzD,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,KAAIE,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC;AAC3E,EAAAA,GAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC7B,OAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,CAAAA,GAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,GAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3D,IAAE,IAAI,CAAC,KAAKF,GAAE,CAAC,IAAIE,GAAE,IAAI,CAAC,KAAK;AAC/B,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIE,GAAE,IAAI,CAAC;AACzD,SAAO,CAACA,IAAG,CAAC;AACd;AAEe,SAAR,gBAAiB,SAAS;AAC/B,SAAO,IAAI,QAAQ,OAAO;AAC5B;;;AChEA,SAAS,KAAK,SAAS,GAAG;AACxB,OAAK,WAAW;AAChB,OAAK,KAAK;AACZ;AAEA,KAAK,YAAY;AAAA,EACf,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,OAAO,KAAK,IAAI,KAAK,EAAE;AAC1F,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,QAAI,KAAK,SAAS,EAAG,MAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,EACpE;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB,SAAS;AACP,YAAI,KAAK,MAAM,GAAG;AAChB,eAAK,SAAS,OAAO,KAAK,IAAIA,EAAC;AAC/B,eAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QAC3B,OAAO;AACL,cAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAMD,KAAI,KAAK;AAC5C,eAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAChC,eAAK,SAAS,OAAO,IAAIC,EAAC;AAAA,QAC5B;AACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,KAAKD,IAAG,KAAK,KAAKC;AAAA,EACzB;AACF;AAEe,SAAR,aAAiB,SAAS;AAC/B,SAAO,IAAI,KAAK,SAAS,GAAG;AAC9B;AAEO,SAAS,WAAW,SAAS;AAClC,SAAO,IAAI,KAAK,SAAS,CAAC;AAC5B;AAEO,SAAS,UAAU,SAAS;AACjC,SAAO,IAAI,KAAK,SAAS,CAAC;AAC5B;;;ACpDe,SAAR,aAAiB,QAAQ,OAAO;AACrC,MAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,WAAS,IAAI,GAAG,GAAG,IAAI,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC1E,SAAK,IAAI,KAAK,OAAO,MAAM,CAAC,CAAC;AAC7B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,SAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;;;ACRe,SAARC,cAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO,QAAQ,IAAI,IAAI,MAAM,CAAC;AACtC,SAAO,EAAE,KAAK,EAAG,GAAE,CAAC,IAAI;AACxB,SAAO;AACT;;;ACCA,SAAS,WAAW,GAAG,KAAK;AAC1B,SAAO,EAAE,GAAG;AACd;AAEA,SAAS,YAAY,KAAK;AACxB,QAAM,SAAS,CAAC;AAChB,SAAO,MAAM;AACb,SAAO;AACT;AAEe,SAAR,gBAAmB;AACxB,MAAI,OAAOC,kBAAS,CAAC,CAAC,GAClB,QAAQC,eACR,SAAS,cACT,QAAQ;AAEZ,WAAS,MAAM,MAAM;AACnB,QAAI,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,SAAS,GAAG,WAAW,GACxD,GAAG,IAAI,GAAG,QAAQ,IAAI,IACtB;AAEJ,eAAW,KAAK,MAAM;AACpB,WAAK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3B,SAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,OAAO;AAAA,MACzD;AAAA,IACF;AAEA,SAAK,IAAI,GAAG,KAAKC,eAAM,MAAM,EAAE,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AAC7C,SAAG,GAAG,CAAC,CAAC,EAAE,QAAQ;AAAA,IACpB;AAEA,WAAO,IAAI,EAAE;AACb,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS,GAAG;AACvB,WAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAIF,kBAAS,MAAM,KAAK,CAAC,CAAC,GAAG,SAAS;AAAA,EACpG;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,GAAG,SAAS;AAAA,EAC1F;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,KAAK,OAAOC,gBAAY,OAAO,MAAM,aAAa,IAAID,kBAAS,MAAM,KAAK,CAAC,CAAC,GAAG,SAAS;AAAA,EAC7H;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,KAAK,OAAO,eAAa,GAAG,SAAS;AAAA,EAC3E;AAEA,SAAO;AACT;;;ACvDe,SAAR,eAAiB,QAAQ,OAAO;AACrC,MAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,WAAS,GAAG,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQG,IAAG,IAAI,GAAG,EAAE,GAAG;AACzD,SAAKA,KAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,MAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AACpD,QAAIA,GAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAKA;AAAA,EACpD;AACA,eAAK,QAAQ,KAAK;AACpB;;;ACTe,SAAR,kBAAiB,QAAQ,OAAO;AACrC,MAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,WAAS,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,EAAE,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5E,SAAK,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnC,WAAK,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG;AAClD,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,MAC1B,WAAW,KAAK,GAAG;AACjB,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,MAC1B,OAAO;AACL,UAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;;;ACXe,SAAR,mBAAiB,QAAQ,OAAO;AACrC,MAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,WAAS,IAAI,GAAG,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACnE,aAAS,IAAI,GAAGC,KAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,MAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC3D,OAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAACA,KAAI;AAAA,EAC9B;AACA,eAAK,QAAQ,KAAK;AACpB;;;ACPe,SAAR,eAAiB,QAAQ,OAAO;AACrC,MAAI,GAAG,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,GAAI;AAC/E,WAASC,KAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1C,UAAI,KAAK,OAAO,MAAM,CAAC,CAAC,GACpB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK,GACnB,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GACvB,MAAM,OAAO,QAAQ;AACzB,eAASC,KAAI,GAAGA,KAAI,GAAG,EAAEA,IAAG;AAC1B,YAAI,KAAK,OAAO,MAAMA,EAAC,CAAC,GACpB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK,GACnB,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK;AAC3B,cAAM,OAAO;AAAA,MACf;AACA,YAAM,MAAM,MAAM,KAAK;AAAA,IACzB;AACA,OAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAID;AAC/B,QAAI,GAAI,CAAAA,MAAK,KAAK;AAAA,EACpB;AACA,KAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAIA;AAC/B,eAAK,QAAQ,KAAK;AACpB;;;ACrBe,SAAR,mBAAiB,QAAQ;AAC9B,MAAI,QAAQ,OAAO,IAAI,IAAI;AAC3B,SAAOE,cAAK,MAAM,EAAE,KAAK,SAASC,IAAG,GAAG;AAAE,WAAO,MAAMA,EAAC,IAAI,MAAM,CAAC;AAAA,EAAG,CAAC;AACzE;AAEA,SAAS,KAAK,QAAQ;AACpB,MAAI,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,KAAK;AAC/C,SAAO,EAAE,IAAI,EAAG,MAAK,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,GAAI,MAAK,IAAI,IAAI;AAC5D,SAAO;AACT;;;ACTe,SAAR,kBAAiB,QAAQ;AAC9B,MAAI,OAAO,OAAO,IAAIC,IAAG;AACzB,SAAOC,cAAK,MAAM,EAAE,KAAK,SAASC,IAAG,GAAG;AAAE,WAAO,KAAKA,EAAC,IAAI,KAAK,CAAC;AAAA,EAAG,CAAC;AACvE;AAEO,SAASF,KAAI,QAAQ;AAC1B,MAAIG,KAAI,GAAG,IAAI,IAAI,IAAI,OAAO,QAAQ;AACtC,SAAO,EAAE,IAAI,EAAG,KAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAG,CAAAA,MAAK;AAC5C,SAAOA;AACT;;;ACTe,SAARC,oBAAiB,QAAQ;AAC9B,SAAO,kBAAU,MAAM,EAAE,QAAQ;AACnC;;;ACDe,SAAR,kBAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO,QACX,GACA,GACA,OAAO,OAAO,IAAIC,IAAG,GACrB,QAAQ,mBAAW,MAAM,GACzB,MAAM,GACN,SAAS,GACT,OAAO,CAAC,GACR,UAAU,CAAC;AAEf,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,QAAI,MAAM,CAAC;AACX,QAAI,MAAM,QAAQ;AAChB,aAAO,KAAK,CAAC;AACb,WAAK,KAAK,CAAC;AAAA,IACb,OAAO;AACL,gBAAU,KAAK,CAAC;AAChB,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,QAAQ,QAAQ,EAAE,OAAO,IAAI;AACtC;;;ACxBe,SAAR,gBAAiB,QAAQ;AAC9B,SAAOC,cAAK,MAAM,EAAE,QAAQ;AAC9B;", "names": ["a", "a", "x", "a", "x", "index", "length", "blur", "y", "x", "sum", "s", "count", "index", "array", "length", "reduce", "index", "j", "i", "sum", "index", "count", "mean", "sum", "index", "min", "max", "index", "x", "y", "index", "key", "x", "groups", "reduce", "map", "values", "keyof", "index", "group", "index", "f", "c", "a", "x", "reduce", "key", "count", "reverse", "ticks", "count", "x", "threshold", "x", "max", "step", "a", "bin", "max", "index", "max", "maxIndex", "index", "min", "index", "min", "minIndex", "index", "array", "k", "s", "max", "numbers", "i", "index", "j", "min", "max", "c", "min", "max", "c", "count", "sum", "index", "flatten", "array", "index", "count", "pairs", "a", "range", "k", "c", "min", "min", "index", "max", "index", "index", "array", "sum", "index", "length", "transpose", "index", "index", "array", "index", "map", "index", "reducer", "index", "set", "set", "set", "format", "a", "k", "s", "min", "max", "y", "x", "a", "k", "c", "k", "s", "a", "cosh", "sinh", "t1", "t2", "t3", "x", "a", "y", "y", "color", "rgb", "a", "c", "x", "a", "a", "a", "c", "k", "zero", "a", "s", "i", "a", "c", "a", "x", "c", "range", "a", "x", "a", "degrees", "identity", "a", "c", "identity", "s", "a", "x", "s", "hsl", "hue", "s", "lab", "a", "hcl", "hue", "c", "cubehelix", "hue", "y", "s", "i", "k", "x", "y", "x", "x", "length", "s", "x", "x", "x", "x", "map", "locale", "group", "sign", "zero", "format", "c", "length", "formatPrefix", "k", "value", "max", "range", "index", "range", "reverse", "copy", "count", "x", "number", "x", "identity", "x", "a", "range", "i", "piecewise", "y", "number", "count", "linear", "identity", "x", "number", "nice", "x", "k", "count", "nice", "c", "x", "x", "identity", "x", "range", "y", "number", "quantile", "range", "x", "y", "range", "x", "y", "range", "x", "y", "t0", "t1", "count", "date", "range", "date", "k", "date", "date", "date", "date", "date", "date", "date", "k", "ticks", "count", "reverse", "step", "date", "y", "locale", "formats", "c", "pad", "format", "sign", "length", "s", "locale", "defaultLocale", "date", "date", "number", "ticks", "second", "format", "formatYear", "tickFormat", "date", "y", "count", "nice", "transformer", "t0", "t1", "identity", "x", "range", "copy", "identity", "x", "transformer", "s", "t0", "t1", "t2", "identity", "x", "range", "copy", "constant_default", "x", "constant", "max", "min", "sqrt", "epsilon", "pi", "tau", "x", "epsilon", "sqrt", "D", "max", "constant_default", "path", "tau", "min", "t0", "t1", "pi", "a", "x", "y", "slice", "array_default", "x", "x", "y", "constant_default", "path", "array_default", "constant_default", "path", "k", "array_default", "a", "identity_default", "identity_default", "sort", "constant_default", "tau", "array_default", "k", "sum", "index", "i", "j", "a", "radial", "c", "a", "c", "x", "y", "x", "y", "x", "y", "path", "link", "slice", "s", "constant_default", "sqrt", "min", "sqrt", "pi", "tau", "sqrt", "sqrt", "y", "x", "sqrt", "sqrt", "min", "sqrt", "x", "sqrt", "pi", "tau", "sqrt", "x", "y", "a", "c", "s", "sqrt3", "sqrt", "y", "sqrt3", "sqrt", "s", "sqrt", "sqrt", "min", "Symbol", "path", "constant_default", "point", "x", "y", "basis_default", "x", "y", "point", "basisClosed_default", "x", "y", "point", "x", "y", "beta", "point", "x", "y", "custom", "tension", "x", "y", "point", "custom", "tension", "x", "y", "point", "custom", "tension", "point", "x", "y", "x2", "y2", "epsilon", "a", "custom", "alpha", "x", "y", "point", "custom", "alpha", "x", "y", "point", "custom", "alpha", "x", "y", "x", "point", "t0", "t1", "y", "x", "y", "a", "x", "y", "none_default", "constant_default", "none_default", "array_default", "y", "y", "y", "k", "none_default", "a", "sum", "none_default", "a", "s", "descending_default", "sum", "none_default"]}