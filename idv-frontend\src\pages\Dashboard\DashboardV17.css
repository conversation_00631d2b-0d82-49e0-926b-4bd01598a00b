/* Dashboard v1.7 - DRDO Official Styling */
.dashboard-v17 {
  padding: 0;
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
  color: #1A3C5E;
}

/* Dashboard Header */
.dashboard-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 24px 32px;
  box-shadow: 0 4px 12px rgba(26, 60, 94, 0.15);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left .dashboard-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  color: white;
}

.header-left .dashboard-subtitle {
  font-size: 14px;
  margin: 4px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.status-indicators {
  display: flex;
  gap: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.status-label {
  font-size: 11px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.status-value {
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.status-value.operational {
  background: #059669;
  color: white;
}

.status-value.secure {
  background: #FF9933;
  color: white;
}

.control-toggle {
  padding: 8px 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.control-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.control-toggle.active {
  background: #059669;
  border-color: #059669;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #64748B;
}

.status-dot.connected {
  background: #059669;
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background: #DC2626;
}

.status-dot.connecting {
  background: #F59E0B;
  animation: pulse 1s infinite;
}

/* Mission Status Section */
.mission-status-section {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1A3C5E;
  margin: 0;
}

.mission-count {
  background: #F8FAFC;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
  border: 1px solid #E2E8F0;
}

.mission-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.mission-card {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
  transition: all 0.3s ease;
}

.mission-card:hover {
  box-shadow: 0 8px 24px rgba(26, 60, 94, 0.15);
  transform: translateY(-2px);
}

.mission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.mission-name {
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 4px 0;
}

.mission-id {
  font-size: 12px;
  color: #64748B;
  font-weight: 500;
}

.mission-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.mission-status.active {
  background: #DCFCE7;
  color: #059669;
}

.mission-status.standby {
  background: #FEF3C7;
  color: #F59E0B;
}

.mission-status.planning {
  background: #DBEAFE;
  color: #3B82F6;
}

.mission-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  font-size: 14px;
  color: #64748B;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

.priority.high {
  color: #DC2626;
  font-weight: 600;
}

.priority.medium {
  color: #F59E0B;
  font-weight: 600;
}

.priority.low {
  color: #059669;
  font-weight: 600;
}

.mission-progress {
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

.progress-bar {
  height: 8px;
  background: #F1F5F9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2, #FF9933);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.mission-equipment {
  margin-bottom: 16px;
}

.equipment-label {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 8px;
  display: block;
}

.equipment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.equipment-tag {
  background: #F8FAFC;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: #1A3C5E;
  border: 1px solid #E2E8F0;
}

.mission-footer {
  border-top: 1px solid #F1F5F9;
  padding-top: 12px;
}

.last-update {
  font-size: 12px;
  color: #64748B;
}

/* Equipment Heatmap Section */
.equipment-heatmap-section {
  padding: 32px;
  background: #F8FAFC;
  max-width: 1400px;
  margin: 0 auto;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.equipment-tile {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.equipment-tile:hover {
  box-shadow: 0 4px 12px rgba(26, 60, 94, 0.1);
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.equipment-name {
  font-size: 16px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.operational {
  background: #DCFCE7;
  color: #059669;
}

.status-badge.maintenance {
  background: #FEF3C7;
  color: #F59E0B;
}

.status-badge.warning {
  background: #FEE2E2;
  color: #DC2626;
}

.equipment-metrics {
  margin-bottom: 12px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 12px;
  color: #64748B;
  min-width: 40px;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: #F1F5F9;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill.health {
  height: 100%;
  background: linear-gradient(90deg, #059669, #34D399);
  border-radius: 3px;
}

.metric-value {
  font-size: 12px;
  font-weight: 500;
  color: #1A3C5E;
  min-width: 40px;
  text-align: right;
}

.equipment-location {
  font-size: 12px;
  color: #64748B;
}

/* India Map Section */
.india-map-section {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.map-container {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
}

.india-map-placeholder {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-info h3 {
  font-size: 20px;
  color: #1A3C5E;
  margin-bottom: 8px;
}

.map-info p {
  color: #64748B;
  margin-bottom: 24px;
}

.lab-locations {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  text-align: left;
}

.location-item {
  padding: 8px 12px;
  background: #F8FAFC;
  border-radius: 6px;
  font-size: 14px;
  color: #1A3C5E;
}

/* Dashboard Footer */
.dashboard-footer {
  background: #1A3C5E;
  color: white;
  padding: 16px 32px;
  margin-top: 32px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 12px;
  }
  
  .mission-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .heatmap-grid {
    grid-template-columns: 1fr;
  }
  
  .lab-locations {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
