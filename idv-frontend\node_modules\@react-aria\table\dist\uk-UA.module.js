var $c7ab180b401e49ff$exports = {};
$c7ab180b401e49ff$exports = {
    "ascending": `\u{432}\u{438}\u{441}\u{445}\u{456}\u{434}\u{43D}\u{438}\u{439}`,
    "ascendingSort": (args)=>`\u{432}\u{456}\u{434}\u{441}\u{43E}\u{440}\u{442}\u{43E}\u{432}\u{430}\u{43D}\u{43E} \u{437}\u{430} \u{441}\u{442}\u{43E}\u{432}\u{43F}\u{446}\u{435}\u{43C} ${args.columnName} \u{443} \u{432}\u{438}\u{441}\u{445}\u{456}\u{434}\u{43D}\u{43E}\u{43C}\u{443} \u{43F}\u{43E}\u{440}\u{44F}\u{434}\u{43A}\u{443}`,
    "columnSize": (args)=>`${args.value} \u{43F}\u{456}\u{43A}\u{441}.`,
    "descending": `\u{43D}\u{438}\u{437}\u{445}\u{456}\u{434}\u{43D}\u{438}\u{439}`,
    "descendingSort": (args)=>`\u{432}\u{456}\u{434}\u{441}\u{43E}\u{440}\u{442}\u{43E}\u{432}\u{430}\u{43D}\u{43E} \u{437}\u{430} \u{441}\u{442}\u{43E}\u{432}\u{43F}\u{446}\u{435}\u{43C} ${args.columnName} \u{443} \u{43D}\u{438}\u{437}\u{445}\u{456}\u{434}\u{43D}\u{43E}\u{43C}\u{443} \u{43F}\u{43E}\u{440}\u{44F}\u{434}\u{43A}\u{443}`,
    "resizerDescription": `\u{41D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{456}\u{442}\u{44C} Enter, \u{449}\u{43E}\u{431} \u{43F}\u{43E}\u{447}\u{430}\u{442}\u{438} \u{437}\u{43C}\u{456}\u{43D}\u{443} \u{440}\u{43E}\u{437}\u{43C}\u{456}\u{440}\u{443}`,
    "select": `\u{412}\u{438}\u{431}\u{440}\u{430}\u{442}\u{438}`,
    "selectAll": `\u{412}\u{438}\u{431}\u{440}\u{430}\u{442}\u{438} \u{432}\u{441}\u{435}`,
    "sortable": `\u{441}\u{43E}\u{440}\u{442}\u{443}\u{432}\u{430}\u{43B}\u{44C}\u{43D}\u{438}\u{439} \u{441}\u{442}\u{43E}\u{432}\u{43F}\u{435}\u{446}\u{44C}`
};


export {$c7ab180b401e49ff$exports as default};
//# sourceMappingURL=uk-UA.module.js.map
