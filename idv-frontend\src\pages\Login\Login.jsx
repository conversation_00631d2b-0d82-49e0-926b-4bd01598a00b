import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { useNavigate } from 'react-router-dom'
import './Login.css'

const Login = () => {
  const [credentials, setCredentials] = useState({ email: '', password: '' })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { login, isAuthenticated, isDevelopmentMode, autoLogin } = useAuth()
  const navigate = useNavigate()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard')
    }
  }, [isAuthenticated, navigate])

  // Auto-login in development mode
  useEffect(() => {
    if (isDevelopmentMode) {
      autoLogin('scientist')
    }
  }, [isDevelopmentMode, autoLogin])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const success = await login(credentials)
      if (success) {
        navigate('/dashboard')
      } else {
        setError('Invalid credentials. Use any email with password: drdo123')
      }
    } catch (err) {
      setError('Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickLogin = (role) => {
    autoLogin(role)
  }

  if (isDevelopmentMode) {
    return (
      <div className="login-dev-container">
        <div className="login-dev-card">
          <div className="drdo-logo-large">🇮🇳</div>
          <h1>DRDO IDX v1.7</h1>
          <h2>Development Mode</h2>
          <p>Auto-login is enabled. Redirecting to dashboard...</p>
          <div className="dev-login-options">
            <h3>Quick Login Options:</h3>
            <div className="role-buttons">
              <button onClick={() => handleQuickLogin('admin')} className="role-btn admin">
                👨‍💼 Admin
              </button>
              <button onClick={() => handleQuickLogin('scientist')} className="role-btn scientist">
                👩‍🔬 Scientist
              </button>
              <button onClick={() => handleQuickLogin('engineer')} className="role-btn engineer">
                👨‍🔧 Engineer
              </button>
              <button onClick={() => handleQuickLogin('commander')} className="role-btn commander">
                🎖️ Commander
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="drdo-logo">🇮🇳</div>
          <h1>DRDO IDX</h1>
          <h2>Interactive Dashboard System</h2>
          <p>Defence Research and Development Organisation</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              value={credentials.email}
              onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
              placeholder="Enter your DRDO email"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={credentials.password}
              onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
              placeholder="Enter your password"
              required
            />
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <button type="submit" className="login-btn" disabled={isLoading}>
            {isLoading ? 'Signing In...' : 'Sign In'}
          </button>
        </form>

        <div className="login-help">
          <h3>Demo Credentials:</h3>
          <div className="demo-accounts">
            <div className="demo-account">
              <strong>Admin:</strong> <EMAIL> / drdo123
            </div>
            <div className="demo-account">
              <strong>Scientist:</strong> <EMAIL> / drdo123
            </div>
            <div className="demo-account">
              <strong>Engineer:</strong> <EMAIL> / drdo123
            </div>
            <div className="demo-account">
              <strong>Commander:</strong> <EMAIL> / drdo123
            </div>
          </div>
        </div>

        <div className="login-footer">
          <p>© 2025 Defence Research and Development Organisation</p>
          <p>Secure Access Portal v1.7</p>
        </div>
      </div>
    </div>
  )
}

export default Login
