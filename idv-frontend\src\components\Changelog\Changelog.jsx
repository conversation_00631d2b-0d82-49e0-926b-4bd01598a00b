import React, { useState } from 'react'
import './Changelog.css'

const Changelog = () => {
  const [isOpen, setIsOpen] = useState(false)

  const toggleChangelog = () => {
    setIsOpen(!isOpen)
  }

  const changelogData = [
    {
      version: "1.5",
      date: "July 2025",
      title: "DRDO Formal Theme & Security Integration - Stage 1 Complete",
      changes: [
        "🎨 DRDO Formal Theme: Clean white layout inspired by drdo.gov.in with official navy blue and orange colors",
        "🔐 Security Layer: AES-256 frontend encryption, role-based access control (RBAC), and audit logging system",
        "🌐 Navigation Overhaul: Modern DRDO-style navigation bar with search, language selector, and user controls",
        "👤 Authentication System: Secure login with demo accounts (<PERSON><PERSON>, Scientist, Engineer) and session management",
        "🎚️ Theme Manager: Global theme selector with Formal White, Minimal, Dark, and Dracula themes",
        "🌍 Multi-language Support: Hindi, Tamil, Telugu language toggle with fallback to English",
        "📊 Enhanced Charts: Preparation for Sankey, Heatmap, Radar, Tree, and Bubble chart components",
        "🔍 Advanced Search: Global search functionality with audit trail and security logging",
        "⚙️ Settings Module: Comprehensive settings page for theme, language, animations, and user preferences",
        "🛡️ Audit System: Encrypted audit logs with downloadable reports and security violation tracking",
        "📱 Responsive Navigation: Mobile-optimized navigation with collapsible menus and touch interactions",
        "🔒 Security Classification: Multi-level data classification (PUBLIC, RESTRICTED, CONFIDENTIAL, SECRET)"
      ]
    },
    {
      version: "1.2",
      date: "July 2025",
      title: "Advanced Data Visualization & Input System - Comprehensive Review",
      changes: [
        "🚀 Code Review & Optimization: Enhanced error handling, performance optimization, React rendering improvements",
        "🎨 UI/UX Enhancement: Modern design system with Tailwind CSS, improved responsive design, accessibility compliance",
        "📊 Advanced Data Visualization: Interactive D3.js charts, real-time Plotly.js 3D plots, enhanced Recharts KPIs",
        "📁 Robust Data Input System: Drag-and-drop with validation, manual entry forms, comprehensive error handling",
        "📤 Professional Export System: Multi-format export (PDF, Excel, JSON), security classification, metadata inclusion",
        "🔍 Interactive Filtering: Real-time data filtering, health thresholds, time range selection, search capabilities",
        "⚡ Real-time Updates: Live data streaming, animated transitions, performance indicators, status monitoring",
        "🛡️ Security & Accessibility: WCAG 2.1 AA compliance, keyboard navigation, DRDO security protocols",
        "🏗️ Enhanced Equipment Status: Research-based monitoring, component-level tracking, predictive maintenance",
        "📱 Responsive Design: Mobile optimization, tablet support, modern card layouts, smooth animations"
      ]
    },
    {
      version: "1.05",
      date: "July 2025",
      title: "Layout Enhancement & Changelog",
      changes: [
        "Added comprehensive changelog popup system",
        "Improved layout alignment and spacing across all modules",
        "Enhanced div structure with proper headings hierarchy",
        "Added responsive grid layout for content sections",
        "Implemented better visual separation between components",
        "Upgraded version tracking system",
        "Added footer changelog button for easy access"
      ]
    },
    {
      version: "1.01",
      date: "July 2025", 
      title: "Initial Stage 1 Release",
      changes: [
        "Established basic React project structure with Vite",
        "Created formal DRDO header with academic design",
        "Implemented sidebar navigation for three main modules",
        "Added Equipment Status page with indigenous systems",
        "Developed Mission Statistics page with Atmanirbhar Bharat focus",
        "Built Simulation Results page with test range analytics",
        "Integrated DRDO-specific content and terminology",
        "Added testing placeholders for layout validation"
      ]
    },
    {
      version: "0.1.0",
      date: "July 2025",
      title: "Project Initialization",
      changes: [
        "Initial project setup and configuration",
        "Basic component architecture establishment",
        "Technology stack selection and implementation",
        "Project documentation and README creation"
      ]
    }
  ]

  return (
    <>
      <button className="changelog-button" onClick={toggleChangelog}>
        📋 Changelog v{changelogData[0].version}
      </button>

      {isOpen && (
        <div className="changelog-overlay" onClick={toggleChangelog}>
          <div className="changelog-popup" onClick={(e) => e.stopPropagation()}>
            <div className="changelog-header">
              <h2>IDX Dashboard - Version History</h2>
              <button className="close-button" onClick={toggleChangelog}>×</button>
            </div>
            
            <div className="changelog-content">
              {changelogData.map((version, index) => (
                <div key={version.version} className="version-section">
                  <div className="version-header">
                    <h3>Version {version.version}</h3>
                    <span className="version-date">{version.date}</span>
                  </div>
                  <h4 className="version-title">{version.title}</h4>
                  <ul className="changes-list">
                    {version.changes.map((change, changeIndex) => (
                      <li key={changeIndex}>{change}</li>
                    ))}
                  </ul>
                  {index < changelogData.length - 1 && <hr className="version-divider" />}
                </div>
              ))}
            </div>
            
            <div className="changelog-footer">
              <p>IDX - Integrated Dashboard for eXcellence | DRDO</p>
              <p>Stage 1: Layout Design & Structure Development</p>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Changelog
