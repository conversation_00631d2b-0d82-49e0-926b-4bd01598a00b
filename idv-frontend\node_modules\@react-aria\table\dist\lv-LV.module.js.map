{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,qCAAa,CAAC;IAC9C,iBAAiB,CAAC,OAAS,CAAC,iCAAqB,EAAE,KAAK,UAAU,CAAC,sCAAc,CAAC;IAClF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,cAAQ,CAAC;IAC/C,cAAc,CAAC,uCAAe,CAAC;IAC/B,kBAAkB,CAAC,OAAS,CAAC,iCAAqB,EAAE,KAAK,UAAU,CAAC,wCAAgB,CAAC;IACrF,sBAAsB,CAAC,oEAA4C,CAAC;IACpE,UAAU,CAAC,aAAO,CAAC;IACnB,aAAa,CAAC,kBAAY,CAAC;IAC3B,YAAY,CAAC,6BAAiB,CAAC;AACjC", "sources": ["packages/@react-aria/table/intl/lv-LV.json"], "sourcesContent": ["{\n  \"ascending\": \"augošā secībā\",\n  \"ascendingSort\": \"kārtots pēc kolonnas {columnName} augošā secībā\",\n  \"columnSize\": \"{value} pikseļi\",\n  \"descending\": \"dilsto<PERSON>ā secībā\",\n  \"descendingSort\": \"kārtots pēc kolonnas {columnName} dilstošā secībā\",\n  \"resizerDescription\": \"Nospiediet Enter, lai sāktu izmēru mainīšanu\",\n  \"select\": \"Atlasīt\",\n  \"selectAll\": \"Atlasīt visu\",\n  \"sortable\": \"kārtojamā kolonna\"\n}\n"], "names": [], "version": 3, "file": "lv-LV.module.js.map"}