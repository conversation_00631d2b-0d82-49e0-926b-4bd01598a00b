{"mappings": "AAAA;;;;;;;;;;CAUC,GAKM,MAAM,4CAAU,IAAI;AAE3B,SAAS,mCAAa,GAAQ;IAC5B,IAAI,OAAO,QAAQ,UACjB,OAAO,IAAI,OAAO,CAAC,QAAQ;IAG7B,OAAO,KAAK;AACd;AAEO,SAAS,0CAAqB,KAAoB,EAAE,SAAc;IACvE,IAAI,SAAS,0CAAQ,GAAG,CAAC;IACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,OAAO,CAAC,EAAE,mCAAa,YAAY;AAC/C;AAEO,SAAS,0CAAa,KAAoB,EAAE,MAAW,EAAE,SAAc;IAC5E,IAAI,SAAS,0CAAQ,GAAG,CAAC;IACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,OAAO,CAAC,EAAE,mCAAa,QAAQ,CAAC,EAAE,mCAAa,YAAY;AACvE;AAEO,SAAS,yCAAoB,KAAoB,EAAE,MAAW;IACnE,yCAAyC;IACzC,OAAO;WAAI,MAAM,UAAU,CAAC,mBAAmB;KAAC,CAAC,GAAG,CAAC,CAAA,YACnD,0CAAU,OAAO,QAAQ,YACzB,IAAI,CAAC;AACT", "sources": ["packages/@react-aria/table/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {TableState} from '@react-stately/table';\n\nexport const gridIds = new WeakMap<TableState<unknown>, string>();\n\nfunction normalizeKey(key: Key): string {\n  if (typeof key === 'string') {\n    return key.replace(/\\s*/g, '');\n  }\n\n  return '' + key;\n}\n\nexport function getColumnHeaderId<T>(state: TableState<T>, columnKey: Key): string {\n  let gridId = gridIds.get(state);\n  if (!gridId) {\n    throw new Error('Unknown grid');\n  }\n\n  return `${gridId}-${normalizeKey(columnKey)}`;\n}\n\nexport function getCellId<T>(state: TableState<T>, rowKey: Key, columnKey: Key): string {\n  let gridId = gridIds.get(state);\n  if (!gridId) {\n    throw new Error('Unknown grid');\n  }\n\n  return `${gridId}-${normalizeKey(rowKey)}-${normalizeKey(columnKey)}`;\n}\n\nexport function getRowLabelledBy<T>(state: TableState<T>, rowKey: Key): string {\n  // A row is labelled by it's row headers.\n  return [...state.collection.rowHeaderColumnKeys].map(columnKey =>\n    getCellId(state, rowKey, columnKey)\n  ).join(' ');\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}