{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AA2CD,MAAM,sCAAgB,CAAA,GAAA,wCAAS,EAAE;AAEjC,SAAS,kCAAY,KAAa,EAAE,IAAY;IAC9C,OAAO,KAAK,KAAK,CAAC,QAAQ,QAAQ;AACpC;AAEA,SAAS,0BAAI,CAAS,EAAE,CAAS;IAC/B,OAAO,AAAC,CAAA,AAAC,IAAI,IAAK,CAAA,IAAK;AACzB;AAEA,SAAS,gCAAU,CAAS;IAC1B,IAAI,IAAI,KAAK,KAAK,CAAC;IACnB,IAAI,MAAM,GACR,OAAO,IAAI;SAEX,OAAO;AAEX;AAEA,SAAS,+BAAS,GAAW;IAC3B,OAAO,MAAM,KAAK,EAAE,GAAG;AACzB;AAEA,SAAS,+BAAS,GAAW;IAC3B,OAAO,MAAM,MAAM,KAAK,EAAE;AAC5B;AAEA,wCAAwC;AACxC,SAAS,uCAAiB,KAAa,EAAE,MAAc;IACrD,IAAI,MAAM,+BAAS,MAAM,QAAQ;IACjC,IAAI,IAAI,KAAK,GAAG,CAAC,OAAQ;IACzB,IAAI,IAAI,KAAK,GAAG,CAAC,OAAQ;IACzB,OAAO;WAAC;WAAG;IAAC;AACd;AAEA,SAAS,uCAAiB,CAAS,EAAE,CAAS,EAAE,MAAc;IAC5D,IAAI,MAAM,+BAAS,KAAK,KAAK,CAAC,IAAI,QAAQ,IAAI;IAC9C,OAAO,AAAC,CAAA,MAAM,GAAE,IAAK;AACvB;AAMO,SAAS,0CAAmB,KAAsB;IACvD,IAAI,EAAC,OAAO,UAAU,gBAAE,YAAY,YAAE,QAAQ,eAAE,WAAW,EAAC,GAAG;IAE/D,IAAI,CAAC,cAAc,CAAC,cAClB,eAAe;IAEjB,IAAI,YACF,aAAa,CAAA,GAAA,yCAAa,EAAE;IAE9B,IAAI,cACF,eAAe,CAAA,GAAA,yCAAa,EAAE;IAGhC,+HAA+H;IAC/H,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,yBAAiB,EAAS,YAAqB,cAAuB;IACxG,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE;QAClB,IAAI,aAAa,WAAW,aAAa;QACzC,OAAO,eAAe,SAAS,eAAe,QAAQ,aAAa,WAAW,QAAQ,CAAC;IACzF,GAAG;QAAC;KAAW;IACf,IAAI,WAAW,CAAA,GAAA,aAAK,EAAE;IACtB,IAAI,WAAW,CAAC;QACd,SAAS,OAAO,GAAG;QACnB,cAAc;IAChB;IAEA,IAAI,eAAe,MAAM,eAAe,CAAC;IACzC,IAAI,EAAC,UAAU,SAAS,EAAE,UAAU,SAAS,EAAE,MAAM,IAAI,EAAE,UAAU,QAAQ,EAAC,GAAG;IACjF,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,eAAO,EAAE;IACzC,IAAI,gBAAgB,CAAA,GAAA,aAAK,EAAE;IAE3B,IAAI,MAAM,MAAM,eAAe,CAAC;IAChC,SAAS,OAAO,CAAS;QACvB,IAAI,IAAI,KACN,0CAA0C;QAC1C,IAAI;QAEN,IAAI,kCAAY,0BAAI,GAAG,MAAM;QAC7B,IAAI,QAAQ,GAAG;YACb,IAAI,QAAQ,MAAM,gBAAgB,CAAC,OAAO;YAC1C,SAAS;QACX;IACF;IAEA,OAAO;eACL;cACA;kBACA;QACA,UAAS,CAAC;YACR,IAAI,QAAQ,CAAA,GAAA,yCAAa,EAAE;YAC3B,SAAS;QACX;aACA;gBACA;QACA,iBAAgB,CAAC,EAAE,CAAC,EAAE,MAAM;YAC1B,OAAO,uCAAiB,GAAG,GAAG;QAChC;QACA,kBAAiB,MAAM;YACrB,OAAO,uCAAiB,MAAM,eAAe,CAAC,QAAQ;QACxD;QACA,WAAU,WAAW,CAAC;YACpB,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU;YAC3B,IAAI,WAAW,MAAM;YACrB,IAAI,YAAY,WACd,0CAA0C;YAC1C,WAAW;YAEb,OAAO,kCAAY,0BAAI,UAAU,MAAM;QACzC;QACA,WAAU,WAAW,CAAC;YACpB,IAAI,IAAI,KAAK,GAAG,CAAC,UAAU;YAC3B,IAAI,QAAQ,GACV,8DAA8D;YAC9D,oCAAoC;YACpC,OAAO,gCAAU,MAAM,KAAK;iBAE5B,OAAO,kCAAY,0BAAI,MAAM,GAAG,MAAM;QAE1C;QACA,aAAY,UAAU;YACpB,IAAI,cAAc,cAAc,OAAO;YACvC,cAAc,OAAO,GAAG;YAExB,IAAI,eAAe,CAAC,cAAc,aAChC,YAAY,SAAS,OAAO;YAG9B,YAAY;QACd;oBACA;QACA;YACE,OAAO,MAAM,QAAQ,CAAC,OAAO,gBAAgB,CAAC,cAAc,KAAK,gBAAgB,CAAC,aAAa,IAAI,gBAAgB,CAAC,SAAS;QAC/H;QACA,YAAY,MAAM,UAAU,IAAI;IAClC;AACF", "sources": ["packages/@react-stately/color/src/useColorWheelState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Color, ColorWheelProps} from '@react-types/color';\nimport {normalizeColor, parseColor} from './Color';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo, useRef, useState} from 'react';\n\nexport interface ColorWheelState {\n  /** The current color value represented by the color wheel. */\n  readonly value: Color,\n  /** Sets the color value represented by the color wheel, and triggers `onChange`. */\n  setValue(value: string | Color): void,\n\n  /** The current value of the hue channel displayed by the color wheel. */\n  readonly hue: number,\n  /** Sets the hue channel of the current color value and triggers `onChange`. */\n  setHue(value: number): void,\n\n  /** Sets the hue channel of the current color value based on the given coordinates and radius of the color wheel, and triggers `onChange`. */\n  setHueFromPoint(x: number, y: number, radius: number): void,\n  /** Returns the coordinates of the thumb relative to the center point of the color wheel. */\n  getThumbPosition(radius: number): {x: number, y: number},\n\n  /** Increments the hue by the given amount (defaults to 1). */\n  increment(stepSize?: number): void,\n  /** Decrements the hue by the given amount (defaults to 1). */\n  decrement(stepSize?: number): void,\n\n  /** Whether the color wheel is currently being dragged. */\n  readonly isDragging: boolean,\n  /** Sets whether the color wheel is being dragged. */\n  setDragging(value: boolean): void,\n  /** Returns the color that should be displayed in the color wheel instead of `value`. */\n  getDisplayColor(): Color,\n  /** The step value of the hue channel, used when incrementing and decrementing. */\n  step: number,\n  /** The page step value of the hue channel, used when incrementing and decrementing. */\n  pageStep: number,\n\n  /** Whether the color wheel is disabled. */\n  readonly isDisabled: boolean\n}\n\nconst DEFAULT_COLOR = parseColor('hsl(0, 100%, 50%)');\n\nfunction roundToStep(value: number, step: number): number {\n  return Math.round(value / step) * step;\n}\n\nfunction mod(n: number, m: number) {\n  return ((n % m) + m) % m;\n}\n\nfunction roundDown(v: number) {\n  let r = Math.floor(v);\n  if (r === v) {\n    return v - 1;\n  } else {\n    return r;\n  }\n}\n\nfunction degToRad(deg: number) {\n  return deg * Math.PI / 180;\n}\n\nfunction radToDeg(rad: number) {\n  return rad * 180 / Math.PI;\n}\n\n// 0deg = 3 o'clock. increases clockwise\nfunction angleToCartesian(angle: number, radius: number): {x: number, y: number} {\n  let rad = degToRad(360 - angle + 90);\n  let x = Math.sin(rad) * (radius);\n  let y = Math.cos(rad) * (radius);\n  return {x, y};\n}\n\nfunction cartesianToAngle(x: number, y: number, radius: number): number {\n  let deg = radToDeg(Math.atan2(y / radius, x / radius));\n  return (deg + 360) % 360;\n}\n\n/**\n * Provides state management for a color wheel component.\n * Color wheels allow users to adjust the hue of an HSL or HSB color value on a circular track.\n */\nexport function useColorWheelState(props: ColorWheelProps): ColorWheelState {\n  let {value: propsValue, defaultValue, onChange, onChangeEnd} = props;\n\n  if (!propsValue && !defaultValue) {\n    defaultValue = DEFAULT_COLOR;\n  }\n  if (propsValue) {\n    propsValue = normalizeColor(propsValue);\n  }\n  if (defaultValue) {\n    defaultValue = normalizeColor(defaultValue);\n  }\n\n  // safe to cast value and defaultValue to Color, one of them will always be defined because if neither are, we assign a default\n  let [stateValue, setValueState] = useControlledState<Color>(propsValue as Color, defaultValue as Color, onChange);\n  let value = useMemo(() => {\n    let colorSpace = stateValue.getColorSpace();\n    return colorSpace === 'hsl' || colorSpace === 'hsb' ? stateValue : stateValue.toFormat('hsl');\n  }, [stateValue]);\n  let valueRef = useRef(value);\n  let setValue = (value: Color) => {\n    valueRef.current = value;\n    setValueState(value);\n  };\n\n  let channelRange = value.getChannelRange('hue');\n  let {minValue: minValueX, maxValue: maxValueX, step: step, pageSize: pageStep} = channelRange;\n  let [isDragging, setDragging] = useState(false);\n  let isDraggingRef = useRef(false);\n\n  let hue = value.getChannelValue('hue');\n  function setHue(v: number) {\n    if (v > 360) {\n      // Make sure you can always get back to 0.\n      v = 0;\n    }\n    v = roundToStep(mod(v, 360), step);\n    if (hue !== v) {\n      let color = value.withChannelValue('hue', v);\n      setValue(color);\n    }\n  }\n\n  return {\n    value,\n    step,\n    pageStep,\n    setValue(v) {\n      let color = normalizeColor(v);\n      setValue(color);\n    },\n    hue,\n    setHue,\n    setHueFromPoint(x, y, radius) {\n      setHue(cartesianToAngle(x, y, radius));\n    },\n    getThumbPosition(radius) {\n      return angleToCartesian(value.getChannelValue('hue'), radius);\n    },\n    increment(stepSize = 1) {\n      let s = Math.max(stepSize, step);\n      let newValue = hue + s;\n      if (newValue >= maxValueX) {\n        // Make sure you can always get back to 0.\n        newValue = minValueX;\n      }\n      setHue(roundToStep(mod(newValue, 360), s));\n    },\n    decrement(stepSize = 1) {\n      let s = Math.max(stepSize, step);\n      if (hue === 0) {\n        // We can't just subtract step because this might be the case:\n        // |(previous step) - 0| < step size\n        setHue(roundDown(360 / s) * s);\n      } else {\n        setHue(roundToStep(mod(hue - s, 360), s));\n      }\n    },\n    setDragging(isDragging) {\n      let wasDragging = isDraggingRef.current;\n      isDraggingRef.current = isDragging;\n\n      if (onChangeEnd && !isDragging && wasDragging) {\n        onChangeEnd(valueRef.current);\n      }\n\n      setDragging(isDragging);\n    },\n    isDragging,\n    getDisplayColor() {\n      return value.toFormat('hsl').withChannelValue('saturation', 100).withChannelValue('lightness', 50).withChannelValue('alpha', 1);\n    },\n    isDisabled: props.isDisabled || false\n  };\n}\n"], "names": [], "version": 3, "file": "useColorWheelState.module.js.map"}