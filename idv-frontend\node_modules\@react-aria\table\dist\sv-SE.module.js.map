{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,sBAAmB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IACrF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,sBAAmB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IACtF,sBAAsB,CAAC,sDAA0C,CAAC;IAClE,UAAU,CAAC,OAAO,CAAC;IACnB,aAAa,CAAC,YAAY,CAAC;IAC3B,YAAY,CAAC,gBAAgB,CAAC;AAChC", "sources": ["packages/@react-aria/table/intl/sv-SE.json"], "sourcesContent": ["{\n  \"ascending\": \"stigande\",\n  \"ascendingSort\": \"sorterat på kolumn {columnName} i stigande ordning\",\n  \"columnSize\": \"{value} pixlar\",\n  \"descending\": \"fallande\",\n  \"descendingSort\": \"sorterat på kolumn {columnName} i fallande ordning\",\n  \"resizerDescription\": \"Tryck på Retur för att börja ä<PERSON> storlek\",\n  \"select\": \"Markera\",\n  \"selectAll\": \"Markera allt\",\n  \"sortable\": \"sorterbar kolumn\"\n}\n"], "names": [], "version": 3, "file": "sv-SE.module.js.map"}