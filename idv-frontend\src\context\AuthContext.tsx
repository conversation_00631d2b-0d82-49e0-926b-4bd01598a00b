import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type UserRole = 'admin' | 'scientist' | 'engineer' | 'analyst' | 'commander' | 'guest';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  department: string;
  clearanceLevel: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';
}

interface AuthContextType {
  user: User | null;
  login: (credentials: { email: string; password: string }) => Promise<boolean>;
  logout: () => void;
  switchRole: (role: UserRole) => void;
  hasPermission: (requiredRole: UserRole) => boolean;
  isAuthenticated: boolean;
  sessionTimeLeft: number;
  autoLogin: (role: UserRole) => void;
  isDevelopmentMode: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

const roleHierarchy: Record<UserRole, number> = {
  guest: 0,
  analyst: 1,
  engineer: 2,
  scientist: 3,
  commander: 4,
  admin: 5,
};

const mockUsers: Record<string, User> = {
  '<EMAIL>': {
    id: '1',
    name: 'Dr. Aditya Shenvi',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '👨‍💼',
    department: 'Systems Integration',
    clearanceLevel: 'SECRET',
  },
  '<EMAIL>': {
    id: '2',
    name: 'Dr. Priya Sharma',
    email: '<EMAIL>',
    role: 'scientist',
    avatar: '👩‍🔬',
    department: 'Missile Technology',
    clearanceLevel: 'CONFIDENTIAL',
  },
  '<EMAIL>': {
    id: '3',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    role: 'engineer',
    avatar: '👨‍🔧',
    department: 'Avionics',
    clearanceLevel: 'RESTRICTED',
  },
  '<EMAIL>': {
    id: '4',
    name: 'Col. Sharma',
    email: '<EMAIL>',
    role: 'commander',
    avatar: '🎖️',
    department: 'Strategic Command',
    clearanceLevel: 'SECRET',
  },
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [sessionTimeLeft, setSessionTimeLeft] = useState(900); // 15 minutes in seconds
  const [sessionTimer, setSessionTimer] = useState<NodeJS.Timeout | null>(null);
  const isDevelopmentMode = process.env.NODE_ENV === 'development';

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem('drdo-user');
    const sessionExpiry = localStorage.getItem('drdo-session-expiry');

    if (savedUser && sessionExpiry) {
      const expiryTime = parseInt(sessionExpiry);
      const currentTime = Date.now();

      if (currentTime < expiryTime) {
        setUser(JSON.parse(savedUser));
        const timeLeft = Math.floor((expiryTime - currentTime) / 1000);
        setSessionTimeLeft(timeLeft);
        startSessionTimer(timeLeft);
        return;
      } else {
        // Session expired
        logout();
      }
    }

    // Auto-login in development mode
    if (isDevelopmentMode && !user) {
      const autoLoginRole = localStorage.getItem('drdo-auto-login-role') as UserRole || 'scientist';
      autoLogin(autoLoginRole);
    }
  }, []);

  const startSessionTimer = (initialTime: number) => {
    if (sessionTimer) {
      clearInterval(sessionTimer);
    }

    const timer = setInterval(() => {
      setSessionTimeLeft((prev) => {
        if (prev <= 1) {
          logout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setSessionTimer(timer);
  };

  const login = async (credentials: { email: string; password: string }): Promise<boolean> => {
    // Mock authentication - in real app, this would call an API
    const mockUser = mockUsers[credentials.email];
    
    if (mockUser && credentials.password === 'drdo123') {
      const sessionExpiry = Date.now() + (15 * 60 * 1000); // 15 minutes
      
      setUser(mockUser);
      setSessionTimeLeft(900);
      
      localStorage.setItem('drdo-user', JSON.stringify(mockUser));
      localStorage.setItem('drdo-session-expiry', sessionExpiry.toString());
      
      startSessionTimer(900);
      
      // Log authentication event
      logAuditEvent('USER_LOGIN', `User ${mockUser.name} logged in`);
      
      return true;
    }
    
    return false;
  };

  const logout = () => {
    if (user) {
      logAuditEvent('USER_LOGOUT', `User ${user.name} logged out`);
    }
    
    setUser(null);
    setSessionTimeLeft(0);
    
    if (sessionTimer) {
      clearInterval(sessionTimer);
      setSessionTimer(null);
    }
    
    localStorage.removeItem('drdo-user');
    localStorage.removeItem('drdo-session-expiry');
  };

  const switchRole = (role: UserRole) => {
    if (user && hasPermission('admin')) {
      const updatedUser = { ...user, role };
      setUser(updatedUser);
      localStorage.setItem('drdo-user', JSON.stringify(updatedUser));
      
      logAuditEvent('ROLE_SWITCH', `User ${user.name} switched to role: ${role}`);
    }
  };

  const hasPermission = (requiredRole: UserRole): boolean => {
    if (!user) return false;
    return roleHierarchy[user.role] >= roleHierarchy[requiredRole];
  };

  const autoLogin = (role: UserRole) => {
    const autoUser = Object.values(mockUsers).find(u => u.role === role);
    if (autoUser) {
      const sessionExpiry = Date.now() + (15 * 60 * 1000); // 15 minutes

      setUser(autoUser);
      setSessionTimeLeft(900);

      localStorage.setItem('drdo-user', JSON.stringify(autoUser));
      localStorage.setItem('drdo-session-expiry', sessionExpiry.toString());
      localStorage.setItem('drdo-auto-login-role', role);

      startSessionTimer(900);

      // Log auto-login event
      logAuditEvent('USER_LOGIN', `Auto-login as ${autoUser.name} (${role}) in development mode`);
    }
  };

  const logAuditEvent = (action: string, details: string) => {
    const auditLog = {
      timestamp: new Date().toISOString(),
      userId: user?.id || 'anonymous',
      userName: user?.name || 'Anonymous',
      action,
      details,
      ipAddress: '127.0.0.1', // Mock IP
      userAgent: navigator.userAgent,
    };

    // Get existing logs
    const existingLogs = JSON.parse(localStorage.getItem('drdo-audit-logs') || '[]');
    existingLogs.push(auditLog);
    
    // Keep only last 1000 logs
    if (existingLogs.length > 1000) {
      existingLogs.splice(0, existingLogs.length - 1000);
    }
    
    localStorage.setItem('drdo-audit-logs', JSON.stringify(existingLogs));
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        switchRole,
        hasPermission,
        isAuthenticated: !!user,
        sessionTimeLeft,
        autoLogin,
        isDevelopmentMode,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
