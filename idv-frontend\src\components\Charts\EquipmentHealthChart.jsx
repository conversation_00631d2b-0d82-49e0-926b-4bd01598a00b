import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import * as d3 from 'd3'
import './EquipmentHealthChart.css'

const EquipmentHealthChart = ({ data = [], width = 800, height = 400 }) => {
  const svgRef = useRef()
  const [selectedSystem, setSelectedSystem] = useState(null)
  const [tooltip, setTooltip] = useState({ visible: false, x: 0, y: 0, content: '' })
  const [timeFilter, setTimeFilter] = useState('all')
  const [healthThreshold, setHealthThreshold] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // Filter data based on time and health threshold
  const filteredData = useMemo(() => {
    let filtered = data.length > 0 ? data : [
    { system: 'BrahMos', timestamp: '2025-07-07T09:00:00Z', health: 95, status: 'Active' },
    { system: 'BrahMos', timestamp: '2025-07-07T10:00:00Z', health: 93, status: 'Active' },
    { system: 'BrahMos', timestamp: '2025-07-07T11:00:00Z', health: 97, status: 'Active' },
    { system: 'Agni-V', timestamp: '2025-07-07T09:00:00Z', health: 88, status: 'Standby' },
    { system: 'Agni-V', timestamp: '2025-07-07T10:00:00Z', health: 90, status: 'Active' },
    { system: 'Agni-V', timestamp: '2025-07-07T11:00:00Z', health: 85, status: 'Maintenance' },
    { system: 'Tejas', timestamp: '2025-07-07T09:00:00Z', health: 92, status: 'Active' },
    { system: 'Tejas', timestamp: '2025-07-07T10:00:00Z', health: 94, status: 'Active' },
    { system: 'Tejas', timestamp: '2025-07-07T11:00:00Z', health: 91, status: 'Active' },
  ]

    // Apply health threshold filter
    if (healthThreshold > 0) {
      filtered = filtered.filter(d => d.health >= healthThreshold)
    }

    // Apply time filter
    if (timeFilter !== 'all') {
      const now = new Date()
      const timeLimit = new Date()

      switch (timeFilter) {
        case '1h':
          timeLimit.setHours(now.getHours() - 1)
          break
        case '6h':
          timeLimit.setHours(now.getHours() - 6)
          break
        case '24h':
          timeLimit.setDate(now.getDate() - 1)
          break
        default:
          break
      }

      filtered = filtered.filter(d => new Date(d.timestamp) >= timeLimit)
    }

    return filtered
  }, [data, timeFilter, healthThreshold])

  useEffect(() => {
    if (!filteredData.length) return

    setIsAnimating(true)

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    const margin = { top: 20, right: 80, bottom: 40, left: 60 }
    const innerWidth = width - margin.left - margin.right
    const innerHeight = height - margin.top - margin.bottom

    const g = svg
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`)

    // Parse timestamps and group by system
    const parseTime = d3.timeParse('%Y-%m-%dT%H:%M:%SZ')
    const processedData = filteredData.map(d => ({
      ...d,
      timestamp: parseTime(d.timestamp)
    }))

    const systems = Array.from(new Set(processedData.map(d => d.system)))
    const colorScale = d3.scaleOrdinal(d3.schemeCategory10).domain(systems)

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(processedData, d => d.timestamp))
      .range([0, innerWidth])

    const yScale = d3.scaleLinear()
      .domain([0, 100])
      .range([innerHeight, 0])

    // Line generator
    const line = d3.line()
      .x(d => xScale(d.timestamp))
      .y(d => yScale(d.health))
      .curve(d3.curveMonotoneX)

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')))

    g.append('g')
      .call(d3.axisLeft(yScale))

    // Add axis labels
    g.append('text')
      .attr('transform', 'rotate(-90)')
      .attr('y', 0 - margin.left)
      .attr('x', 0 - (innerHeight / 2))
      .attr('dy', '1em')
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .text('Health Score (%)')

    g.append('text')
      .attr('transform', `translate(${innerWidth / 2}, ${innerHeight + margin.bottom})`)
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .text('Time')

    // Draw lines for each system
    systems.forEach(system => {
      const systemData = processedData.filter(d => d.system === system)
      
      const path = g.append('path')
        .datum(systemData)
        .attr('fill', 'none')
        .attr('stroke', colorScale(system))
        .attr('stroke-width', selectedSystem === system ? 3 : 2)
        .attr('opacity', selectedSystem && selectedSystem !== system ? 0.3 : 1)
        .attr('d', line)
        .style('cursor', 'pointer')

      // Add dots for data points
      g.selectAll(`.dot-${system}`)
        .data(systemData)
        .enter().append('circle')
        .attr('class', `dot-${system}`)
        .attr('cx', d => xScale(d.timestamp))
        .attr('cy', d => yScale(d.health))
        .attr('r', 4)
        .attr('fill', colorScale(system))
        .style('cursor', 'pointer')
        .on('mouseover', (event, d) => {
          setTooltip({
            visible: true,
            x: event.pageX + 10,
            y: event.pageY - 10,
            content: `${d.system}: ${d.health}% (${d.status})`
          })
        })
        .on('mouseout', () => {
          setTooltip({ visible: false, x: 0, y: 0, content: '' })
        })
        .on('click', (event, d) => {
          setSelectedSystem(selectedSystem === d.system ? null : d.system)
        })
    })

    // Add legend
    const legend = g.append('g')
      .attr('transform', `translate(${innerWidth + 10}, 20)`)

    systems.forEach((system, i) => {
      const legendRow = legend.append('g')
        .attr('transform', `translate(0, ${i * 20})`)
        .style('cursor', 'pointer')
        .on('click', () => {
          setSelectedSystem(selectedSystem === system ? null : system)
        })

      legendRow.append('rect')
        .attr('width', 12)
        .attr('height', 12)
        .attr('fill', colorScale(system))
        .attr('opacity', selectedSystem && selectedSystem !== system ? 0.3 : 1)

      legendRow.append('text')
        .attr('x', 16)
        .attr('y', 9)
        .style('font-size', '12px')
        .style('alignment-baseline', 'middle')
        .text(system)
        .attr('opacity', selectedSystem && selectedSystem !== system ? 0.3 : 1)
    })

    // Animation completion
    setTimeout(() => setIsAnimating(false), 1000)

  }, [filteredData, width, height, selectedSystem, timeFilter, healthThreshold])

  return (
    <div className="equipment-health-chart">
      <div className="chart-header">
        <h3>Equipment Health Monitoring</h3>
        <div className="chart-controls">
          <div className="filter-group">
            <label>Time Range:</label>
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Time</option>
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h">Last 24 Hours</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Health Threshold:</label>
            <input
              type="range"
              min="0"
              max="100"
              value={healthThreshold}
              onChange={(e) => setHealthThreshold(Number(e.target.value))}
              className="health-slider"
            />
            <span className="threshold-value">{healthThreshold}%</span>
          </div>

          <button
            className={`filter-btn ${!selectedSystem ? 'active' : ''}`}
            onClick={() => setSelectedSystem(null)}
          >
            All Systems
          </button>
          {selectedSystem && (
            <span className="selected-system">
              Viewing: {selectedSystem}
            </span>
          )}
        </div>
      </div>
      
      <div className="chart-container">
        <svg ref={svgRef}></svg>
        
        {tooltip.visible && (
          <div 
            className="chart-tooltip"
            style={{ left: tooltip.x, top: tooltip.y }}
          >
            {tooltip.content}
          </div>
        )}
      </div>

      <div className="chart-info">
        <div className="info-item">
          <span className="info-label">Data Points:</span>
          <span className="info-value">{sampleData.length}</span>
        </div>
        <div className="info-item">
          <span className="info-label">Systems:</span>
          <span className="info-value">{Array.from(new Set(sampleData.map(d => d.system))).length}</span>
        </div>
        <div className="info-item">
          <span className="info-label">Last Update:</span>
          <span className="info-value">{new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  )
}

export default EquipmentHealthChart
