import React, { useState, useEffect, useMemo } from 'react'
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, AreaChart, Area
} from 'recharts'
import './KPIDashboard.css'

const KPIDashboard = ({ data = {} }) => {
  const [activeChart, setActiveChart] = useState('overview')
  const [realTimeMetrics, setRealTimeMetrics] = useState({
    successRate: 95.2,
    totalMissions: 387,
    equipmentUptime: 94.6,
    activeLabs: 52
  })
  const [isUpdating, setIsUpdating] = useState(false)

  // Sample KPI data if none provided
  const sampleData = {
    missionSuccess: [
      { name: 'Land Operations', success: 95, total: 100 },
      { name: 'Air Defense', success: 88, total: 95 },
      { name: 'Naval Systems', success: 92, total: 98 },
      { name: 'Space & Cyber', success: 85, total: 90 }
    ],
    equipmentStatus: [
      { name: 'Active', value: 65, color: '#28a745' },
      { name: 'Standby', value: 20, color: '#ffc107' },
      { name: 'Maintenance', value: 12, color: '#fd7e14' },
      { name: 'Offline', value: 3, color: '#dc3545' }
    ],
    monthlyPerformance: [
      { month: 'Jan', missions: 45, success: 42, efficiency: 93 },
      { month: 'Feb', missions: 52, success: 48, efficiency: 92 },
      { month: 'Mar', missions: 48, success: 46, efficiency: 96 },
      { month: 'Apr', missions: 55, success: 51, efficiency: 93 },
      { month: 'May', missions: 60, success: 57, efficiency: 95 },
      { month: 'Jun', missions: 58, success: 55, efficiency: 95 },
      { month: 'Jul', missions: 62, success: 59, efficiency: 95 }
    ],
    systemReadiness: [
      { system: 'BrahMos', readiness: 98 },
      { system: 'Agni-V', readiness: 95 },
      { system: 'Tejas', readiness: 92 },
      { system: 'Arjun', readiness: 88 },
      { system: 'Akash', readiness: 94 }
    ]
  }

  // Real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setIsUpdating(true)
      setRealTimeMetrics(prev => ({
        successRate: Math.max(90, Math.min(100, prev.successRate + (Math.random() - 0.5) * 0.5)),
        totalMissions: prev.totalMissions + Math.floor(Math.random() * 3),
        equipmentUptime: Math.max(85, Math.min(100, prev.equipmentUptime + (Math.random() - 0.5) * 0.3)),
        activeLabs: Math.max(50, Math.min(52, prev.activeLabs + Math.floor((Math.random() - 0.5) * 2)))
      }))
      setTimeout(() => setIsUpdating(false), 500)
    }, 10000) // Update every 10 seconds

    return () => clearInterval(interval)
  }, [])

  const kpiData = useMemo(() => ({ ...sampleData, ...data }), [sampleData, data])

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}${entry.dataKey.includes('efficiency') || entry.dataKey.includes('readiness') ? '%' : ''}`}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  const renderChart = () => {
    switch (activeChart) {
      case 'overview':
        return (
          <div className="chart-grid">
            <div className="chart-item">
              <h4>Mission Success Rate</h4>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={kpiData.missionSuccess}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" fontSize={12} />
                  <YAxis fontSize={12} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="success" fill="#007bff" />
                  <Bar dataKey="total" fill="#e9ecef" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="chart-item">
              <h4>Equipment Status Distribution</h4>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={kpiData.equipmentStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {kpiData.equipmentStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        )
      
      case 'performance':
        return (
          <div className="chart-single">
            <h4>Monthly Performance Trends</h4>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={kpiData.monthlyPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line type="monotone" dataKey="missions" stroke="#007bff" strokeWidth={2} />
                <Line type="monotone" dataKey="success" stroke="#28a745" strokeWidth={2} />
                <Line type="monotone" dataKey="efficiency" stroke="#ffc107" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )
      
      case 'readiness':
        return (
          <div className="chart-single">
            <h4>System Readiness Levels</h4>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={kpiData.systemReadiness}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="system" />
                <YAxis domain={[80, 100]} />
                <Tooltip content={<CustomTooltip />} />
                <Area 
                  type="monotone" 
                  dataKey="readiness" 
                  stroke="#007bff" 
                  fill="#007bff" 
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="kpi-dashboard">
      <div className="dashboard-header">
        <h3>Key Performance Indicators</h3>
        <div className="chart-tabs">
          <button 
            className={`tab-btn ${activeChart === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveChart('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab-btn ${activeChart === 'performance' ? 'active' : ''}`}
            onClick={() => setActiveChart('performance')}
          >
            Performance
          </button>
          <button 
            className={`tab-btn ${activeChart === 'readiness' ? 'active' : ''}`}
            onClick={() => setActiveChart('readiness')}
          >
            Readiness
          </button>
        </div>
      </div>

      <div className="kpi-summary">
        <div className={`kpi-card ${isUpdating ? 'updating' : ''}`}>
          <div className="kpi-value">{realTimeMetrics.successRate.toFixed(1)}%</div>
          <div className="kpi-label">Overall Success Rate</div>
          <div className="kpi-trend positive">****%</div>
          <div className="kpi-indicator">
            <span className="indicator-dot success"></span>
            <span className="indicator-text">Excellent</span>
          </div>
        </div>
        <div className={`kpi-card ${isUpdating ? 'updating' : ''}`}>
          <div className="kpi-value">{realTimeMetrics.totalMissions}</div>
          <div className="kpi-label">Total Missions</div>
          <div className="kpi-trend positive">+15</div>
          <div className="kpi-indicator">
            <span className="indicator-dot active"></span>
            <span className="indicator-text">Active</span>
          </div>
        </div>
        <div className={`kpi-card ${isUpdating ? 'updating' : ''}`}>
          <div className="kpi-value">{realTimeMetrics.equipmentUptime.toFixed(1)}%</div>
          <div className="kpi-label">Equipment Uptime</div>
          <div className="kpi-trend neutral">-0.2%</div>
          <div className="kpi-indicator">
            <span className="indicator-dot warning"></span>
            <span className="indicator-text">Good</span>
          </div>
        </div>
        <div className={`kpi-card ${isUpdating ? 'updating' : ''}`}>
          <div className="kpi-value">{realTimeMetrics.activeLabs}</div>
          <div className="kpi-label">Active Labs</div>
          <div className="kpi-trend positive">+2</div>
          <div className="kpi-indicator">
            <span className="indicator-dot success"></span>
            <span className="indicator-text">Operational</span>
          </div>
        </div>
      </div>

      <div className="chart-content">
        {renderChart()}
      </div>

      <div className="dashboard-footer">
        <div className="footer-info">
          <span>Last Updated: {new Date().toLocaleString()}</span>
          <span>Data Source: DRDO Integrated Systems</span>
          <span>Refresh Rate: Real-time</span>
        </div>
      </div>
    </div>
  )
}

export default KPIDashboard
