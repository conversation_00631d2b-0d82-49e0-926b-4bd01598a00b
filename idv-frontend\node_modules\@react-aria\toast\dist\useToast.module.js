import $7WpW4$intlStringsmodulejs from "./intlStrings.module.js";
import {useId as $7WpW4$useId, useSlotId as $7WpW4$useSlotId, filterDOMProps as $7WpW4$filterDOMProps} from "@react-aria/utils";
import {useEffect as $7WpW4$useEffect, useState as $7WpW4$useState} from "react";
import {useLocalizedStringFormatter as $7WpW4$useLocalizedStringFormatter} from "@react-aria/i18n";


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 



function $d6542812f0669241$export$a407b657d3044108(props, state, ref) {
    let { key: key, timer: timer, timeout: timeout } = props.toast;
    (0, $7WpW4$useEffect)(()=>{
        if (timer == null || timeout == null) return;
        timer.reset(timeout);
        return ()=>{
            timer.pause();
        };
    }, [
        timer,
        timeout
    ]);
    let titleId = (0, $7WpW4$useId)();
    let descriptionId = (0, $7WpW4$useSlotId)();
    let stringFormatter = (0, $7WpW4$useLocalizedStringFormatter)((0, ($parcel$interopDefault($7WpW4$intlStringsmodulejs))), '@react-aria/toast');
    // This is required for NVDA announcements, without it NVDA will NOT announce the toast when it appears.
    // Originally was tied to animationStart/End via https://github.com/adobe/react-spectrum/pull/6223/commits/e22e319df64958e822ab7cd9685e96818cae9ba5
    // but toasts don't always have animations.
    let [isVisible, setIsVisible] = (0, $7WpW4$useState)(false);
    (0, $7WpW4$useEffect)(()=>{
        setIsVisible(true);
    }, []);
    let toastProps = (0, $7WpW4$filterDOMProps)(props, {
        labelable: true
    });
    return {
        toastProps: {
            ...toastProps,
            role: 'alertdialog',
            'aria-modal': 'false',
            'aria-labelledby': props['aria-labelledby'] || titleId,
            'aria-describedby': props['aria-describedby'] || descriptionId,
            tabIndex: 0
        },
        contentProps: {
            role: 'alert',
            'aria-atomic': 'true',
            'aria-hidden': isVisible ? undefined : 'true'
        },
        titleProps: {
            id: titleId
        },
        descriptionProps: {
            id: descriptionId
        },
        closeButtonProps: {
            'aria-label': stringFormatter.format('close'),
            onPress: ()=>state.close(key)
        }
    };
}


export {$d6542812f0669241$export$a407b657d3044108 as useToast};
//# sourceMappingURL=useToast.module.js.map
