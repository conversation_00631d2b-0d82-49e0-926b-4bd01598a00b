# ARIA Query

![CI](https://github.com/A11yance/aria-query/workflows/CI/badge.svg)

Programmatic access to the [WAI-ARIA 1.2 Roles Model](https://www.w3.org/TR/wai-aria-1.2/#roles).
This package tracks the W3C Recommendation (last update: 6 June 2023).

CDN URL: <https://unpkg.com/aria-query>

## Building the `src/etc` files

The files under `src/etc` are generated by the `breakUpAriaJSON` script.

To change them, edit the file `scripts/roles.json` then run:

```bash
node ./scripts/breakUpAriaJSON.js
git add scripts/roles.json src/etc
```

It should work with Node version 6.11.2 or later.

## Utilities

### Interface

These methods are available on each export from the module. The typing here in the documentation is pseudo-typed. Each export will have its own specific types for each method signature.

```javascript
{|
  entries: () => Array<$Item>,
  get: (key: $Key) => ?$Value,
  has: (key: $Key) => boolean,
  keys: () => Array<$Key>,
  values: () => Array<$Value>,
|};
```

### Roles

```javascript
import { roles } from 'aria-query';
```

A map of role names to the role definition. For example:

```javascript
let alertRole = roles.get('alert');
/**
 * Value of alertRole
 * {
 *   "requiredProps": {},
 *   "props": {
 *     "aria-atomic": "true",
 *     "aria-busy": null,
 *     "aria-controls": null,
 *     "aria-current": null,
 *     "aria-describedby": null,
 *     "aria-details": null,
 *     "aria-disabled": null,
 *     "aria-dropeffect": null,
 *     "aria-errormessage": null,
 *     "aria-expanded": null,
 *     "aria-flowto": null,
 *     "aria-grabbed": null,
 *     "aria-haspopup": null,
 *     "aria-hidden": null,
 *     "aria-invalid": null,
 *     "aria-keyshortcuts": null,
 *     "aria-label": null,
 *     "aria-labelledby": null,
 *     "aria-live": "assertive",
 *     "aria-owns": null,
 *     "aria-relevant": null,
 *     "aria-roledescription": null
 *   },
 *   "abstract": false,
 *   "childrenPresentational": false,
 *   "baseConcepts": [],
 *   "relatedConcepts": [ {
 *     "module": "XForms",
 *     "concept": {
 *       "name": "alert"
 *     }
 *   }],
 *   "superClass": [["roletype", "structure", "section"]]
 * }
```

### Elements to Roles

```javascript
import { elementRoles } from 'aria-query';
```

HTML Elements with inherent roles are mapped to those roles. In the case of an element like `<input>`, the element often requires a `type` attribute to map to an ARIA role.

```javascript
[
  [ '{"name": "article"}', [ 'article' ] ],
  [ '{"name": "button"}', [ 'button' ] ],
  [ '{"name": "td"}', [ 'cell', 'gridcell' ] ],
  [ '{"name": "input", "attributes": [ {"name": "type", "value": "checkbox"}] }', [ 'checkbox' ] ],
  [ '{"name": "th"}', [ 'columnheader' ] ],
  [ '{"name": "select"}', [ 'combobox', 'listbox' ] ],
  [ '{"name": "menuitem"}', [ 'command', 'menuitem' ] ],
  [ '{"name": "dd"}', [ 'definition' ] ],
  [ '{"name": "figure"}', [ 'figure' ] ],
  [ '{"name": "form"}', [ 'form' ] ],
  [ '{"name": "table"}', [ 'grid', 'table' ] ],
  [ '{"name": "fieldset"}', [ 'group' ] ],
  [ '{"name": "h1"}', [ 'heading' ] ],
  [ '{"name": "h2"}', [ 'heading' ] ],
  [ '{"name": "h3"}', [ 'heading' ] ],
  [ '{"name": "h4"}', [ 'heading' ] ],
  [ '{"name": "h5"}', [ 'heading' ] ],
  [ '{"name": "h6"}', [ 'heading' ] ],
  [ '{"name": "img"}', [ 'img' ] ],
  [ '{"name": "a"}', [ 'link' ] ],
  [ '{"name": "link"}', [ 'link' ] ],
  [ '{"name": "ol"}', [ 'list' ] ],
  [ '{"name": "ul"}', [ 'list' ] ],
  [ '{"name": "li"}', [ 'listitem' ] ],
  [ '{"name": "nav"}', [ 'navigation' ] ],
  [ '{"name": "option"}', [ 'option' ] ],
  [ '{"name": "input", "attributes": [ {"name": "type", "value": "radio"}] }', [ 'radio' ] ],
  [ '{"name": "frame"}', [ 'region' ] ],
  [ '{"name": "rel"}', [ 'roletype' ] ],
  [ '{"name": "tr"}', [ 'row' ] ],
  [ '{"name": "tbody"}', [ 'rowgroup' ] ],
  [ '{"name": "tfoot"}', [ 'rowgroup' ] ],
  [ '{"name": "thead"}', [ 'rowgroup' ] ],
  [ '{"name": "th", "attributes": [ {"name": "scope", "value": "row"}] }', [ 'rowheader' ] ],
  [ '{"name": "input", "attributes": [ {"name": "type", "value": "search"}] }', [ 'searchbox' ] ],
  [ '{"name": "hr"}', [ 'separator' ] ],
  [ '{"name": "dt"}', [ 'term' ] ],
  [ '{"name": "dfn"}', [ 'term' ] ],
  [ '{"name": "textarea"}', [ 'textbox' ] ],
  [ '{"name": "input", "attributes": [ {"name": "type", "value": "text"}] }', [ 'textbox' ] ],
]
```

The map of elements to roles is keyed by an HTML concept. An HTML concept corresponds to the `baseConcepts` and `relatedConcepts` of an ARIA role. Concepts exist in the context of a `module`: HTML, XForms, Dublin Core, for example.  The concept representation is an object literal with a name property (the element name) and an optional attributes array.

The roles are provided in a [Set](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set).

### Role to element

```javascript
import { roleElements } from 'aria-query';
```

ARIA roles are mapped to the HTML Elements with the same inherent role. Some roles, such as `columnheader` are only mapped to an HTML element that expresses specific attributes. In the case of `<input>`, the element often requires a `type` attribute to map to an ARIA role.

```javascript
[
  [ 'article', [ {"name": "article"} ] ],
  [ 'button', [ {"name": "button"} ] ],
  [ 'cell', [ {"name": "td"} ] ],
  [ 'checkbox', [ {"name": "input", "attributes": [ {"name": "type", "value": "checkbox"}] } ] ],
  [ 'columnheader', [ {"name": "th"} ] ],
  [ 'combobox', [ {"name": "select"} ] ],
  [ 'command', [ {"name": "menuitem"} ] ],
  [ 'definition', [ {"name": "dd"}', '{"name": "dfn"} ] ],
  [ 'figure', [ {"name": "figure"} ] ],
  [ 'form', [ {"name": "form"} ] ],
  [ 'grid', [ {"name": "table"} ] ],
  [ 'gridcell', [ {"name": "td"} ] ],
  [ 'group', [ {"name": "fieldset"} ] ],
  [ 'heading', [ {"name": "h1"}', '{"name": "h2"}', '{"name": "h3"}', '{"name": "h4"}',  '{"name": "h5"}', '{"name": "h6"} ] ],
  [ 'img', [ {"name": "img"} ] ],
  [ 'link', [ {"name": "a"}', '{"name": "link"} ] ],
  [ 'list', [ {"name": "ol"}', '{"name": "ul"} ] ],
  [ 'listbox', [ {"name": "select"} ] ],
  [ 'listitem', [ {"name": "li"} ] ],
  [ 'menuitem', [ {"name": "menuitem"} ] ],
  [ 'navigation', [ {"name": "nav"} ] ],
  [ 'option', [ {"name": "option"} ] ],
  [ 'radio', [ {"name": "input", "attributes": [ {"name": "type", "value": "radio"}] } ] ],
  [ 'region', [ {"name": "frame"} ] ],
  [ 'roletype', [ {"name": "rel"} ] ],
  [ 'row', [ {"name": "tr"} ] ],
  [ 'rowgroup', [ {"name": "tbody"}', '{"name": "tfoot"}', '{"name": "thead"} ] ],
  [ 'rowheader', [ {"name": "th", "attributes": [ {"name": "scope", "value": "row"}] }, {"name": "th", "attributes": [ {"name": "scope", "value": "rowgroup"}] } ] ],
  [ 'searchbox', [ {"name": "input", "attributes": [ {"name": "type", "value": "search"}] } ] ],
  [ 'separator', [ {"name": "hr"} ] ],
  [ 'table', [ {"name": "table"} ] ],
  [ 'term', [ {"name": "dt"} ] ],
  [ 'textbox', [ {"name": "textarea"}', '{"name": "input", "attributes": [ {"name": "type", "value": "text"}] } ] ],
]
```

## License

Copyright (c) 2021 A11yance
