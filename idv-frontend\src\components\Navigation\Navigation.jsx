import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../context/AuthContext'
import { t } from '../../utils/i18n'
import { logAuditEvent } from '../../utils/audit'
import './Navigation.css'

const Navigation = () => {
  const { user, hasPermission } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [hoveredItem, setHoveredItem] = useState(null)

  const activeView = location.pathname.replace('/', '') || 'dashboard'

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      description: 'Main overview with mission status and real-time equipment monitoring',
      requiredRole: 'guest',
      path: '/dashboard'
    },
    {
      id: 'data-transfer',
      label: 'Data Transfer',
      icon: '📤',
      description: 'Secure file upload with encryption level selection',
      requiredRole: 'engineer',
      path: '/data-transfer'
    },
    {
      id: 'visual-sandbox',
      label: 'Visual Sandbox',
      icon: '📈',
      description: 'Interactive chart builder with live preview capabilities',
      requiredRole: 'scientist',
      path: '/visual-sandbox'
    },
    {
      id: 'users',
      label: 'Users',
      icon: '👥',
      description: 'User management with encrypted data and role badges',
      requiredRole: 'admin',
      path: '/users'
    },
    {
      id: 'roles',
      label: 'Roles',
      icon: '🔐',
      description: 'Role-based access control (RBAC) management interface',
      requiredRole: 'admin',
      path: '/roles'
    },
    {
      id: 'audit-log',
      label: 'Audit Log',
      icon: '📋',
      description: 'Encrypted audit trail with filtering and export functionality',
      requiredRole: 'admin',
      path: '/audit-log'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: '👤',
      description: 'User information display and session management',
      requiredRole: 'guest',
      path: '/profile'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      description: 'Theme selector, auto-refresh intervals, and language settings',
      requiredRole: 'guest',
      path: '/settings'
    }
  ]

  const handleNavigation = (item) => {
    if (!hasPermission(item.requiredRole)) {
      logAuditEvent('SECURITY_VIOLATION', `Unauthorized access attempt to ${item.label}`, 'NAVIGATION', 'RESTRICTED', user?.id, user?.name, false)
      alert('Access denied. Insufficient permissions.')
      return
    }

    logAuditEvent('NAVIGATION', `Navigated to ${item.label}`, 'NAVIGATION', 'PUBLIC', user?.id, user?.name)
    navigate(item.path)
  }

  const getAccessLevel = (requiredRole) => {
    if (!user) return 'denied'
    return hasPermission(requiredRole) ? 'granted' : 'denied'
  }

  return (
    <nav className="drdo-navigation">
      <div className="nav-container">
        <div className="nav-items">
          {navigationItems.map((item) => {
            const accessLevel = getAccessLevel(item.requiredRole)
            const isActive = activeView === item.id
            const isAccessible = accessLevel === 'granted'

            return (
              <div
                key={item.id}
                className={`nav-item ${isActive ? 'active' : ''} ${!isAccessible ? 'disabled' : ''}`}
                onClick={() => isAccessible && handleNavigation(item)}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                title={!isAccessible ? 'Access denied - insufficient permissions' : item.description}
              >
                <div className="nav-item-content">
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                  {!isAccessible && (
                    <span className="access-indicator">🔒</span>
                  )}
                </div>
                
                {hoveredItem === item.id && isAccessible && (
                  <div className="nav-tooltip">
                    <div className="tooltip-content">
                      <strong>{item.label}</strong>
                      <p>{item.description}</p>
                      <span className="required-role">Requires: {item.requiredRole}+ access</span>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Security Status Indicator */}
        <div className="security-status">
          <div className="security-indicator">
            <span className="security-icon">🔐</span>
            <div className="security-info">
              <span className="security-level">
                {user?.clearanceLevel || 'GUEST'}
              </span>
              <span className="security-role">
                {user?.role || 'guest'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navigation
