import React, { useState } from 'react'
import { useAuth } from '../../context/AuthContext'
import { t } from '../../utils/i18n'
import { logAuditEvent } from '../../utils/audit'
import './Navigation.css'

const Navigation = ({ activeView, setActiveView }) => {
  const { user, hasPermission } = useAuth()
  const [hoveredItem, setHoveredItem] = useState(null)

  const navigationItems = [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: '📊',
      description: 'System overview and key metrics',
      requiredRole: 'guest',
      path: '/dashboard'
    },
    {
      id: 'simulation',
      label: t('nav.simulation'),
      icon: '🚀',
      description: 'Simulation testing and analysis',
      requiredRole: 'engineer',
      path: '/simulation-testing'
    },
    {
      id: 'systems',
      label: 'Systems',
      icon: '⚙️',
      description: 'Weapons and equipment monitoring',
      requiredRole: 'engineer',
      path: '/weapons-systems'
    },
    {
      id: 'mission',
      label: 'Mission',
      icon: '🎯',
      description: 'Mission planning and coordination',
      requiredRole: 'scientist',
      path: '/mission-planning'
    },
    {
      id: 'cyber',
      label: 'Cyber Defense',
      icon: '🛡️',
      description: 'Cybersecurity monitoring',
      requiredRole: 'analyst',
      path: '/cyber-defense'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: '📈',
      description: 'Intelligence and analytics',
      requiredRole: 'scientist',
      path: '/analytics-intel'
    },
    {
      id: 'transfer',
      label: 'Transfer',
      icon: '📤',
      description: 'Secure data transfer',
      requiredRole: 'engineer',
      path: '/transfer'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: '👤',
      description: 'User profile and settings',
      requiredRole: 'guest',
      path: '/profile'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      description: 'System configuration',
      requiredRole: 'guest',
      path: '/settings'
    }
  ]

  const handleNavigation = (item) => {
    if (!hasPermission(item.requiredRole)) {
      logAuditEvent('SECURITY_VIOLATION', `Unauthorized access attempt to ${item.label}`, 'NAVIGATION', 'RESTRICTED', user?.id, user?.name, false)
      alert('Access denied. Insufficient permissions.')
      return
    }

    setActiveView(item.id)
    logAuditEvent('NAVIGATION', `Navigated to ${item.label}`, 'NAVIGATION', 'PUBLIC', user?.id, user?.name)
    
    // Update URL without page reload
    window.history.pushState({}, '', item.path)
  }

  const getAccessLevel = (requiredRole) => {
    if (!user) return 'denied'
    return hasPermission(requiredRole) ? 'granted' : 'denied'
  }

  return (
    <nav className="drdo-navigation">
      <div className="nav-container">
        <div className="nav-items">
          {navigationItems.map((item) => {
            const accessLevel = getAccessLevel(item.requiredRole)
            const isActive = activeView === item.id
            const isAccessible = accessLevel === 'granted'

            return (
              <div
                key={item.id}
                className={`nav-item ${isActive ? 'active' : ''} ${!isAccessible ? 'disabled' : ''}`}
                onClick={() => isAccessible && handleNavigation(item)}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                title={!isAccessible ? 'Access denied - insufficient permissions' : item.description}
              >
                <div className="nav-item-content">
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                  {!isAccessible && (
                    <span className="access-indicator">🔒</span>
                  )}
                </div>
                
                {hoveredItem === item.id && isAccessible && (
                  <div className="nav-tooltip">
                    <div className="tooltip-content">
                      <strong>{item.label}</strong>
                      <p>{item.description}</p>
                      <span className="required-role">Requires: {item.requiredRole}+ access</span>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Security Status Indicator */}
        <div className="security-status">
          <div className="security-indicator">
            <span className="security-icon">🔐</span>
            <div className="security-info">
              <span className="security-level">
                {user?.clearanceLevel || 'GUEST'}
              </span>
              <span className="security-role">
                {user?.role || 'guest'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navigation
