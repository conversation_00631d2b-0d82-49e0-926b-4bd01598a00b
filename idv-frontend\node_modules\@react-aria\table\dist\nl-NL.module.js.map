{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,0CAA0C,EAAE,KAAK,UAAU,EAAE;IACzF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,0CAA0C,EAAE,KAAK,UAAU,EAAE;IAC1F,sBAAsB,CAAC,wCAAwC,CAAC;IAChE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,gBAAgB,CAAC;IAC/B,YAAY,CAAC,iBAAiB,CAAC;AACjC", "sources": ["packages/@react-aria/table/intl/nl-NL.json"], "sourcesContent": ["{\n  \"ascending\": \"oplopend\",\n  \"ascendingSort\": \"gesorteerd in oplopende volgorde in kolom {columnName}\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"aflopend\",\n  \"descendingSort\": \"gesorteerd in aflopende volgorde in kolom {columnName}\",\n  \"resizerDescription\": \"Druk op Enter om het formaat te wijzigen\",\n  \"select\": \"Selecteren\",\n  \"selectAll\": \"Alles selecteren\",\n  \"sortable\": \"sorteerbare kolom\"\n}\n"], "names": [], "version": 3, "file": "nl-NL.module.js.map"}