{"mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAgCM,SAAS,0CAAwB,KAAoC,EAAE,KAAoB,EAAE,GAAuC;QAoBpH,uBACD;IApBpB,IAAI,QAAC,IAAI,EAAC,GAAG;IACb,IAAI,gBAAgB,KAAK,KAAK,CAAC,aAAa;IAC5C,4EAA4E;IAC5E,IAAI,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,gCAAU,EAAE;QAAC,GAAG,KAAK;QAAE,WAAW;IAAO,GAAG,OAAO;IAEzE,IAAI,0BAA0B,KAAK,KAAK,CAAC,eAAe,IAAI,MAAM,gBAAgB,CAAC,aAAa,KAAK;IAErG,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,qCAAO,EAAE;QAC1B,YAAY,CAAC,iBAAiB;QAC9B;YACE,MAAM,IAAI,CAAC,KAAK,GAAG;QACrB;aACA;IACF;IAEA,qFAAqF;IACrF,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,yCAAW,EAAE,CAAC,GAAG;IAExC,IAAI,WAAmD;IACvD,IAAI,iBAAiB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,MAAM,MAAK,KAAK,GAAG;IAC9D,IAAI,iBAAgB,yBAAA,MAAM,cAAc,cAApB,6CAAA,uBAAsB,SAAS;IACnD,8CAA8C;IAC9C,IAAI,KAAK,KAAK,CAAC,aAAa,IAAI,CAAC,CAAA,GAAA,+BAAQ,KACvC,WAAW,iBAAiB,gBAAgB;IAG9C,IAAI,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAChE,IAAI;IACJ,IAAI,eAAe;QACjB,kBAAkB,GAAG,gBAAgB,MAAM,CAAC,aAAa;QACzD,wGAAwG;QACxG,IAAI,kBAAkB,iBAAiB,CAAA,GAAA,+BAAQ,KAC7C,kBAAkB,GAAG,gBAAgB,EAAE,EAAE,gBAAgB,MAAM,CAAC,gBAAgB;IAEpF;IAEA,IAAI,mBAAmB,CAAA,GAAA,oCAAa,EAAE;IAEtC,IAAI,qBAAqB,MAAM,UAAU,CAAC,IAAI,KAAK;IACnD,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,sBAAsB,MAAM,gBAAgB,CAAC,UAAU,KAAK,KAAK,GAAG,EACtE,MAAM,gBAAgB,CAAC,aAAa,CAAC;IAEzC,GAAG;QAAC;QAAoB,MAAM,gBAAgB;QAAE,KAAK,GAAG;KAAC;IAEzD,OAAO;QACL,mBAAmB;YACjB,GAAG,CAAA,GAAA,gCAAS,EACV,gBACA,eACA,YACA,kBACA,4DAA4D;YAC5D,qBAAqB;gBAAC,UAAU;YAAE,IAAI,KACvC;YACD,MAAM;YACN,IAAI,CAAA,GAAA,2CAAgB,EAAE,OAAO,KAAK,GAAG;YACrC,gBAAgB,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG;YAClE,aAAa;QACf;IACF;AACF", "sources": ["packages/@react-aria/table/src/useTableColumnHeader.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getColumnHeaderId} from './utils';\nimport {GridNode} from '@react-types/grid';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {isAndroid, mergeProps, useDescription} from '@react-aria/utils';\nimport {TableState} from '@react-stately/table';\nimport {useEffect} from 'react';\nimport {useFocusable, usePress} from '@react-aria/interactions';\nimport {useGridCell} from '@react-aria/grid';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaTableColumnHeaderProps<T> {\n  /** An object representing the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader). Contains all the relevant information that makes up the column header. */\n  node: GridNode<T>,\n  /** Whether the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader) is contained in a virtual scroller. */\n  isVirtualized?: boolean\n}\n\nexport interface TableColumnHeaderAria {\n  /** Props for the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader) element. */\n  columnHeaderProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a column header in a table.\n * @param props - Props for the column header.\n * @param state - State of the table, as returned by `useTableState`.\n * @param ref - The ref attached to the column header element.\n */\nexport function useTableColumnHeader<T>(props: AriaTableColumnHeaderProps<T>, state: TableState<T>, ref: RefObject<FocusableElement | null>): TableColumnHeaderAria {\n  let {node} = props;\n  let allowsSorting = node.props.allowsSorting;\n  // if there are no focusable children, the column header will focus the cell\n  let {gridCellProps} = useGridCell({...props, focusMode: 'child'}, state, ref);\n\n  let isSelectionCellDisabled = node.props.isSelectionCell && state.selectionManager.selectionMode === 'single';\n\n  let {pressProps} = usePress({\n    isDisabled: !allowsSorting || isSelectionCellDisabled,\n    onPress() {\n      state.sort(node.key);\n    },\n    ref\n  });\n\n  // Needed to pick up the focusable context, enabling things like Tooltips for example\n  let {focusableProps} = useFocusable({}, ref);\n\n  let ariaSort: DOMAttributes['aria-sort'] | undefined = undefined;\n  let isSortedColumn = state.sortDescriptor?.column === node.key;\n  let sortDirection = state.sortDescriptor?.direction;\n  // aria-sort not supported in Android Talkback\n  if (node.props.allowsSorting && !isAndroid()) {\n    ariaSort = isSortedColumn ? sortDirection : 'none';\n  }\n\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n  let sortDescription;\n  if (allowsSorting) {\n    sortDescription = `${stringFormatter.format('sortable')}`;\n    // Android Talkback doesn't support aria-sort so we add sort order details to the aria-described by here\n    if (isSortedColumn && sortDirection && isAndroid()) {\n      sortDescription = `${sortDescription}, ${stringFormatter.format(sortDirection)}`;\n    }\n  }\n\n  let descriptionProps = useDescription(sortDescription);\n\n  let shouldDisableFocus = state.collection.size === 0;\n  useEffect(() => {\n    if (shouldDisableFocus && state.selectionManager.focusedKey === node.key) {\n      state.selectionManager.setFocusedKey(null);\n    }\n  }, [shouldDisableFocus, state.selectionManager, node.key]);\n\n  return {\n    columnHeaderProps: {\n      ...mergeProps(\n        focusableProps,\n        gridCellProps,\n        pressProps,\n        descriptionProps,\n        // If the table is empty, make all column headers untabbable\n        shouldDisableFocus ? {tabIndex: -1} : null\n      ),\n      role: 'columnheader',\n      id: getColumnHeaderId(state, node.key),\n      'aria-colspan': node.colSpan && node.colSpan > 1 ? node.colSpan : undefined,\n      'aria-sort': ariaSort\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useTableColumnHeader.main.js.map"}