import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import { encryptData, decryptData } from '../../utils/encryption'
import './Users.css'

const Users = () => {
  const { user, hasPermission } = useAuth()
  const [users, setUsers] = useState([])
  const [filteredUsers, setFilteredUsers] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddUser, setShowAddUser] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)

  // Mock users data with encryption
  const mockUsers = [
    {
      id: '1',
      name: 'Dr. <PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'scientist',
      department: 'Aeronautics',
      clearanceLevel: 'SECRET',
      status: 'active',
      lastLogin: '2025-01-08T10:30:00Z',
      avatar: '👩‍🔬',
      phone: '+91-9876543210',
      location: 'Bangalore'
    },
    {
      id: '2',
      name: 'Col. Sharma',
      email: '<EMAIL>',
      role: 'commander',
      department: 'Strategic Command',
      clearanceLevel: 'SECRET',
      status: 'active',
      lastLogin: '2025-01-08T09:15:00Z',
      avatar: '🎖️',
      phone: '+91-9876543211',
      location: 'Delhi'
    },
    {
      id: '3',
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      role: 'engineer',
      department: 'Avionics',
      clearanceLevel: 'RESTRICTED',
      status: 'active',
      lastLogin: '2025-01-08T08:45:00Z',
      avatar: '👨‍🔧',
      phone: '+91-9876543212',
      location: 'Hyderabad'
    },
    {
      id: '4',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT Administration',
      clearanceLevel: 'TOP_SECRET',
      status: 'active',
      lastLogin: '2025-01-08T11:00:00Z',
      avatar: '👑',
      phone: '+91-9876543213',
      location: 'Delhi'
    },
    {
      id: '5',
      name: 'Analyst Singh',
      email: '<EMAIL>',
      role: 'analyst',
      department: 'Intelligence',
      clearanceLevel: 'CONFIDENTIAL',
      status: 'inactive',
      lastLogin: '2025-01-07T16:30:00Z',
      avatar: '📊',
      phone: '+91-9876543214',
      location: 'Chennai'
    }
  ]

  useEffect(() => {
    // Encrypt and store user data
    const encryptedUsers = mockUsers.map(userData => ({
      ...userData,
      encryptedData: encryptData(JSON.stringify({
        phone: userData.phone,
        personalInfo: `${userData.name} - ${userData.department}`
      }))
    }))
    
    setUsers(encryptedUsers)
    setFilteredUsers(encryptedUsers)
    
    logAuditEvent('USER_MANAGEMENT_VIEW', 'Users page accessed', 'USER_MANAGEMENT', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  useEffect(() => {
    let filtered = users

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(userData => 
        userData.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        userData.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        userData.department.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(userData => userData.role === roleFilter)
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(userData => userData.status === statusFilter)
    }

    setFilteredUsers(filtered)
  }, [users, searchTerm, roleFilter, statusFilter])

  const getRoleBadgeColor = (role) => {
    const colors = {
      admin: '#7C3AED',
      commander: '#DC2626',
      scientist: '#059669',
      engineer: '#F59E0B',
      analyst: '#3B82F6',
      guest: '#64748B'
    }
    return colors[role] || '#64748B'
  }

  const getClearanceBadgeColor = (level) => {
    const colors = {
      'TOP_SECRET': '#DC2626',
      'SECRET': '#F59E0B',
      'CONFIDENTIAL': '#3B82F6',
      'RESTRICTED': '#059669',
      'PUBLIC': '#64748B'
    }
    return colors[level] || '#64748B'
  }

  const formatLastLogin = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours}h ago`
    return date.toLocaleDateString()
  }

  const handleViewUser = (userData) => {
    setSelectedUser(userData)
    logAuditEvent('USER_VIEW', `Viewed user: ${userData.name}`, 'USER_MANAGEMENT', 'RESTRICTED', user?.id, user?.name)
  }

  const handleDecryptData = (userData) => {
    try {
      const decrypted = decryptData(userData.encryptedData)
      const parsedData = JSON.parse(decrypted)
      alert(`Decrypted Data:\nPhone: ${parsedData.phone}\nInfo: ${parsedData.personalInfo}`)
      logAuditEvent('DATA_DECRYPT', `Decrypted data for user: ${userData.name}`, 'USER_MANAGEMENT', 'CONFIDENTIAL', user?.id, user?.name)
    } catch (error) {
      alert('Failed to decrypt data')
    }
  }

  if (!hasPermission('admin')) {
    return (
      <div className="users-page">
        <div className="access-denied">
          <h2>🔒 Access Denied</h2>
          <p>You need administrator privileges to access user management.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="users-page">
      <div className="users-header">
        <h1 className="users-title">👥 User Management</h1>
        <p className="users-subtitle">Manage DRDO personnel with encrypted data and role-based access</p>
      </div>

      <div className="users-content">
        {/* Filters and Search */}
        <div className="users-controls">
          <div className="search-section">
            <input
              type="text"
              placeholder="Search users by name, email, or department..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="filter-section">
            <select 
              value={roleFilter} 
              onChange={(e) => setRoleFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="commander">Commander</option>
              <option value="scientist">Scientist</option>
              <option value="engineer">Engineer</option>
              <option value="analyst">Analyst</option>
            </select>
            
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <button 
            onClick={() => setShowAddUser(true)}
            className="btn-add-user"
          >
            ➕ Add User
          </button>
        </div>

        {/* Users Grid */}
        <div className="users-grid">
          {filteredUsers.map(userData => (
            <div key={userData.id} className={`user-card ${userData.status}`}>
              <div className="user-header">
                <div className="user-avatar">
                  {userData.avatar}
                </div>
                <div className="user-basic-info">
                  <h3 className="user-name">{userData.name}</h3>
                  <p className="user-email">{userData.email}</p>
                </div>
                <div className={`status-indicator ${userData.status}`}>
                  {userData.status === 'active' ? '🟢' : '🔴'}
                </div>
              </div>

              <div className="user-details">
                <div className="user-badges">
                  <span 
                    className="role-badge" 
                    style={{ backgroundColor: getRoleBadgeColor(userData.role) }}
                  >
                    {userData.role.toUpperCase()}
                  </span>
                  <span 
                    className="clearance-badge"
                    style={{ backgroundColor: getClearanceBadgeColor(userData.clearanceLevel) }}
                  >
                    {userData.clearanceLevel}
                  </span>
                </div>
                
                <div className="user-info">
                  <div className="info-item">
                    <span className="info-label">Department:</span>
                    <span className="info-value">{userData.department}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">Location:</span>
                    <span className="info-value">{userData.location}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">Last Login:</span>
                    <span className="info-value">{formatLastLogin(userData.lastLogin)}</span>
                  </div>
                </div>
              </div>

              <div className="user-actions">
                <button 
                  onClick={() => handleViewUser(userData)}
                  className="btn-view"
                >
                  👁️ View
                </button>
                <button 
                  onClick={() => handleDecryptData(userData)}
                  className="btn-decrypt"
                >
                  🔓 Decrypt
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="no-users">
            <h3>No users found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>

      {/* User Details Modal */}
      {selectedUser && (
        <div className="modal-overlay" onClick={() => setSelectedUser(null)}>
          <div className="user-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>User Details</h2>
              <button onClick={() => setSelectedUser(null)} className="modal-close">✕</button>
            </div>
            <div className="modal-content">
              <div className="user-detail-grid">
                <div className="detail-item">
                  <label>Name:</label>
                  <span>{selectedUser.name}</span>
                </div>
                <div className="detail-item">
                  <label>Email:</label>
                  <span>{selectedUser.email}</span>
                </div>
                <div className="detail-item">
                  <label>Role:</label>
                  <span>{selectedUser.role}</span>
                </div>
                <div className="detail-item">
                  <label>Department:</label>
                  <span>{selectedUser.department}</span>
                </div>
                <div className="detail-item">
                  <label>Clearance:</label>
                  <span>{selectedUser.clearanceLevel}</span>
                </div>
                <div className="detail-item">
                  <label>Status:</label>
                  <span>{selectedUser.status}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Users Footer */}
      <div className="users-footer">
        <div className="footer-stats">
          <span>Total Users: {users.length}</span>
          <span>Active: {users.filter(u => u.status === 'active').length}</span>
          <span>Filtered Results: {filteredUsers.length}</span>
        </div>
      </div>
    </div>
  )
}

export default Users
