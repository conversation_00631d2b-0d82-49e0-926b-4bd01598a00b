import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import './Roles.css'

const Roles = () => {
  const { user, hasPermission } = useAuth()
  const [roles, setRoles] = useState([])
  const [permissions, setPermissions] = useState([])

  const mockRoles = [
    {
      id: 'admin',
      name: 'Administrator',
      description: 'Full system access and user management',
      permissions: ['USER_MANAGEMENT', 'SYSTEM_CONFIG', 'AUDIT_ACCESS', 'DATA_EXPORT', 'ROLE_MANAGEMENT'],
      userCount: 2,
      color: '#7C3AED'
    },
    {
      id: 'commander',
      name: 'Commander',
      description: 'Strategic command and mission oversight',
      permissions: ['MISSION_CONTROL', 'EQUIPMENT_MONITOR', 'STRATEGIC_VIEW', 'PERSONNEL_MANAGE'],
      userCount: 5,
      color: '#DC2626'
    },
    {
      id: 'scientist',
      name: 'Scientist',
      description: 'Research and development access',
      permissions: ['RESEARCH_ACCESS', 'DATA_ANALYSIS', 'SIMULATION_RUN', 'REPORT_GENERATE'],
      userCount: 15,
      color: '#059669'
    },
    {
      id: 'engineer',
      name: 'Engineer',
      description: 'Technical systems and equipment management',
      permissions: ['EQUIPMENT_CONTROL', 'SYSTEM_MONITOR', 'DATA_TRANSFER', 'MAINTENANCE_LOG'],
      userCount: 12,
      color: '#F59E0B'
    },
    {
      id: 'analyst',
      name: 'Analyst',
      description: 'Data analysis and intelligence gathering',
      permissions: ['DATA_VIEW', 'REPORT_VIEW', 'ANALYTICS_ACCESS', 'INTELLIGENCE_READ'],
      userCount: 8,
      color: '#3B82F6'
    },
    {
      id: 'guest',
      name: 'Guest',
      description: 'Limited read-only access',
      permissions: ['BASIC_VIEW', 'PUBLIC_DATA'],
      userCount: 3,
      color: '#64748B'
    }
  ]

  const allPermissions = [
    { id: 'USER_MANAGEMENT', name: 'User Management', category: 'Administration' },
    { id: 'SYSTEM_CONFIG', name: 'System Configuration', category: 'Administration' },
    { id: 'AUDIT_ACCESS', name: 'Audit Log Access', category: 'Administration' },
    { id: 'ROLE_MANAGEMENT', name: 'Role Management', category: 'Administration' },
    { id: 'MISSION_CONTROL', name: 'Mission Control', category: 'Operations' },
    { id: 'EQUIPMENT_MONITOR', name: 'Equipment Monitoring', category: 'Operations' },
    { id: 'STRATEGIC_VIEW', name: 'Strategic Overview', category: 'Operations' },
    { id: 'PERSONNEL_MANAGE', name: 'Personnel Management', category: 'Operations' },
    { id: 'RESEARCH_ACCESS', name: 'Research Access', category: 'Research' },
    { id: 'DATA_ANALYSIS', name: 'Data Analysis', category: 'Research' },
    { id: 'SIMULATION_RUN', name: 'Simulation Execution', category: 'Research' },
    { id: 'REPORT_GENERATE', name: 'Report Generation', category: 'Research' },
    { id: 'EQUIPMENT_CONTROL', name: 'Equipment Control', category: 'Technical' },
    { id: 'SYSTEM_MONITOR', name: 'System Monitoring', category: 'Technical' },
    { id: 'DATA_TRANSFER', name: 'Data Transfer', category: 'Technical' },
    { id: 'MAINTENANCE_LOG', name: 'Maintenance Logging', category: 'Technical' },
    { id: 'DATA_VIEW', name: 'Data Viewing', category: 'Analysis' },
    { id: 'REPORT_VIEW', name: 'Report Viewing', category: 'Analysis' },
    { id: 'ANALYTICS_ACCESS', name: 'Analytics Access', category: 'Analysis' },
    { id: 'INTELLIGENCE_READ', name: 'Intelligence Reading', category: 'Analysis' },
    { id: 'BASIC_VIEW', name: 'Basic Viewing', category: 'General' },
    { id: 'PUBLIC_DATA', name: 'Public Data Access', category: 'General' },
    { id: 'DATA_EXPORT', name: 'Data Export', category: 'General' }
  ]

  useEffect(() => {
    setRoles(mockRoles)
    setPermissions(allPermissions)
    logAuditEvent('ROLE_MANAGEMENT_VIEW', 'Role management page accessed', 'ROLE_MANAGEMENT', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  const getPermissionsByCategory = () => {
    const categories = {}
    permissions.forEach(permission => {
      if (!categories[permission.category]) {
        categories[permission.category] = []
      }
      categories[permission.category].push(permission)
    })
    return categories
  }

  if (!hasPermission('admin')) {
    return (
      <div className="roles-page">
        <div className="access-denied">
          <h2>🔒 Access Denied</h2>
          <p>You need administrator privileges to access role management.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="roles-page">
      <div className="roles-header">
        <h1 className="roles-title">🔐 Role Management</h1>
        <p className="roles-subtitle">Manage role-based access control (RBAC) for DRDO personnel</p>
      </div>

      <div className="roles-content">
        {/* Roles Overview */}
        <div className="roles-overview">
          <h2 className="section-title">👥 System Roles</h2>
          <div className="roles-grid">
            {roles.map(role => (
              <div key={role.id} className="role-card">
                <div className="role-header">
                  <div className="role-icon" style={{ backgroundColor: role.color }}>
                    {role.name.charAt(0)}
                  </div>
                  <div className="role-info">
                    <h3 className="role-name">{role.name}</h3>
                    <p className="role-description">{role.description}</p>
                  </div>
                  <div className="role-stats">
                    <span className="user-count">{role.userCount} users</span>
                  </div>
                </div>
                
                <div className="role-permissions">
                  <h4>Permissions ({role.permissions.length})</h4>
                  <div className="permission-tags">
                    {role.permissions.slice(0, 3).map(permId => {
                      const perm = permissions.find(p => p.id === permId)
                      return perm ? (
                        <span key={permId} className="permission-tag">
                          {perm.name}
                        </span>
                      ) : null
                    })}
                    {role.permissions.length > 3 && (
                      <span className="permission-tag more">
                        +{role.permissions.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Permissions Matrix */}
        <div className="permissions-matrix">
          <h2 className="section-title">🔑 Permissions Matrix</h2>
          <div className="matrix-container">
            <div className="matrix-header">
              <div className="matrix-cell header">Permission</div>
              {roles.map(role => (
                <div key={role.id} className="matrix-cell header role-header" style={{ backgroundColor: role.color }}>
                  {role.name}
                </div>
              ))}
            </div>
            
            {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
              <div key={category} className="permission-category">
                <div className="category-header">{category}</div>
                {categoryPermissions.map(permission => (
                  <div key={permission.id} className="matrix-row">
                    <div className="matrix-cell permission-name">
                      {permission.name}
                    </div>
                    {roles.map(role => (
                      <div key={`${role.id}-${permission.id}`} className="matrix-cell permission-cell">
                        {role.permissions.includes(permission.id) ? (
                          <span className="permission-granted">✓</span>
                        ) : (
                          <span className="permission-denied">✗</span>
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Role Hierarchy */}
        <div className="role-hierarchy">
          <h2 className="section-title">📊 Role Hierarchy</h2>
          <div className="hierarchy-chart">
            <div className="hierarchy-level">
              <div className="hierarchy-role admin">
                <span>Administrator</span>
                <small>Full System Access</small>
              </div>
            </div>
            <div className="hierarchy-level">
              <div className="hierarchy-role commander">
                <span>Commander</span>
                <small>Strategic Control</small>
              </div>
            </div>
            <div className="hierarchy-level">
              <div className="hierarchy-role scientist">
                <span>Scientist</span>
                <small>Research & Development</small>
              </div>
              <div className="hierarchy-role engineer">
                <span>Engineer</span>
                <small>Technical Systems</small>
              </div>
            </div>
            <div className="hierarchy-level">
              <div className="hierarchy-role analyst">
                <span>Analyst</span>
                <small>Data Analysis</small>
              </div>
            </div>
            <div className="hierarchy-level">
              <div className="hierarchy-role guest">
                <span>Guest</span>
                <small>Limited Access</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Roles Footer */}
      <div className="roles-footer">
        <div className="footer-info">
          <span>Total Roles: {roles.length}</span>
          <span>Total Permissions: {permissions.length}</span>
          <span>Total Users: {roles.reduce((sum, role) => sum + role.userCount, 0)}</span>
        </div>
      </div>
    </div>
  )
}

export default Roles
