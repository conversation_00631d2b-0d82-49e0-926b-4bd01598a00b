import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { logAuditEvent } from '../../utils/audit'
import './Profile.css'

const Profile = () => {
  const { user, logout, sessionTimeLeft } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    department: '',
    phone: '',
    location: '',
    bio: '',
    preferences: {
      emailNotifications: true,
      smsNotifications: false,
      dashboardLayout: 'default'
    }
  })

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name,
        email: user.email,
        department: user.department,
        phone: user.phone || '',
        location: user.location || '',
        bio: user.bio || '',
        preferences: user.preferences || {
          emailNotifications: true,
          smsNotifications: false,
          dashboardLayout: 'default'
        }
      })
    }

    logAuditEvent('PROFILE_VIEW', 'Profile page accessed', 'PROFILE', 'RESTRICTED', user?.id, user?.name)
  }, [user])

  const handleSave = () => {
    // In a real app, this would make an API call
    logAuditEvent('PROFILE_UPDATE', 'Profile information updated', 'PROFILE', 'RESTRICTED', user?.id, user?.name)
    setIsEditing(false)

    // Professional notification
    const notification = document.createElement('div')
    notification.className = 'profile-notification success'
    notification.innerHTML = '✅ Profile updated successfully!'
    document.body.appendChild(notification)

    setTimeout(() => {
      notification.remove()
    }, 3000)
  }

  const handleCancel = () => {
    // Reset to original data
    if (user) {
      setProfileData({
        name: user.name,
        email: user.email,
        department: user.department,
        phone: user.phone || '',
        location: user.location || '',
        bio: user.bio || '',
        preferences: user.preferences || {
          emailNotifications: true,
          smsNotifications: false,
          dashboardLayout: 'default'
        }
      })
    }
    setIsEditing(false)
  }

  const formatSessionTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getRoleBadgeColor = (role) => {
    const colors = {
      admin: '#7C3AED',
      commander: '#DC2626',
      scientist: '#059669',
      engineer: '#F59E0B',
      analyst: '#3B82F6',
      guest: '#64748B'
    }
    return colors[role] || '#64748B'
  }

  if (!user) {
    return (
      <div className="profile-page">
        <div className="profile-error">
          <h2>Access Denied</h2>
          <p>Please log in to view your profile.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="profile-page">
      <div className="profile-header">
        <h1 className="profile-title">👤 User Profile</h1>
        <p className="profile-subtitle">Manage your account information and preferences</p>
      </div>

      <div className="profile-content">
        {/* Profile Card */}
        <div className="profile-card">
          <div className="profile-avatar-section">
            <div className="profile-avatar">
              {user.avatar || '👤'}
            </div>
            <div className="profile-basic-info">
              <h2 className="profile-name">{user.name}</h2>
              <div className="profile-role" style={{ backgroundColor: getRoleBadgeColor(user.role) }}>
                {user.role.toUpperCase()}
              </div>
              <div className="profile-clearance">
                🔒 {user.clearanceLevel}
              </div>
            </div>
          </div>

          <div className="profile-details">
            <div className="detail-section">
              <h3>Contact Information</h3>
              <div className="detail-grid">
                <div className="detail-item">
                  <label>Email</label>
                  {isEditing ? (
                    <input 
                      type="email" 
                      value={profileData.email} 
                      onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                      className="profile-input"
                    />
                  ) : (
                    <span>{profileData.email}</span>
                  )}
                </div>
                <div className="detail-item">
                  <label>Phone</label>
                  {isEditing ? (
                    <input 
                      type="tel" 
                      value={profileData.phone} 
                      onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                      className="profile-input"
                      placeholder="Enter phone number"
                    />
                  ) : (
                    <span>{profileData.phone || 'Not provided'}</span>
                  )}
                </div>
                <div className="detail-item">
                  <label>Department</label>
                  {isEditing ? (
                    <input 
                      type="text" 
                      value={profileData.department} 
                      onChange={(e) => setProfileData({...profileData, department: e.target.value})}
                      className="profile-input"
                    />
                  ) : (
                    <span>{profileData.department}</span>
                  )}
                </div>
                <div className="detail-item">
                  <label>Location</label>
                  {isEditing ? (
                    <input 
                      type="text" 
                      value={profileData.location} 
                      onChange={(e) => setProfileData({...profileData, location: e.target.value})}
                      className="profile-input"
                      placeholder="Enter location"
                    />
                  ) : (
                    <span>{profileData.location || 'Not provided'}</span>
                  )}
                </div>
              </div>
            </div>

            <div className="detail-section">
              <h3>Bio</h3>
              {isEditing ? (
                <textarea 
                  value={profileData.bio} 
                  onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                  className="profile-textarea"
                  placeholder="Tell us about yourself..."
                  rows="4"
                />
              ) : (
                <p className="profile-bio">{profileData.bio || 'No bio provided'}</p>
              )}
            </div>

            <div className="detail-section">
              <h3>Preferences</h3>
              <div className="preferences-grid">
                <label className="preference-item">
                  <input 
                    type="checkbox" 
                    checked={profileData.preferences.emailNotifications}
                    onChange={(e) => setProfileData({
                      ...profileData, 
                      preferences: {...profileData.preferences, emailNotifications: e.target.checked}
                    })}
                    disabled={!isEditing}
                  />
                  Email Notifications
                </label>
                <label className="preference-item">
                  <input 
                    type="checkbox" 
                    checked={profileData.preferences.smsNotifications}
                    onChange={(e) => setProfileData({
                      ...profileData, 
                      preferences: {...profileData.preferences, smsNotifications: e.target.checked}
                    })}
                    disabled={!isEditing}
                  />
                  SMS Notifications
                </label>
              </div>
            </div>
          </div>

          <div className="profile-actions">
            {isEditing ? (
              <div className="edit-actions">
                <button onClick={handleSave} className="btn-save">
                  💾 Save Changes
                </button>
                <button onClick={handleCancel} className="btn-cancel">
                  ❌ Cancel
                </button>
              </div>
            ) : (
              <button onClick={() => setIsEditing(true)} className="btn-edit">
                ✏️ Edit Profile
              </button>
            )}
          </div>
        </div>

        {/* Session Information */}
        <div className="session-card">
          <h3>🕒 Session Information</h3>
          <div className="session-details">
            <div className="session-item">
              <span className="session-label">Session Time Remaining:</span>
              <span className="session-value">{formatSessionTime(sessionTimeLeft)}</span>
            </div>
            <div className="session-item">
              <span className="session-label">User ID:</span>
              <span className="session-value">{user.id}</span>
            </div>
            <div className="session-item">
              <span className="session-label">Last Login:</span>
              <span className="session-value">Today</span>
            </div>
          </div>
          <div className="session-actions">
            <button onClick={logout} className="btn-logout">
              🚪 Logout
            </button>
          </div>
        </div>
      </div>

      {/* Profile Footer */}
      <div className="profile-footer">
        <div className="footer-info">
          <span>Profile last updated: Today</span>
          <span>Account created: 2024</span>
        </div>
      </div>
    </div>
  )
}

export default Profile
