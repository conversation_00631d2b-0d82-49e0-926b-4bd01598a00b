/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'drdo-navy': '#1A3C5E',
        'drdo-blue': '#4A90E2',
        'drdo-orange': '#F5A623',
        'drdo-gray': '#F5F7FA',
        'drdo-white': '#FFFFFF',
        'status-active': '#059669',
        'status-warning': '#F59E0B',
        'status-critical': '#EF4444',
        'text-primary': '#1F2937',
        'text-secondary': '#6B7280',
        'border': '#E5E7EB',
        'surface': '#FFFFFF',
        // Theme variations
        'formal-primary': '#1A3C5E',
        'formal-secondary': '#4A90E2',
        'formal-accent': '#F5A623',
        'formal-bg': '#FFFFFF',
        'formal-surface': '#F5F7FA',
        'minimal-primary': '#374151',
        'minimal-secondary': '#6B7280',
        'minimal-bg': '#F9FAFB',
        'dark-primary': '#1F2937',
        'dark-secondary': '#374151',
        'dark-bg': '#111827',
        'dracula-primary': '#44475A',
        'dracula-secondary': '#6272A4',
        'dracula-accent': '#50FA7B',
        'dracula-bg': '#282A36',
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'roboto': ['Roboto', 'sans-serif'],
        'mono': ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite',
        'bounce-slow': 'bounce 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
