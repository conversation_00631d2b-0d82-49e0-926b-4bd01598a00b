/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'drdo-navy': '#1A3C5E',
        'drdo-blue': '#4A90E2',
        'drdo-orange': '#F5A623',
        'drdo-gray': '#F5F7FA',
        'drdo-white': '#FFFFFF',
        'status-active': '#059669',
        'status-warning': '#F59E0B',
        'status-critical': '#EF4444',
        'text-primary': '#1F2937',
        'text-secondary': '#6B7280',
        'border': '#E5E7EB',
        'surface': '#FFFFFF',
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'roboto': ['Roboto', 'sans-serif'],
        'mono': ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite',
        'bounce-slow': 'bounce 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
