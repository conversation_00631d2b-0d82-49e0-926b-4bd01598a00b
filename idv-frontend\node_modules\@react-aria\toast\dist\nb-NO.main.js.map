{"mappings": "AAAA,iBAAiB;IAAG,SAAS,CAAC,IAAI,CAAC;IACjC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,SAAS,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,QAAQ,CAAC;QAAA,GAAG,CAAC,CAAC;AAC3L", "sources": ["packages/@react-aria/toast/intl/nb-NO.json"], "sourcesContent": ["{\n  \"close\": \"Lukk\",\n  \"notifications\": \"{count, plural, one {# varsling} other {# varsler}}.\"\n}\n"], "names": [], "version": 3, "file": "nb-NO.main.js.map"}