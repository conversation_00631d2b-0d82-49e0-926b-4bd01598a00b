var $4da03c1ecd9a9c7d$exports = {};
$4da03c1ecd9a9c7d$exports = {
    "alpha": `Alfa`,
    "black": `\u{10D}ierna`,
    "blue": `<PERSON><PERSON>\xe1`,
    "blue purple": `mod<PERSON><PERSON><PERSON>\xe1`,
    "brightness": `Jas`,
    "brown": `hned\xe1`,
    "brown yellow": `hnedo\u{17E}lt\xe1`,
    "colorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}`,
    "cyan": `az\xfarov\xe1`,
    "cyan blue": `az\xfarov\xe1 modr\xe1`,
    "dark": `tmav\xe1`,
    "gray": `siv\xe1`,
    "grayish": `sivast\xe1`,
    "green": `Zelen\xe1`,
    "green cyan": `zelen\xe1 az\xfarov\xe1`,
    "hue": `Odtie\u{148}`,
    "light": `svetl\xe1`,
    "lightness": `<PERSON><PERSON><PERSON>\u{165}`,
    "magenta": `purpurov\xe1`,
    "magenta pink": `ru\u{17E}ov\xe1 purpurov\xe1`,
    "orange": `oran\u{17E}ov\xe1`,
    "orange yellow": `oran\u{17E}ovo\u{17E}lt\xe1`,
    "pale": `bled\xe1`,
    "pink": `ru\u{17E}ov\xe1`,
    "pink red": `ru\u{17E}ovo\u{10D}erven\xe1`,
    "purple": `fialov\xe1`,
    "purple magenta": `fialov\xe1 purpurov\xe1`,
    "red": `\u{10C}erven\xe1`,
    "red orange": `\u{10D}ervenooran\u{17E}ov\xe1`,
    "saturation": `S\xfdtos\u{165}`,
    "transparentColorName": (args)=>`${args.lightness} ${args.chroma} ${args.hue}, ${args.percentTransparent} prieh\u{13E}adn\xe1`,
    "very dark": `ve\u{13E}mi tmav\xe1`,
    "very light": `ve\u{13E}mi svetl\xe1`,
    "vibrant": `energick\xe1`,
    "white": `biela`,
    "yellow": `\u{17E}lt\xe1`,
    "yellow green": `\u{17E}ltozelen\xe1`
};


export {$4da03c1ecd9a9c7d$exports as default};
//# sourceMappingURL=sk-SK.module.js.map
