import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type ThemeType = 'formal-white' | 'minimal' | 'dark' | 'dracula';

interface ThemeContextType {
  theme: ThemeType;
  setTheme: (theme: ThemeType) => void;
  animationsEnabled: boolean;
  setAnimationsEnabled: (enabled: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState<ThemeType>('formal-white');
  const [animationsEnabled, setAnimationsEnabled] = useState(true);

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('drdo-theme') as ThemeType;
    const savedAnimations = localStorage.getItem('drdo-animations') === 'true';
    
    if (savedTheme) {
      setTheme(savedTheme);
    }
    setAnimationsEnabled(savedAnimations);
  }, []);

  useEffect(() => {
    // Save theme to localStorage and apply to document
    localStorage.setItem('drdo-theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
    
    // Apply theme-specific classes to body
    document.body.className = getThemeClasses(theme);
  }, [theme]);

  useEffect(() => {
    localStorage.setItem('drdo-animations', animationsEnabled.toString());
    document.documentElement.setAttribute('data-animations', animationsEnabled.toString());
  }, [animationsEnabled]);

  const getThemeClasses = (currentTheme: ThemeType): string => {
    switch (currentTheme) {
      case 'formal-white':
        return 'bg-white text-drdo-navy font-inter';
      case 'minimal':
        return 'bg-gray-50 text-gray-900 font-inter';
      case 'dark':
        return 'bg-gray-900 text-gray-100 font-inter';
      case 'dracula':
        return 'bg-purple-900 text-green-400 font-mono';
      default:
        return 'bg-white text-drdo-navy font-inter';
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, animationsEnabled, setAnimationsEnabled }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const themeConfig = {
  'formal-white': {
    name: 'Formal White',
    description: 'DRDO official styling with navy blue and saffron',
    primary: '#1A3C5E',        // DRDO Navy Blue
    secondary: '#4A90E2',      // Light Blue
    accent: '#FF9933',         // Saffron/Orange
    background: '#FFFFFF',     // Pure White
    surface: '#F8FAFC',        // Light Gray Surface
    text: '#1A3C5E',          // Navy Text
    border: '#E2E8F0',        // Light Border
    success: '#059669',        // Green
    warning: '#F59E0B',        // Amber
    error: '#DC2626',          // Red
  },
  'minimal': {
    name: 'Minimal',
    description: 'Clean gray and white design',
    primary: '#374151',
    secondary: '#6B7280',
    accent: '#9CA3AF',
    background: '#F9FAFB',
    surface: '#FFFFFF',
    text: '#111827',
  },
  'dark': {
    name: 'Dark',
    description: 'Deep navy with elegant panels',
    primary: '#1F2937',
    secondary: '#374151',
    accent: '#60A5FA',
    background: '#111827',
    surface: '#1F2937',
    text: '#F9FAFB',
  },
  'dracula': {
    name: 'Dracula',
    description: 'Matrix-style neon theme',
    primary: '#44475A',
    secondary: '#6272A4',
    accent: '#50FA7B',
    background: '#282A36',
    surface: '#44475A',
    text: '#F8F8F2',
  },
};
