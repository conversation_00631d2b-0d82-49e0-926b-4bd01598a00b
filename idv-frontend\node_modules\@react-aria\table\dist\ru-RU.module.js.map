{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,6EAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,gIAAoB,EAAE,KAAK,UAAU,CAAC,wIAAsB,CAAC;IACzF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,8BAAM,CAAC;IAC7C,cAAc,CAAC,wDAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,gIAAoB,EAAE,KAAK,UAAU,CAAC,mHAAmB,CAAC;IACvF,sBAAsB,CAAC,mSAAmD,CAAC;IAC3E,UAAU,CAAC,iDAAO,CAAC;IACnB,aAAa,CAAC,uEAAW,CAAC;IAC1B,YAAY,CAAC,+HAAmB,CAAC;AACnC", "sources": ["packages/@react-aria/table/intl/ru-RU.json"], "sourcesContent": ["{\n  \"ascending\": \"возрастание\",\n  \"ascendingSort\": \"сортировать столбец {columnName} в порядке возрастания\",\n  \"columnSize\": \"{value} пикс.\",\n  \"descending\": \"убывание\",\n  \"descendingSort\": \"сортировать столбец {columnName} в порядке убывания\",\n  \"resizerDescription\": \"Нажмите клавишу Enter для начала изменения размеров\",\n  \"select\": \"Выбрать\",\n  \"selectAll\": \"Выбрать все\",\n  \"sortable\": \"сортируемый столбец\"\n}\n"], "names": [], "version": 3, "file": "ru-RU.module.js.map"}