/* Data Transfer Page - DRDO v1.7 */
.data-transfer-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.transfer-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.transfer-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.transfer-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.transfer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 24px 0;
}

/* Upload Section */
.upload-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.upload-zone {
  border: 2px dashed #E2E8F0;
  border-radius: 12px;
  padding: 48px 32px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.upload-zone:hover,
.upload-zone.drag-active {
  border-color: #4A90E2;
  background: #F8FAFC;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #64748B;
}

.upload-content h3 {
  font-size: 20px;
  color: #1A3C5E;
  margin: 0;
}

.upload-content p {
  color: #64748B;
  margin: 0;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-info {
  margin-top: 16px;
}

.upload-info p {
  font-size: 12px;
  color: #64748B;
  margin: 4px 0;
}

/* Selected Files */
.selected-files {
  margin-top: 24px;
}

.selected-files h3 {
  font-size: 16px;
  color: #1A3C5E;
  margin-bottom: 16px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

.file-size {
  font-size: 12px;
  color: #64748B;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  width: 100px;
  height: 6px;
  background: #F1F5F9;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2, #FF9933);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.remove-file {
  background: none;
  border: none;
  color: #DC2626;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

/* Encryption Section */
.encryption-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.encryption-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.encryption-option {
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.encryption-option:hover {
  border-color: #4A90E2;
}

.encryption-option.selected {
  border-color: #FF9933;
  background: #FFF7ED;
}

.option-header {
  margin-bottom: 8px;
}

.encryption-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.option-description {
  font-size: 14px;
  color: #64748B;
  margin: 0;
}

/* Preview Section */
.preview-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.preview-card {
  border: 1px solid #F1F5F9;
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  background: #F8FAFC;
  padding: 16px;
  border-bottom: 1px solid #F1F5F9;
}

.preview-header h3 {
  font-size: 16px;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.preview-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #64748B;
}

.preview-content {
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.preview-content pre {
  font-size: 12px;
  color: #1A3C5E;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Transfer Controls */
.transfer-controls {
  text-align: center;
  margin-bottom: 32px;
}

.btn-transfer {
  padding: 16px 32px;
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-transfer:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

.btn-transfer:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* History Section */
.history-section {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
}

.history-info {
  flex: 1;
}

.history-filename {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.history-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #64748B;
}

.history-badges {
  display: flex;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

/* Access Denied */
.access-denied {
  text-align: center;
  padding: 80px 32px;
}

.access-denied h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.access-denied p {
  color: #64748B;
  font-size: 16px;
}

/* Transfer Footer */
.transfer-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-info {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Responsive Design */
@media (max-width: 768px) {
  .transfer-content {
    padding: 24px 16px;
  }
  
  .upload-section,
  .encryption-section,
  .preview-section,
  .history-section {
    padding: 24px 16px;
  }
  
  .encryption-options {
    grid-template-columns: 1fr;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .history-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.upload-section,
.encryption-section,
.preview-section,
.history-section {
  animation: fadeIn 0.5s ease-out;
}
