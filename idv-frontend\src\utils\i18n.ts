export type Language = 'en' | 'hi' | 'ta' | 'te';

export interface TranslationKeys {
  // Navigation
  'nav.dashboard': string;
  'nav.simulation': string;
  'nav.systems': string;
  'nav.mission': string;
  'nav.cyber': string;
  'nav.analytics': string;
  'nav.transfer': string;
  'nav.profile': string;
  'nav.settings': string;
  
  // Common
  'common.loading': string;
  'common.error': string;
  'common.success': string;
  'common.save': string;
  'common.cancel': string;
  'common.delete': string;
  'common.edit': string;
  'common.view': string;
  'common.export': string;
  'common.import': string;
  'common.search': string;
  'common.filter': string;
  'common.refresh': string;
  
  // Dashboard
  'dashboard.title': string;
  'dashboard.overview': string;
  'dashboard.alerts': string;
  'dashboard.status': string;
  'dashboard.performance': string;
  
  // Security
  'security.classification': string;
  'security.public': string;
  'security.restricted': string;
  'security.confidential': string;
  'security.secret': string;
  'security.unauthorized': string;
  'security.accessDenied': string;
  
  // Profile
  'profile.name': string;
  'profile.role': string;
  'profile.department': string;
  'profile.clearance': string;
  'profile.logout': string;
  'profile.switchRole': string;
  
  // Settings
  'settings.theme': string;
  'settings.language': string;
  'settings.animations': string;
  'settings.notifications': string;
  'settings.privacy': string;
}

const translations: Record<Language, TranslationKeys> = {
  en: {
    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.simulation': 'Simulation',
    'nav.systems': 'Systems',
    'nav.mission': 'Mission',
    'nav.cyber': 'Cyber Defense',
    'nav.analytics': 'Analytics',
    'nav.transfer': 'Data Transfer',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.export': 'Export',
    'common.import': 'Import',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.refresh': 'Refresh',
    
    // Dashboard
    'dashboard.title': 'DRDO Interactive Dashboard',
    'dashboard.overview': 'System Overview',
    'dashboard.alerts': 'Active Alerts',
    'dashboard.status': 'System Status',
    'dashboard.performance': 'Performance Metrics',
    
    // Security
    'security.classification': 'Security Classification',
    'security.public': 'Public',
    'security.restricted': 'Restricted',
    'security.confidential': 'Confidential',
    'security.secret': 'Secret',
    'security.unauthorized': 'Unauthorized Access',
    'security.accessDenied': 'Access Denied',
    
    // Profile
    'profile.name': 'Name',
    'profile.role': 'Role',
    'profile.department': 'Department',
    'profile.clearance': 'Security Clearance',
    'profile.logout': 'Logout',
    'profile.switchRole': 'Switch Role',
    
    // Settings
    'settings.theme': 'Theme',
    'settings.language': 'Language',
    'settings.animations': 'Animations',
    'settings.notifications': 'Notifications',
    'settings.privacy': 'Privacy',
  },
  
  hi: {
    // Navigation
    'nav.dashboard': 'डैशबोर्ड',
    'nav.simulation': 'सिमुलेशन',
    'nav.systems': 'सिस्टम',
    'nav.mission': 'मिशन',
    'nav.cyber': 'साइबर रक्षा',
    'nav.analytics': 'विश्लेषण',
    'nav.transfer': 'डेटा स्थानांतरण',
    'nav.profile': 'प्रोफाइल',
    'nav.settings': 'सेटिंग्स',
    
    // Common
    'common.loading': 'लोड हो रहा है...',
    'common.error': 'त्रुटि',
    'common.success': 'सफलता',
    'common.save': 'सेव करें',
    'common.cancel': 'रद्द करें',
    'common.delete': 'हटाएं',
    'common.edit': 'संपादित करें',
    'common.view': 'देखें',
    'common.export': 'निर्यात',
    'common.import': 'आयात',
    'common.search': 'खोजें',
    'common.filter': 'फिल्टर',
    'common.refresh': 'रीफ्रेश',
    
    // Dashboard
    'dashboard.title': 'डीआरडीओ इंटरैक्टिव डैशबोर्ड',
    'dashboard.overview': 'सिस्टम अवलोकन',
    'dashboard.alerts': 'सक्रिय अलर्ट',
    'dashboard.status': 'सिस्टम स्थिति',
    'dashboard.performance': 'प्रदर्शन मेट्रिक्स',
    
    // Security
    'security.classification': 'सुरक्षा वर्गीकरण',
    'security.public': 'सार्वजनिक',
    'security.restricted': 'प्रतिबंधित',
    'security.confidential': 'गोपनीय',
    'security.secret': 'गुप्त',
    'security.unauthorized': 'अनधिकृत पहुंच',
    'security.accessDenied': 'पहुंच अस्वीकृत',
    
    // Profile
    'profile.name': 'नाम',
    'profile.role': 'भूमिका',
    'profile.department': 'विभाग',
    'profile.clearance': 'सुरक्षा मंजूरी',
    'profile.logout': 'लॉगआउट',
    'profile.switchRole': 'भूमिका बदलें',
    
    // Settings
    'settings.theme': 'थीम',
    'settings.language': 'भाषा',
    'settings.animations': 'एनीमेशन',
    'settings.notifications': 'सूचनाएं',
    'settings.privacy': 'गोपनीयता',
  },
  
  ta: {
    // Navigation
    'nav.dashboard': 'டாஷ்போர்டு',
    'nav.simulation': 'உருவகப்படுத்துதல்',
    'nav.systems': 'அமைப்புகள்',
    'nav.mission': 'பணி',
    'nav.cyber': 'இணைய பாதுகாப்பு',
    'nav.analytics': 'பகுப்பாய்வு',
    'nav.transfer': 'தரவு பரிமாற்றம்',
    'nav.profile': 'சுயவிவரம்',
    'nav.settings': 'அமைப்புகள்',
    
    // Common
    'common.loading': 'ஏற்றுகிறது...',
    'common.error': 'பிழை',
    'common.success': 'வெற்றி',
    'common.save': 'சேமி',
    'common.cancel': 'ரத்து செய்',
    'common.delete': 'நீக்கு',
    'common.edit': 'திருத்து',
    'common.view': 'பார்',
    'common.export': 'ஏற்றுமதி',
    'common.import': 'இறக்குமதி',
    'common.search': 'தேடு',
    'common.filter': 'வடிகட்டி',
    'common.refresh': 'புதுப்பி',
    
    // Dashboard
    'dashboard.title': 'டிஆர்டிஓ ஊடாடும் டாஷ்போர்டு',
    'dashboard.overview': 'அமைப்பு கண்ணோட்டம்',
    'dashboard.alerts': 'செயலில் உள்ள எச்சரிக்கைகள்',
    'dashboard.status': 'அமைப்பு நிலை',
    'dashboard.performance': 'செயல்திறன் அளவீடுகள்',
    
    // Security
    'security.classification': 'பாதுகாப்பு வகைப்பாடு',
    'security.public': 'பொது',
    'security.restricted': 'தடைசெய்யப்பட்ட',
    'security.confidential': 'ரகசிய',
    'security.secret': 'மிக ரகசிய',
    'security.unauthorized': 'அங்கீகரிக்கப்படாத அணுகல்',
    'security.accessDenied': 'அணுகல் மறுக்கப்பட்டது',
    
    // Profile
    'profile.name': 'பெயர்',
    'profile.role': 'பங்கு',
    'profile.department': 'துறை',
    'profile.clearance': 'பாதுகாப்பு அனுமதி',
    'profile.logout': 'வெளியேறு',
    'profile.switchRole': 'பங்கை மாற்று',
    
    // Settings
    'settings.theme': 'தீம்',
    'settings.language': 'மொழி',
    'settings.animations': 'அனிமேஷன்கள்',
    'settings.notifications': 'அறிவிப்புகள்',
    'settings.privacy': 'தனியுரிமை',
  },
  
  te: {
    // Navigation
    'nav.dashboard': 'డాష్‌బోర్డ్',
    'nav.simulation': 'అనుకరణ',
    'nav.systems': 'వ్యవస్థలు',
    'nav.mission': 'మిషన్',
    'nav.cyber': 'సైబర్ రక్షణ',
    'nav.analytics': 'విశ్లేషణలు',
    'nav.transfer': 'డేటా బదిలీ',
    'nav.profile': 'ప్రొఫైల్',
    'nav.settings': 'సెట్టింగులు',
    
    // Common
    'common.loading': 'లోడ్ అవుతోంది...',
    'common.error': 'లోపం',
    'common.success': 'విజయం',
    'common.save': 'సేవ్ చేయండి',
    'common.cancel': 'రద్దు చేయండి',
    'common.delete': 'తొలగించండి',
    'common.edit': 'సవరించండి',
    'common.view': 'చూడండి',
    'common.export': 'ఎగుమతి',
    'common.import': 'దిగుమతి',
    'common.search': 'వెతకండి',
    'common.filter': 'ఫిల్టర్',
    'common.refresh': 'రిఫ్రెష్',
    
    // Dashboard
    'dashboard.title': 'డిఆర్డిఓ ఇంటరాక్టివ్ డాష్‌బోర్డ్',
    'dashboard.overview': 'వ్యవస్థ అవలోకనం',
    'dashboard.alerts': 'క్రియాశీల హెచ్చరికలు',
    'dashboard.status': 'వ్యవస్థ స్థితి',
    'dashboard.performance': 'పనితీరు కొలమానలు',
    
    // Security
    'security.classification': 'భద్రతా వర్గీకరణ',
    'security.public': 'ప్రజా',
    'security.restricted': 'పరిమితం',
    'security.confidential': 'గోప్య',
    'security.secret': 'రహస్య',
    'security.unauthorized': 'అనధికార ప్రవేశం',
    'security.accessDenied': 'ప్రవేశం నిరాకరించబడింది',
    
    // Profile
    'profile.name': 'పేరు',
    'profile.role': 'పాత్ర',
    'profile.department': 'విభాగం',
    'profile.clearance': 'భద్రతా అనుమతి',
    'profile.logout': 'లాగ్ అవుట్',
    'profile.switchRole': 'పాత్రను మార్చండి',
    
    // Settings
    'settings.theme': 'థీమ్',
    'settings.language': 'భాష',
    'settings.animations': 'యానిమేషన్లు',
    'settings.notifications': 'నోటిఫికేషన్లు',
    'settings.privacy': 'గోప్యత',
  },
};

class I18nManager {
  private static instance: I18nManager;
  private currentLanguage: Language = 'en';

  public static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager();
    }
    return I18nManager.instance;
  }

  constructor() {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('drdo-language') as Language;
    if (savedLanguage && translations[savedLanguage]) {
      this.currentLanguage = savedLanguage;
    }
  }

  setLanguage(language: Language): void {
    if (translations[language]) {
      this.currentLanguage = language;
      localStorage.setItem('drdo-language', language);
      document.documentElement.setAttribute('lang', language);
    }
  }

  getLanguage(): Language {
    return this.currentLanguage;
  }

  translate(key: keyof TranslationKeys): string {
    return translations[this.currentLanguage][key] || translations.en[key] || key;
  }

  getAvailableLanguages(): { code: Language; name: string; nativeName: string }[] {
    return [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
      { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்' },
      { code: 'te', name: 'Telugu', nativeName: 'తెలుగు' },
    ];
  }
}

// Export singleton instance
export const i18n = I18nManager.getInstance();

// Convenience function
export const t = (key: keyof TranslationKeys): string => i18n.translate(key);
