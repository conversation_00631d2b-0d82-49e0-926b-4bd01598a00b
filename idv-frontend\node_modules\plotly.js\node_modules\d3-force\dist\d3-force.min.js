// https://d3js.org/d3-force/ v1.2.1 Copyright 2019 Mike <PERSON>
!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("d3-quadtree"),require("d3-collection"),require("d3-dispatch"),require("d3-timer")):"function"==typeof define&&define.amd?define(["exports","d3-quadtree","d3-collection","d3-dispatch","d3-timer"],t):t(n.d3=n.d3||{},n.d3,n.d3,n.d3,n.d3)}(this,function(n,t,e,r,i){"use strict";function u(n){return function(){return n}}function o(){return 1e-6*(Math.random()-.5)}function f(n){return n.x+n.vx}function a(n){return n.y+n.vy}function c(n){return n.index}function l(n,t){var e=n.get(t);if(!e)throw new Error("missing: "+t);return e}function h(n){return n.x}function v(n){return n.y}var y=10,d=Math.PI*(3-Math.sqrt(5));n.forceCenter=function(n,t){var e;function r(){var r,i,u=e.length,o=0,f=0;for(r=0;r<u;++r)o+=(i=e[r]).x,f+=i.y;for(o=o/u-n,f=f/u-t,r=0;r<u;++r)(i=e[r]).x-=o,i.y-=f}return null==n&&(n=0),null==t&&(t=0),r.initialize=function(n){e=n},r.x=function(t){return arguments.length?(n=+t,r):n},r.y=function(n){return arguments.length?(t=+n,r):t},r},n.forceCollide=function(n){var e,r,i=1,c=1;function l(){for(var n,u,l,v,y,d,x,g=e.length,s=0;s<c;++s)for(u=t.quadtree(e,f,a).visitAfter(h),n=0;n<g;++n)l=e[n],d=r[l.index],x=d*d,v=l.x+l.vx,y=l.y+l.vy,u.visit(p);function p(n,t,e,r,u){var f=n.data,a=n.r,c=d+a;if(!f)return t>v+c||r<v-c||e>y+c||u<y-c;if(f.index>l.index){var h=v-f.x-f.vx,g=y-f.y-f.vy,s=h*h+g*g;s<c*c&&(0===h&&(s+=(h=o())*h),0===g&&(s+=(g=o())*g),s=(c-(s=Math.sqrt(s)))/s*i,l.vx+=(h*=s)*(c=(a*=a)/(x+a)),l.vy+=(g*=s)*c,f.vx-=h*(c=1-c),f.vy-=g*c)}}}function h(n){if(n.data)return n.r=r[n.data.index];for(var t=n.r=0;t<4;++t)n[t]&&n[t].r>n.r&&(n.r=n[t].r)}function v(){if(e){var t,i,u=e.length;for(r=new Array(u),t=0;t<u;++t)i=e[t],r[i.index]=+n(i,t,e)}}return"function"!=typeof n&&(n=u(null==n?1:+n)),l.initialize=function(n){e=n,v()},l.iterations=function(n){return arguments.length?(c=+n,l):c},l.strength=function(n){return arguments.length?(i=+n,l):i},l.radius=function(t){return arguments.length?(n="function"==typeof t?t:u(+t),v(),l):n},l},n.forceLink=function(n){var t,r,i,f,a,h=c,v=function(n){return 1/Math.min(f[n.source.index],f[n.target.index])},y=u(30),d=1;function x(e){for(var i=0,u=n.length;i<d;++i)for(var f,c,l,h,v,y,x,g=0;g<u;++g)c=(f=n[g]).source,h=(l=f.target).x+l.vx-c.x-c.vx||o(),v=l.y+l.vy-c.y-c.vy||o(),h*=y=((y=Math.sqrt(h*h+v*v))-r[g])/y*e*t[g],v*=y,l.vx-=h*(x=a[g]),l.vy-=v*x,c.vx+=h*(x=1-x),c.vy+=v*x}function g(){if(i){var u,o,c=i.length,v=n.length,y=e.map(i,h);for(u=0,f=new Array(c);u<v;++u)(o=n[u]).index=u,"object"!=typeof o.source&&(o.source=l(y,o.source)),"object"!=typeof o.target&&(o.target=l(y,o.target)),f[o.source.index]=(f[o.source.index]||0)+1,f[o.target.index]=(f[o.target.index]||0)+1;for(u=0,a=new Array(v);u<v;++u)o=n[u],a[u]=f[o.source.index]/(f[o.source.index]+f[o.target.index]);t=new Array(v),s(),r=new Array(v),p()}}function s(){if(i)for(var e=0,r=n.length;e<r;++e)t[e]=+v(n[e],e,n)}function p(){if(i)for(var t=0,e=n.length;t<e;++t)r[t]=+y(n[t],t,n)}return null==n&&(n=[]),x.initialize=function(n){i=n,g()},x.links=function(t){return arguments.length?(n=t,g(),x):n},x.id=function(n){return arguments.length?(h=n,x):h},x.iterations=function(n){return arguments.length?(d=+n,x):d},x.strength=function(n){return arguments.length?(v="function"==typeof n?n:u(+n),s(),x):v},x.distance=function(n){return arguments.length?(y="function"==typeof n?n:u(+n),p(),x):y},x},n.forceManyBody=function(){var n,e,r,i,f=u(-30),a=1,c=1/0,l=.81;function y(i){var u,o=n.length,f=t.quadtree(n,h,v).visitAfter(x);for(r=i,u=0;u<o;++u)e=n[u],f.visit(g)}function d(){if(n){var t,e,r=n.length;for(i=new Array(r),t=0;t<r;++t)e=n[t],i[e.index]=+f(e,t,n)}}function x(n){var t,e,r,u,o,f=0,a=0;if(n.length){for(r=u=o=0;o<4;++o)(t=n[o])&&(e=Math.abs(t.value))&&(f+=t.value,a+=e,r+=e*t.x,u+=e*t.y);n.x=r/a,n.y=u/a}else{(t=n).x=t.data.x,t.y=t.data.y;do{f+=i[t.data.index]}while(t=t.next)}n.value=f}function g(n,t,u,f){if(!n.value)return!0;var h=n.x-e.x,v=n.y-e.y,y=f-t,d=h*h+v*v;if(y*y/l<d)return d<c&&(0===h&&(d+=(h=o())*h),0===v&&(d+=(v=o())*v),d<a&&(d=Math.sqrt(a*d)),e.vx+=h*n.value*r/d,e.vy+=v*n.value*r/d),!0;if(!(n.length||d>=c)){(n.data!==e||n.next)&&(0===h&&(d+=(h=o())*h),0===v&&(d+=(v=o())*v),d<a&&(d=Math.sqrt(a*d)));do{n.data!==e&&(y=i[n.data.index]*r/d,e.vx+=h*y,e.vy+=v*y)}while(n=n.next)}}return y.initialize=function(t){n=t,d()},y.strength=function(n){return arguments.length?(f="function"==typeof n?n:u(+n),d(),y):f},y.distanceMin=function(n){return arguments.length?(a=n*n,y):Math.sqrt(a)},y.distanceMax=function(n){return arguments.length?(c=n*n,y):Math.sqrt(c)},y.theta=function(n){return arguments.length?(l=n*n,y):Math.sqrt(l)},y},n.forceRadial=function(n,t,e){var r,i,o,f=u(.1);function a(n){for(var u=0,f=r.length;u<f;++u){var a=r[u],c=a.x-t||1e-6,l=a.y-e||1e-6,h=Math.sqrt(c*c+l*l),v=(o[u]-h)*i[u]*n/h;a.vx+=c*v,a.vy+=l*v}}function c(){if(r){var t,e=r.length;for(i=new Array(e),o=new Array(e),t=0;t<e;++t)o[t]=+n(r[t],t,r),i[t]=isNaN(o[t])?0:+f(r[t],t,r)}}return"function"!=typeof n&&(n=u(+n)),null==t&&(t=0),null==e&&(e=0),a.initialize=function(n){r=n,c()},a.strength=function(n){return arguments.length?(f="function"==typeof n?n:u(+n),c(),a):f},a.radius=function(t){return arguments.length?(n="function"==typeof t?t:u(+t),c(),a):n},a.x=function(n){return arguments.length?(t=+n,a):t},a.y=function(n){return arguments.length?(e=+n,a):e},a},n.forceSimulation=function(n){var t,u=1,o=.001,f=1-Math.pow(o,1/300),a=0,c=.6,l=e.map(),h=i.timer(x),v=r.dispatch("tick","end");function x(){g(),v.call("tick",t),u<o&&(h.stop(),v.call("end",t))}function g(e){var r,i,o=n.length;void 0===e&&(e=1);for(var h=0;h<e;++h)for(u+=(a-u)*f,l.each(function(n){n(u)}),r=0;r<o;++r)null==(i=n[r]).fx?i.x+=i.vx*=c:(i.x=i.fx,i.vx=0),null==i.fy?i.y+=i.vy*=c:(i.y=i.fy,i.vy=0);return t}function s(){for(var t,e=0,r=n.length;e<r;++e){if((t=n[e]).index=e,null!=t.fx&&(t.x=t.fx),null!=t.fy&&(t.y=t.fy),isNaN(t.x)||isNaN(t.y)){var i=y*Math.sqrt(e),u=e*d;t.x=i*Math.cos(u),t.y=i*Math.sin(u)}(isNaN(t.vx)||isNaN(t.vy))&&(t.vx=t.vy=0)}}function p(t){return t.initialize&&t.initialize(n),t}return null==n&&(n=[]),s(),t={tick:g,restart:function(){return h.restart(x),t},stop:function(){return h.stop(),t},nodes:function(e){return arguments.length?(n=e,s(),l.each(p),t):n},alpha:function(n){return arguments.length?(u=+n,t):u},alphaMin:function(n){return arguments.length?(o=+n,t):o},alphaDecay:function(n){return arguments.length?(f=+n,t):+f},alphaTarget:function(n){return arguments.length?(a=+n,t):a},velocityDecay:function(n){return arguments.length?(c=1-n,t):1-c},force:function(n,e){return arguments.length>1?(null==e?l.remove(n):l.set(n,p(e)),t):l.get(n)},find:function(t,e,r){var i,u,o,f,a,c=0,l=n.length;for(null==r?r=1/0:r*=r,c=0;c<l;++c)(o=(i=t-(f=n[c]).x)*i+(u=e-f.y)*u)<r&&(a=f,r=o);return a},on:function(n,e){return arguments.length>1?(v.on(n,e),t):v.on(n)}}},n.forceX=function(n){var t,e,r,i=u(.1);function o(n){for(var i,u=0,o=t.length;u<o;++u)(i=t[u]).vx+=(r[u]-i.x)*e[u]*n}function f(){if(t){var u,o=t.length;for(e=new Array(o),r=new Array(o),u=0;u<o;++u)e[u]=isNaN(r[u]=+n(t[u],u,t))?0:+i(t[u],u,t)}}return"function"!=typeof n&&(n=u(null==n?0:+n)),o.initialize=function(n){t=n,f()},o.strength=function(n){return arguments.length?(i="function"==typeof n?n:u(+n),f(),o):i},o.x=function(t){return arguments.length?(n="function"==typeof t?t:u(+t),f(),o):n},o},n.forceY=function(n){var t,e,r,i=u(.1);function o(n){for(var i,u=0,o=t.length;u<o;++u)(i=t[u]).vy+=(r[u]-i.y)*e[u]*n}function f(){if(t){var u,o=t.length;for(e=new Array(o),r=new Array(o),u=0;u<o;++u)e[u]=isNaN(r[u]=+n(t[u],u,t))?0:+i(t[u],u,t)}}return"function"!=typeof n&&(n=u(null==n?0:+n)),o.initialize=function(n){t=n,f()},o.strength=function(n){return arguments.length?(i="function"==typeof n?n:u(+n),f(),o):i},o.y=function(t){return arguments.length?(n="function"==typeof t?t:u(+t),f(),o):n},o},Object.defineProperty(n,"__esModule",{value:!0})});
