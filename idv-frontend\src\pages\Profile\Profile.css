/* Profile Page - DRDO v1.7 */
.profile-page {
  background: #FFFFFF;
  min-height: 100vh;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.profile-header {
  background: linear-gradient(135deg, #1A3C5E 0%, #2A4C6E 100%);
  color: white;
  padding: 32px;
  text-align: center;
}

.profile-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.profile-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.profile-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 32px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.profile-card,
.session-card {
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(26, 60, 94, 0.08);
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #F1F5F9;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.profile-basic-info {
  flex: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: 700;
  color: #1A3C5E;
  margin: 0 0 8px 0;
}

.profile-role {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.profile-clearance {
  font-size: 14px;
  color: #64748B;
  font-weight: 500;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 16px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item label {
  font-size: 14px;
  font-weight: 500;
  color: #64748B;
}

.detail-item span {
  font-size: 14px;
  color: #1A3C5E;
  padding: 8px 0;
}

.profile-input,
.profile-textarea {
  padding: 12px 16px;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  font-size: 14px;
  color: #1A3C5E;
  background: white;
  transition: border-color 0.3s ease;
}

.profile-input:focus,
.profile-textarea:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.profile-bio {
  color: #1A3C5E;
  line-height: 1.6;
  margin: 0;
  padding: 8px 0;
}

.preferences-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preference-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1A3C5E;
  cursor: pointer;
}

.preference-item input[type="checkbox"] {
  transform: scale(1.2);
}

.profile-actions {
  border-top: 1px solid #F1F5F9;
  padding-top: 24px;
  display: flex;
  justify-content: center;
}

.edit-actions {
  display: flex;
  gap: 16px;
}

.btn-edit,
.btn-save,
.btn-cancel,
.btn-logout {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-edit {
  background: linear-gradient(135deg, #4A90E2, #FF9933);
  color: white;
}

.btn-edit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
}

.btn-save {
  background: #059669;
  color: white;
}

.btn-save:hover {
  background: #047857;
}

.btn-cancel {
  background: white;
  color: #64748B;
  border: 2px solid #E2E8F0;
}

.btn-cancel:hover {
  border-color: #64748B;
}

.btn-logout {
  background: #DC2626;
  color: white;
  width: 100%;
}

.btn-logout:hover {
  background: #B91C1C;
}

/* Session Card */
.session-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1A3C5E;
  margin: 0 0 20px 0;
}

.session-details {
  margin-bottom: 24px;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #F8FAFC;
}

.session-label {
  font-size: 14px;
  color: #64748B;
}

.session-value {
  font-size: 14px;
  font-weight: 500;
  color: #1A3C5E;
}

/* Profile Footer */
.profile-footer {
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
  padding: 24px 32px;
}

.footer-info {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #64748B;
}

/* Error State */
.profile-error {
  text-align: center;
  padding: 80px 32px;
}

.profile-error h2 {
  font-size: 24px;
  color: #DC2626;
  margin-bottom: 16px;
}

.profile-error p {
  color: #64748B;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
    padding: 24px 16px;
  }
  
  .profile-card,
  .session-card {
    padding: 24px 16px;
  }
  
  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .edit-actions {
    flex-direction: column;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-card,
.session-card {
  animation: fadeIn 0.5s ease-out;
}
