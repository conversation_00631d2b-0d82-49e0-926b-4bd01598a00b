/* DRDO IDX v1.7 - Global App Styles */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #FFFFFF;
  color: #1A3C5E;
  line-height: 1.5;
}

#root {
  height: 100%;
  width: 100%;
}

.App {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Ensure content is visible */
.layout-v17,
.layout-minimal {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
}

/* Fix for invisible content */
.layout-main {
  flex: 1;
  padding: 20px;
  background: #FFFFFF;
  overflow-y: auto;
  min-height: calc(100vh - 80px);
}

/* Button fixes */
button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}

button:focus {
  outline: 2px solid #FF9933;
  outline-offset: 2px;
}

/* Ensure all text is visible */
h1, h2, h3, h4, h5, h6, p, span, div {
  color: inherit;
}

/* Professional DRDO styling */
.drdo-primary {
  background: #1A3C5E;
  color: white;
}

.drdo-accent {
  background: #FF9933;
  color: white;
}

.drdo-surface {
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
}

/* Utility classes for visibility */
.visible {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

.flex-visible {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
}
