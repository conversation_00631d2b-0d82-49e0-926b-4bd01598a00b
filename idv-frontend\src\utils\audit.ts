import { securityManager, SecurityLevel } from './encryption';

export interface AuditEvent {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  securityLevel: SecurityLevel;
  sessionId: string;
  module: string;
  success: boolean;
  errorMessage?: string;
}

export type AuditAction = 
  | 'USER_LOGIN'
  | 'USER_LOGOUT'
  | 'DATA_VIEW'
  | 'DATA_EXPORT'
  | 'DATA_IMPORT'
  | 'SETTINGS_CHANGE'
  | 'ROLE_SWITCH'
  | 'FILE_UPLOAD'
  | 'FILE_DOWNLOAD'
  | 'CHART_VIEW'
  | 'FILTER_APPLY'
  | 'SEARCH_PERFORM'
  | 'THEME_CHANGE'
  | 'SECURITY_VIOLATION'
  | 'SESSION_TIMEOUT';

class AuditLogger {
  private static instance: AuditLogger;
  private sessionId: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
  }

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log an audit event
   */
  log(
    action: AuditAction,
    details: string,
    module: string = 'SYSTEM',
    securityLevel: SecurityLevel = 'PUBLIC',
    userId: string = 'anonymous',
    userName: string = 'Anonymous',
    success: boolean = true,
    errorMessage?: string
  ): void {
    const event: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      userId,
      userName,
      action,
      details,
      ipAddress: this.getMockIpAddress(),
      userAgent: navigator.userAgent,
      securityLevel,
      sessionId: this.sessionId,
      module,
      success,
      errorMessage,
    };

    this.storeEvent(event);
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Audit Event:', event);
    }
  }

  private getMockIpAddress(): string {
    // In a real application, this would be obtained from the server
    return '*************';
  }

  private storeEvent(event: AuditEvent): void {
    try {
      // Encrypt the audit event
      const encryptedEvent = securityManager.encrypt(event, 'CONFIDENTIAL');
      
      // Get existing logs
      const existingLogs = this.getStoredLogs();
      existingLogs.push(encryptedEvent);
      
      // Keep only last 5000 events to prevent storage overflow
      if (existingLogs.length > 5000) {
        existingLogs.splice(0, existingLogs.length - 5000);
      }
      
      // Store back to localStorage
      localStorage.setItem('drdo-audit-logs', JSON.stringify(existingLogs));
    } catch (error) {
      console.error('Failed to store audit event:', error);
    }
  }

  private getStoredLogs(): any[] {
    try {
      const stored = localStorage.getItem('drdo-audit-logs');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to retrieve audit logs:', error);
      return [];
    }
  }

  /**
   * Retrieve audit logs (decrypted)
   */
  getLogs(limit: number = 100): AuditEvent[] {
    try {
      const encryptedLogs = this.getStoredLogs();
      const decryptedLogs: AuditEvent[] = [];
      
      // Decrypt logs (most recent first)
      const recentLogs = encryptedLogs.slice(-limit).reverse();
      
      for (const encryptedLog of recentLogs) {
        try {
          const decrypted = securityManager.decrypt(encryptedLog);
          decryptedLogs.push(decrypted);
        } catch (error) {
          console.warn('Failed to decrypt audit log:', error);
        }
      }
      
      return decryptedLogs;
    } catch (error) {
      console.error('Failed to retrieve audit logs:', error);
      return [];
    }
  }

  /**
   * Filter logs by criteria
   */
  filterLogs(
    filters: {
      userId?: string;
      action?: AuditAction;
      module?: string;
      startDate?: Date;
      endDate?: Date;
      securityLevel?: SecurityLevel;
    },
    limit: number = 100
  ): AuditEvent[] {
    const allLogs = this.getLogs(limit * 2); // Get more logs to filter from
    
    return allLogs.filter(log => {
      if (filters.userId && log.userId !== filters.userId) return false;
      if (filters.action && log.action !== filters.action) return false;
      if (filters.module && log.module !== filters.module) return false;
      if (filters.securityLevel && log.securityLevel !== filters.securityLevel) return false;
      
      const logDate = new Date(log.timestamp);
      if (filters.startDate && logDate < filters.startDate) return false;
      if (filters.endDate && logDate > filters.endDate) return false;
      
      return true;
    }).slice(0, limit);
  }

  /**
   * Export logs as encrypted file
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    const logs = this.getLogs(1000);
    
    if (format === 'csv') {
      const headers = ['Timestamp', 'User', 'Action', 'Module', 'Details', 'Success'];
      const csvRows = [headers.join(',')];
      
      logs.forEach(log => {
        const row = [
          log.timestamp,
          log.userName,
          log.action,
          log.module,
          `"${log.details.replace(/"/g, '""')}"`,
          log.success.toString()
        ];
        csvRows.push(row.join(','));
      });
      
      return csvRows.join('\n');
    }
    
    return JSON.stringify(logs, null, 2);
  }

  /**
   * Clear all audit logs
   */
  clearLogs(): void {
    localStorage.removeItem('drdo-audit-logs');
    this.log('AUDIT_CLEAR', 'All audit logs cleared', 'AUDIT', 'CONFIDENTIAL');
  }

  /**
   * Get audit statistics
   */
  getStatistics(): {
    totalEvents: number;
    eventsByAction: Record<string, number>;
    eventsByModule: Record<string, number>;
    recentActivity: AuditEvent[];
    securityViolations: number;
  } {
    const logs = this.getLogs(1000);
    
    const eventsByAction: Record<string, number> = {};
    const eventsByModule: Record<string, number> = {};
    let securityViolations = 0;
    
    logs.forEach(log => {
      eventsByAction[log.action] = (eventsByAction[log.action] || 0) + 1;
      eventsByModule[log.module] = (eventsByModule[log.module] || 0) + 1;
      
      if (log.action === 'SECURITY_VIOLATION' || !log.success) {
        securityViolations++;
      }
    });
    
    return {
      totalEvents: logs.length,
      eventsByAction,
      eventsByModule,
      recentActivity: logs.slice(0, 10),
      securityViolations,
    };
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();

// Convenience functions
export const logAuditEvent = (
  action: AuditAction,
  details: string,
  module?: string,
  securityLevel?: SecurityLevel,
  userId?: string,
  userName?: string,
  success?: boolean,
  errorMessage?: string
) => auditLogger.log(action, details, module, securityLevel, userId, userName, success, errorMessage);

export const getAuditLogs = (limit?: number) => auditLogger.getLogs(limit);

export const exportAuditLogs = (format?: 'json' | 'csv') => auditLogger.exportLogs(format);

export const getAuditStatistics = () => auditLogger.getStatistics();
