var $hREOp$reactariagridlist = require("@react-aria/gridlist");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useTree", () => $0caa22fd4e1b3c7e$export$fb0040ce9d6e8bd1);
/*
 * Copyright 2024 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 
function $0caa22fd4e1b3c7e$export$fb0040ce9d6e8bd1(props, state, ref) {
    let { gridProps: gridProps } = (0, $hREOp$reactariagridlist.useGridList)(props, state, ref);
    gridProps.role = 'treegrid';
    return {
        gridProps: gridProps
    };
}


//# sourceMappingURL=useTree.main.js.map
