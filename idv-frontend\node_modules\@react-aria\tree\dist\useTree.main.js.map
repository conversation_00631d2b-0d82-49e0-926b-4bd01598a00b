{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAiCM,SAAS,0CAAW,KAAyB,EAAE,KAAmB,EAAE,GAAkC;IAC3G,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,oCAAU,EAAE,OAAO,OAAO;IAC5C,UAAU,IAAI,GAAG;IAEjB,OAAO;mBACL;IACF;AACF", "sources": ["packages/@react-aria/tree/src/useTree.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaGridListOptions, AriaGridListProps, GridListProps, useGridList} from '@react-aria/gridlist';\nimport {\n  DOMAttributes,\n  KeyboardDelegate,\n  RefObject\n} from '@react-types/shared';\nimport {TreeState} from '@react-stately/tree';\n\nexport interface TreeProps<T> extends GridListProps<T> {}\n\nexport interface AriaTreeProps<T> extends Omit<AriaGridListProps<T>, 'keyboardNavigationBehavior'> {}\nexport interface AriaTreeOptions<T> extends Omit<AriaGridListOptions<T>, 'children' | 'shouldFocusWrap'> {\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate\n}\n\nexport interface TreeAria {\n  /** Props for the treegrid element. */\n  gridProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a single column treegrid component with interactive children.\n * A tree grid provides users with a way to navigate nested hierarchical information.\n * @param props - Props for the treegrid.\n * @param state - State for the treegrid, as returned by `useTreeState`.\n * @param ref - The ref attached to the treegrid element.\n */\nexport function useTree<T>(props: AriaTreeOptions<T>, state: TreeState<T>, ref: RefObject<HTMLElement | null>): TreeAria {\n  let {gridProps} = useGridList(props, state, ref);\n  gridProps.role = 'treegrid';\n\n  return {\n    gridProps\n  };\n}\n"], "names": [], "version": 3, "file": "useTree.main.js.map"}