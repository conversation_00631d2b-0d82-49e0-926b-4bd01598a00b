import { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import Header from '../Header/Header';
import Navigation from '../Navigation/Navigation';
import './Layout.css';

const Layout = ({ children }) => {
  const [activeView, setActiveView] = useState('dashboard');
  const { isAuthenticated, isDevelopmentMode } = useAuth();
  const { theme } = useTheme();

  // If not authenticated and not in development mode, show minimal layout
  if (!isAuthenticated && !isDevelopmentMode) {
    return (
      <div className="layout-minimal">
        {children}
      </div>
    );
  }

  // If in development mode but not authenticated yet, show loading
  if (!isAuthenticated && isDevelopmentMode) {
    return (
      <div className="layout-minimal">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          background: '#FFFFFF',
          flexDirection: 'column',
          gap: '20px'
        }}>
          <div style={{ fontSize: '48px' }}>🇮🇳</div>
          <h2 style={{ color: '#1A3C5E' }}>DRDO IDX v1.7</h2>
          <p style={{ color: '#64748B' }}>Development Mode - Auto-login in progress...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`layout-v17 theme-${theme}`}>
      <Header />
      <div className="layout-body">
        <Navigation activeView={activeView} setActiveView={setActiveView} />
        <main className="layout-main">
          {children}
        </main>
      </div>
      {isDevelopmentMode && (
        <div className="dev-indicator">
          🛠️ Development Mode
        </div>
      )}
    </div>
  );
};

export default Layout;
