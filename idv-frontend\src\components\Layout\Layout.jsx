import { useState } from 'react';
import Header from '../Header/Header';
import Navigation from '../Navigation/Navigation';
import Sidebar from '../Sidebar/Sidebar';

const Layout = ({ children }) => {
  const [activeView, setActiveView] = useState('dashboard');

  return (
    <div className="min-h-screen bg-drdo-gray">  
      <Header />
      <Navigation activeView={activeView} setActiveView={setActiveView} />
      <div className="flex">
        <Sidebar activeView={activeView} setActiveView={setActiveView} />
        <main className="flex-1 p-6 bg-drdo-gray min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
