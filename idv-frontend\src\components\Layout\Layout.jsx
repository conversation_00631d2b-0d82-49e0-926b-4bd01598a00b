import { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import Header from '../Header/Header';
import Navigation from '../Navigation/Navigation';
import './Layout.css';

const Layout = ({ children }) => {
  const [activeView, setActiveView] = useState('dashboard');
  const { isAuthenticated, isDevelopmentMode } = useAuth();
  const { theme } = useTheme();

  // If not authenticated, show minimal layout
  if (!isAuthenticated) {
    return (
      <div className="layout-minimal">
        {children}
      </div>
    );
  }

  return (
    <div className={`layout-v17 theme-${theme}`}>
      <Header />
      <div className="layout-body">
        <Navigation activeView={activeView} setActiveView={setActiveView} />
        <main className="layout-main">
          {children}
        </main>
      </div>
      {isDevelopmentMode && (
        <div className="dev-indicator">
          🛠️ Development Mode
        </div>
      )}
    </div>
  );
};

export default Layout;
