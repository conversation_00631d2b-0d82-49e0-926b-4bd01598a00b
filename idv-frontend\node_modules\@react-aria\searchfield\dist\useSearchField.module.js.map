{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAgCM,SAAS,0CACd,KAA2B,EAC3B,KAAuB,EACvB,QAA4C;IAE5C,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAChE,IAAI,cACF,UAAU,cACV,UAAU,YACV,QAAQ,WACR,OAAO,QACP,OAAO,UACR,GAAG;IAEJ,IAAI,YAAY,CAAC;QACf,MAAM,MAAM,EAAE,GAAG;QAEjB,IAAI,QAAQ,WAAY,CAAA,cAAc,UAAS,GAC7C,EAAE,cAAc;QAGlB,IAAI,cAAc,YAChB;QAGF,8BAA8B;QAC9B,2FAA2F;QAC3F,IAAI,QAAQ,WAAW,UAAU;YAC/B,EAAE,cAAc;YAChB,SAAS,MAAM,KAAK;QACtB;QAEA,IAAI,QAAQ;YACV,4HAA4H;YAC5H,WAAW;YACX,IAAI,MAAM,KAAK,KAAK,MAAO,CAAA,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,KAAK,KAAK,EAAC,GAC1E,EAAE,mBAAmB;iBAChB;gBACL,EAAE,cAAc;gBAChB,MAAM,QAAQ,CAAC;gBACf,IAAI,SACF;YAEJ;;IAEJ;IAEA,IAAI,qBAAqB;QACvB,MAAM,QAAQ,CAAC;QAEf,IAAI,SACF;IAEJ;IAEA,IAAI,eAAe;YACjB,+FAA+F;QAC/F,mCAAmC;QACnC;SAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,IAAI,cAAC,UAAU,cAAE,UAAU,oBAAE,gBAAgB,qBAAE,iBAAiB,EAAE,GAAG,YAAW,GAAG,CAAA,GAAA,mBAAW,EAAE;QAC9F,GAAG,KAAK;QACR,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QACxB,WAAW,CAAC,aAAa,CAAA,GAAA,YAAI,EAAE,WAAW,MAAM,SAAS,IAAI,MAAM,SAAS;cAC5E;IACF,GAAG;IAEH,OAAO;oBACL;QACA,YAAY;YACV,GAAG,UAAU;YACb,yCAAyC;YACzC,cAAc;QAChB;QACA,kBAAkB;YAChB,cAAc,gBAAgB,MAAM,CAAC;YACrC,qBAAqB;YACrB,qBAAqB;YACrB,YAAY,cAAc;YAC1B,SAAS;0BACT;QACF;0BACA;2BACA;QACA,GAAG,UAAU;IACf;AACF", "sources": ["packages/@react-aria/searchfield/src/useSearchField.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaSearchFieldProps} from '@react-types/searchfield';\nimport {chain} from '@react-aria/utils';\nimport {DOMAttributes, RefObject, ValidationResult} from '@react-types/shared';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {SearchFieldState} from '@react-stately/searchfield';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useTextField} from '@react-aria/textfield';\n\nexport interface SearchFieldAria extends ValidationResult {\n  /** Props for the text field's visible label element (if any). */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Props for the clear button. */\n  clearButtonProps: AriaButtonProps,\n  /** Props for the searchfield's description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the searchfield's error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a search field.\n * @param props - Props for the search field.\n * @param state - State for the search field, as returned by `useSearchFieldState`.\n * @param inputRef - A ref to the input element.\n */\nexport function useSearchField(\n  props: AriaSearchFieldProps,\n  state: SearchFieldState,\n  inputRef: RefObject<HTMLInputElement | null>\n): SearchFieldAria {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/searchfield');\n  let {\n    isDisabled,\n    isReadOnly,\n    onSubmit,\n    onClear,\n    type = 'search'\n  } = props;\n\n  let onKeyDown = (e) => {\n    const key = e.key;\n\n    if (key === 'Enter' && (isDisabled || isReadOnly)) {\n      e.preventDefault();\n    }\n\n    if (isDisabled || isReadOnly) {\n      return;\n    }\n\n    // for backward compatibility;\n    // otherwise, \"Enter\" on an input would trigger a form submit, the default browser behavior\n    if (key === 'Enter' && onSubmit) {\n      e.preventDefault();\n      onSubmit(state.value);\n    }\n\n    if (key === 'Escape') {\n      // Also check the inputRef value for the case where the value was set directly on the input element instead of going through\n      // the hook\n      if (state.value === '' && (!inputRef.current || inputRef.current.value === '')) {\n        e.continuePropagation();\n      } else {\n        e.preventDefault();\n        state.setValue('');\n        if (onClear) {\n          onClear();\n        }\n      }\n    }\n  };\n\n  let onClearButtonClick = () => {\n    state.setValue('');\n\n    if (onClear) {\n      onClear();\n    }\n  };\n\n  let onPressStart = () => {\n    // this is in PressStart for mobile so that touching the clear button doesn't remove focus from\n    // the input and close the keyboard\n    inputRef.current?.focus();\n  };\n\n  let {labelProps, inputProps, descriptionProps, errorMessageProps, ...validation} = useTextField({\n    ...props,\n    value: state.value,\n    onChange: state.setValue,\n    onKeyDown: !isReadOnly ? chain(onKeyDown, props.onKeyDown) : props.onKeyDown,\n    type\n  }, inputRef);\n\n  return {\n    labelProps,\n    inputProps: {\n      ...inputProps,\n      // already handled by useSearchFieldState\n      defaultValue: undefined\n    },\n    clearButtonProps: {\n      'aria-label': stringFormatter.format('Clear search'),\n      excludeFromTabOrder: true,\n      preventFocusOnPress: true,\n      isDisabled: isDisabled || isReadOnly,\n      onPress: onClearButtonClick,\n      onPressStart\n    },\n    descriptionProps,\n    errorMessageProps,\n    ...validation\n  };\n}\n"], "names": [], "version": 3, "file": "useSearchField.module.js.map"}