{"mappings": ";;;AAqBA,gCAAgC,CAAC,CAAE,SAAQ,iBAAiB;IAC1D,wBAAwB;IACxB,KAAK,EAAE,YAAY,CAAC,CAAC,CAAA;CACtB;AAED;IACE,+DAA+D;IAC/D,UAAU,EAAE,aAAa,CAAC;IAC1B,iDAAiD;IACjD,YAAY,EAAE,aAAa,CAAC;IAC5B,yCAAyC;IACzC,UAAU,EAAE,aAAa,CAAC;IAC1B,uDAAuD;IACvD,gBAAgB,EAAE,aAAa,CAAC;IAChC,wCAAwC;IACxC,gBAAgB,EAAE,eAAe,CAAA;CAClC;AAED;;;GAGG;AAEH,yBAAyB,CAAC,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,SAAS,CAyD9H;AC/ED,qCAAsC,SAAQ,iBAAiB;IAC7D;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAA;CACtB;AAED;IACE,6CAA6C;IAC7C,WAAW,EAAE,aAAa,CAAA;CAC3B;AAED;;;GAGG;AACH,+BAA+B,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,WAAW,GAAG,IAAI,CAAC,GAAG,eAAe,CAiKxI", "sources": ["packages/@react-aria/toast/src/packages/@react-aria/toast/src/useToast.ts", "packages/@react-aria/toast/src/packages/@react-aria/toast/src/useToastRegion.ts", "packages/@react-aria/toast/src/packages/@react-aria/toast/src/index.ts", "packages/@react-aria/toast/src/index.ts"], "sourcesContent": [null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {useToast} from './useToast';\nexport {useToastRegion} from './useToastRegion';\n\nexport type {AriaToastRegionProps, ToastRegionAria} from './useToastRegion';\nexport type {AriaToastProps, ToastAria} from './useToast';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}