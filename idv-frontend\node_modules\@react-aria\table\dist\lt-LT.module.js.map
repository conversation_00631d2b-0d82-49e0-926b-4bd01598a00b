{"mappings": ";AAAA,4BAAiB;IAAG,aAAa,CAAC,6BAAiB,CAAC;IAClD,iBAAiB,CAAC,OAAS,CAAC,gCAA0B,EAAE,KAAK,UAAU,CAAC,8BAAkB,CAAC;IAC3F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC;IAC7C,cAAc,CAAC,mCAAiB,CAAC;IACjC,kBAAkB,CAAC,OAAS,CAAC,gCAA0B,EAAE,KAAK,UAAU,CAAC,oCAAkB,CAAC;IAC5F,sBAAsB,CAAC,gFAAgD,CAAC;IACxE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,sBAAgB,CAAC;IAC/B,YAAY,CAAC,qBAAqB,CAAC;AACrC", "sources": ["packages/@react-aria/table/intl/lt-LT.json"], "sourcesContent": ["{\n  \"ascending\": \"did<PERSON><PERSON><PERSON>ia tvarka\",\n  \"ascendingSort\": \"surikiuota pagal stulpelį {columnName} did<PERSON><PERSON><PERSON>ia tvarka\",\n  \"columnSize\": \"{value} piks.\",\n  \"descending\": \"mažėjančia tvarka\",\n  \"descendingSort\": \"surikiuota pagal stulpelį {columnName} mažėjančia tvarka\",\n  \"resizerDescription\": \"Paspauskite „Enter“, kad pradėtumėte keisti dydį\",\n  \"select\": \"Pasirinkti\",\n  \"selectAll\": \"Pasirinkti viską\",\n  \"sortable\": \"rikiuo<PERSON><PERSON> stulpelis\"\n}\n"], "names": [], "version": 3, "file": "lt-LT.module.js.map"}