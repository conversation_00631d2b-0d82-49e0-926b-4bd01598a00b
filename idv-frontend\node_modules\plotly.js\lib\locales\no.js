'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'no',
    dictionary: {
        'Autoscale': 'Autoskalere',                                                      // components/modebar/buttons.js:148
        'Box Select': 'Velg rektangel',                                                  // components/modebar/buttons.js:112
        'Click to enter Colorscale title': '<PERSON>lik<PERSON> for å oppgi tittel på fargeskala',     // plots/plots.js:326
        'Click to enter Component A title': 'Klikk for å oppgi tittel på komponent A',   // plots/ternary/ternary.js:376
        'Click to enter Component B title': 'Klikk for å oppgi tittel på komponent B',   // plots/ternary/ternary.js:386
        'Click to enter Component C title': 'Klikk for å oppgi tittel på komponent C',   // plots/ternary/ternary.js:396
        'Click to enter Plot title': 'Klikk for å oppgi tittel på plot',                 // plot_api/plot_api.js:584
        'Click to enter X axis title': '<PERSON>lik<PERSON> for å oppgi tittel på x-akse',              // plots/plots.js:324
        'Click to enter Y axis title': 'Klikk for å oppgi tittel på y-akse',              // plots/plots.js:325
        'Click to enter radial axis title': 'Klikk for å oppgi tittel på radiell akse',   // plots/polar/polar.js:498
        'Compare data on hover': 'Sammenligne data når musepekeren holdes åver',         // components/modebar/buttons.js:176
        'Double-click on legend to isolate one trace': 'Dobbelklikk på på forklaringen for å vise bare en serie',  // components/legend/handle_click.js:89
        'Double-click to zoom back out': 'Dobbelklikk for å zoome ut igjen',             // plots/cartesian/dragbox.js:1089
        'Download plot': 'Last ned plot',                                                // components/modebar/buttons.js:53
        'Download plot as a png': 'Last ned plot som png',                               // components/modebar/buttons.js:52
        'Edit in Chart Studio': 'Editer i Chart Studio',                                 // components/modebar/buttons.js:85
        'IE only supports svg.  Changing format to svg.': 'IE støtter bare svg. Bytt format til svg.',  // components/modebar/buttons.js:63
        'Lasso Select': 'Velg lasso',                                                    // components/modebar/buttons.js:121
        'Orbital rotation': 'Orbital rotasjon',                                          // components/modebar/buttons.js:281
        'Pan': 'Panne',                                                                  // components/modebar/buttons.js:103
        'Produced with Plotly': 'Laget med Plotly',                                      // components/modebar/modebar.js:304
        'Reset': 'Nullstille',                                                           // components/modebar/buttons.js:433
        'Reset akses': 'Nullstille akser',                                                 // components/modebar/buttons.js:157
        'Reset camera to default': 'Nullstille kamera til standard',                     // components/modebar/buttons.js:319
        'Reset camera to last save': 'Nullstille kamera til siste lagret',               // components/modebar/buttons.js:327
        'Reset view': 'Nullstille visning',                                              // components/modebar/buttons.js:512
        'Reset views': 'Nullstille visninger',                                           // components/modebar/buttons.js:550
        'Show closest data on hover': 'Vis nærmeste verdi når musepekeren holdes over',  // components/modebar/buttons.js:166
        'Snapshot succeeded': 'Bilde Laget',                                             // components/modebar/buttons.js:75
        'Sorry, there was a problem downloading your snapshot!': 'Beklager, noe gikk galt under nedlasting av bildet', // components/modebar/buttons.js:78
        'Taking snapshot - this may take a few seconds': 'Oppretter bilde - dette kan ta noen sekunder', // components/modebar/buttons.js:60
        'Toggle Spike Lines': 'Aktiver / deaktiver topplinjer',                          // components/modebar/buttons.js:569
        'Toggle show closest data on hover': 'Aktiver / deaktiver nærmeste verdi når musepekeren holdes over', // components/modebar/buttons.js:361
        'Turntable rotation': 'Flat rotation',                                           // components/modebar/buttons.js:290
        'Zoom': 'Zoom',                                                                  // components/modebar/buttons.js:94
        'Zoom in': 'Zoom inn',                                                           // components/modebar/buttons.js:130
        'Zoom out': 'Zoom ut',                                                           // components/modebar/buttons.js:139
        'close:': 'steng:',                                                              // traces/ohlc/calc.js:106
        'concentration:': 'konsentrasjon:',                                              // traces/sankey/plot.js:166
        'high:': 'høy:',                                                                 // traces/ohlc/calc.js:104
        'incoming flow count:': 'innkommende strømningssammendrag:',                     // traces/sankey/plot.js:167
        'kde:': 'kde:',                                                                  // traces/violin/calc.js:94
        'lat:': 'lat:',                                                                  // traces/scattergeo/calc.js:48
        'lon:': 'lon:',                                                                  // traces/scattergeo/calc.js:49
        'low:': 'lav:',                                                                  // traces/ohlc/calc.js:105
        'lower fence:': 'lavere grense',                                                  // traces/box/calc.js:146
        'max:': 'maks:',                                                                 // traces/box/calc.js:144
        'mean ± σ:': 'gjennomsnitt ± σ:',                                                // traces/box/calc.js:145
        'mean:': 'gjennomsnitt:',                                                        // traces/box/calc.js:145
        'median:': 'median:',                                                            // traces/box/calc.js:140
        'min:': 'min:',                                                                  // traces/box/calc.js:141
        'new text': 'ny text',                                                           // plots/plots.js:327
        'open:': 'åpen:',                                                                // traces/ohlc/calc.js:103
        'outgoing flow count:': 'utgående strømningssammendrag:',                        // traces/sankey/plot.js:168
        'q1:': 'q1:',                                                                    // traces/box/calc.js:142
        'q3:': 'q3:',                                                                    // traces/box/calc.js:143
        'source:': 'kilde:',                                                             // traces/sankey/plot.js:164
        'target:': 'mål:',                                                               // traces/sankey/plot.js:165
        'trace': 'serie',                                                                // plots/plots.js:329
        'upper fence:': 'øvre grense:',                                                  // traces/box/calc.js:147
    },
    format: {
        days: ['Søndag', 'Mandag', 'Tirsdag', 'Onsdag', 'Torsdag', 'Fredag', 'Lørdag'],
        shortDays: ['Søn', 'Man', 'Tir', 'Ons', 'Tor', 'Fre', 'Lør'],
        months: [
            'Januar', 'Februar', 'Mars', 'April', 'Mai', 'Juni',
            'Juli', 'August', 'September', 'Oktober', 'November', 'Desember'
        ],
        shortMonths: [
            'Jan', 'Feb', 'Mar', 'Apr', 'Mai', 'Jun',
            'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Des'
        ],
        date: '%d.%m.%Y',
        decimal: ',',
        thousands: ' ',
    }
};
