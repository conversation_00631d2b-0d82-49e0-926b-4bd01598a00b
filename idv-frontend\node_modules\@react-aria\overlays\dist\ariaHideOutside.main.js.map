{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,iFAAiF;AACjF,uFAAuF;AACvF,IAAI,oCAAc,IAAI;AAOtB,IAAI,sCAAwC,EAAE;AAUvC,SAAS,0CAAgB,OAAkB,EAAE,OAAO,SAAS,IAAI;IACtE,IAAI,eAAe,IAAI,IAAa;IACpC,IAAI,cAAc,IAAI;IAEtB,IAAI,OAAO,CAAC;QACV,oEAAoE;QACpE,KAAK,IAAI,WAAW,KAAK,gBAAgB,CAAC,sDACxC,aAAa,GAAG,CAAC;QAGnB,IAAI,aAAa,CAAC;YAChB,yFAAyF;YACzF,2FAA2F;YAC3F,uGAAuG;YACvG,2GAA2G;YAC3G,IACE,aAAa,GAAG,CAAC,SAChB,KAAK,aAAa,IAAI,YAAY,GAAG,CAAC,KAAK,aAAa,KAAK,KAAK,aAAa,CAAC,YAAY,CAAC,YAAY,OAE1G,OAAO,WAAW,aAAa;YAGjC,oFAAoF;YACpF,KAAK,IAAI,UAAU,aAAc;gBAC/B,IAAI,KAAK,QAAQ,CAAC,SAChB,OAAO,WAAW,WAAW;YAEjC;YAEA,OAAO,WAAW,aAAa;QACjC;QAEA,IAAI,SAAS,SAAS,gBAAgB,CACpC,MACA,WAAW,YAAY,EACvB;wBAAC;QAAU;QAGb,wCAAwC;QACxC,IAAI,aAAa,WAAW;QAC5B,IAAI,eAAe,WAAW,aAAa,EACzC,KAAK;QAGP,IAAI,eAAe,WAAW,aAAa,EAAE;YAC3C,IAAI,OAAO,OAAO,QAAQ;YAC1B,MAAO,QAAQ,KAAM;gBACnB,KAAK;gBACL,OAAO,OAAO,QAAQ;YACxB;QACF;IACF;IAEA,IAAI,OAAO,CAAC;YACK;QAAf,IAAI,WAAW,CAAA,mBAAA,kCAAY,GAAG,CAAC,mBAAhB,8BAAA,mBAAyB;QAExC,uEAAuE;QACvE,uDAAuD;QACvD,IAAI,KAAK,YAAY,CAAC,mBAAmB,UAAU,aAAa,GAC9D;QAGF,IAAI,aAAa,GACf,KAAK,YAAY,CAAC,eAAe;QAGnC,YAAY,GAAG,CAAC;QAChB,kCAAY,GAAG,CAAC,MAAM,WAAW;IACnC;IAEA,yEAAyE;IACzE,0CAA0C;IAC1C,IAAI,oCAAc,MAAM,EACtB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,CAAC,UAAU;IAGpD,KAAK;IAEL,IAAI,WAAW,IAAI,iBAAiB,CAAA;QAClC,KAAK,IAAI,UAAU,QAAS;YAC1B,IAAI,OAAO,IAAI,KAAK,eAAe,OAAO,UAAU,CAAC,MAAM,KAAK,GAC9D;YAGF,6EAA6E;YAC7E,sEAAsE;YACtE,IAAI,CAAC;mBAAI;mBAAiB;aAAY,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,OAAO,MAAM,IAAI;gBACjF,KAAK,IAAI,QAAQ,OAAO,YAAY,CAClC,IAAI,gBAAgB,SAAS;oBAC3B,aAAa,MAAM,CAAC;oBACpB,YAAY,MAAM,CAAC;gBACrB;gBAGF,KAAK,IAAI,QAAQ,OAAO,UAAU,CAAE;oBAClC,IACE,AAAC,CAAA,gBAAgB,eAAe,gBAAgB,UAAS,KACxD,CAAA,KAAK,OAAO,CAAC,aAAa,KAAK,UAAU,KAAK,OAAO,CAAC,iBAAiB,KAAK,MAAK,GAElF,aAAa,GAAG,CAAC;yBACZ,IAAI,gBAAgB,SACzB,KAAK;gBAET;YACF;QACF;IACF;IAEA,SAAS,OAAO,CAAC,MAAM;QAAC,WAAW;QAAM,SAAS;IAAI;IAEtD,IAAI,kBAAmC;sBACrC;qBACA;QACA;YACE,SAAS,OAAO,CAAC,MAAM;gBAAC,WAAW;gBAAM,SAAS;YAAI;QACxD;QACA;YACE,SAAS,UAAU;QACrB;IACF;IAEA,oCAAc,IAAI,CAAC;IAEnB,OAAO;QACL,SAAS,UAAU;QAEnB,KAAK,IAAI,QAAQ,YAAa;YAC5B,IAAI,QAAQ,kCAAY,GAAG,CAAC;YAC5B,IAAI,SAAS,MACX;YAEF,IAAI,UAAU,GAAG;gBACf,KAAK,eAAe,CAAC;gBACrB,kCAAY,MAAM,CAAC;YACrB,OACE,kCAAY,GAAG,CAAC,MAAM,QAAQ;QAElC;QAEA,mEAAmE;QACnE,IAAI,oBAAoB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,EAAE;YAC/D,oCAAc,GAAG;YACjB,IAAI,oCAAc,MAAM,EACtB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,CAAC,OAAO;QAEnD,OACE,oCAAc,MAAM,CAAC,oCAAc,OAAO,CAAC,kBAAkB;IAEjE;AACF;AAEO,SAAS,0CAAY,OAAgB;IAC1C,IAAI,WAAW,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE;IACtD,IAAI,YAAY,CAAC,SAAS,YAAY,CAAC,GAAG,CAAC,UAAU;QACnD,SAAS,YAAY,CAAC,GAAG,CAAC;QAC1B,OAAO;YACL,SAAS,YAAY,CAAC,MAAM,CAAC;QAC/B;IACF;AACF", "sources": ["packages/@react-aria/overlays/src/ariaHideOutside.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Keeps a ref count of all hidden elements. Added to when hiding an element, and\n// subtracted from when showing it again. When it reaches zero, aria-hidden is removed.\nlet refCountMap = new WeakMap<Element, number>();\ninterface ObserverWrapper {\n  visibleNodes: Set<Element>,\n  hiddenNodes: Set<Element>,\n  observe: () => void,\n  disconnect: () => void\n}\nlet observerStack: Array<ObserverWrapper> = [];\n\n/**\n * Hides all elements in the DOM outside the given targets from screen readers using aria-hidden,\n * and returns a function to revert these changes. In addition, changes to the DOM are watched\n * and new elements outside the targets are automatically hidden.\n * @param targets - The elements that should remain visible.\n * @param root - Nothing will be hidden above this element.\n * @returns - A function to restore all hidden elements.\n */\nexport function ariaHideOutside(targets: Element[], root = document.body) {\n  let visibleNodes = new Set<Element>(targets);\n  let hiddenNodes = new Set<Element>();\n\n  let walk = (root: Element) => {\n    // Keep live announcer and top layer elements (e.g. toasts) visible.\n    for (let element of root.querySelectorAll('[data-live-announcer], [data-react-aria-top-layer]')) {\n      visibleNodes.add(element);\n    }\n\n    let acceptNode = (node: Element) => {\n      // Skip this node and its children if it is one of the target nodes, or a live announcer.\n      // Also skip children of already hidden nodes, as aria-hidden is recursive. An exception is\n      // made for elements with role=\"row\" since VoiceOver on iOS has issues hiding elements with role=\"row\".\n      // For that case we want to hide the cells inside as well (https://bugs.webkit.org/show_bug.cgi?id=222623).\n      if (\n        visibleNodes.has(node) ||\n        (node.parentElement && hiddenNodes.has(node.parentElement) && node.parentElement.getAttribute('role') !== 'row')\n      ) {\n        return NodeFilter.FILTER_REJECT;\n      }\n\n      // Skip this node but continue to children if one of the targets is inside the node.\n      for (let target of visibleNodes) {\n        if (node.contains(target)) {\n          return NodeFilter.FILTER_SKIP;\n        }\n      }\n\n      return NodeFilter.FILTER_ACCEPT;\n    };\n\n    let walker = document.createTreeWalker(\n      root,\n      NodeFilter.SHOW_ELEMENT,\n      {acceptNode}\n    );\n\n    // TreeWalker does not include the root.\n    let acceptRoot = acceptNode(root);\n    if (acceptRoot === NodeFilter.FILTER_ACCEPT) {\n      hide(root);\n    }\n\n    if (acceptRoot !== NodeFilter.FILTER_REJECT) {\n      let node = walker.nextNode() as Element;\n      while (node != null) {\n        hide(node);\n        node = walker.nextNode() as Element;\n      }\n    }\n  };\n\n  let hide = (node: Element) => {\n    let refCount = refCountMap.get(node) ?? 0;\n\n    // If already aria-hidden, and the ref count is zero, then this element\n    // was already hidden and there's nothing for us to do.\n    if (node.getAttribute('aria-hidden') === 'true' && refCount === 0) {\n      return;\n    }\n\n    if (refCount === 0) {\n      node.setAttribute('aria-hidden', 'true');\n    }\n\n    hiddenNodes.add(node);\n    refCountMap.set(node, refCount + 1);\n  };\n\n  // If there is already a MutationObserver listening from a previous call,\n  // disconnect it so the new on takes over.\n  if (observerStack.length) {\n    observerStack[observerStack.length - 1].disconnect();\n  }\n\n  walk(root);\n\n  let observer = new MutationObserver(changes => {\n    for (let change of changes) {\n      if (change.type !== 'childList' || change.addedNodes.length === 0) {\n        continue;\n      }\n\n      // If the parent element of the added nodes is not within one of the targets,\n      // and not already inside a hidden node, hide all of the new children.\n      if (![...visibleNodes, ...hiddenNodes].some(node => node.contains(change.target))) {\n        for (let node of change.removedNodes) {\n          if (node instanceof Element) {\n            visibleNodes.delete(node);\n            hiddenNodes.delete(node);\n          }\n        }\n\n        for (let node of change.addedNodes) {\n          if (\n            (node instanceof HTMLElement || node instanceof SVGElement) &&\n            (node.dataset.liveAnnouncer === 'true' || node.dataset.reactAriaTopLayer === 'true')\n          ) {\n            visibleNodes.add(node);\n          } else if (node instanceof Element) {\n            walk(node);\n          }\n        }\n      }\n    }\n  });\n\n  observer.observe(root, {childList: true, subtree: true});\n\n  let observerWrapper: ObserverWrapper = {\n    visibleNodes,\n    hiddenNodes,\n    observe() {\n      observer.observe(root, {childList: true, subtree: true});\n    },\n    disconnect() {\n      observer.disconnect();\n    }\n  };\n\n  observerStack.push(observerWrapper);\n\n  return (): void => {\n    observer.disconnect();\n\n    for (let node of hiddenNodes) {\n      let count = refCountMap.get(node);\n      if (count == null) {\n        continue;\n      }\n      if (count === 1) {\n        node.removeAttribute('aria-hidden');\n        refCountMap.delete(node);\n      } else {\n        refCountMap.set(node, count - 1);\n      }\n    }\n\n    // Remove this observer from the stack, and start the previous one.\n    if (observerWrapper === observerStack[observerStack.length - 1]) {\n      observerStack.pop();\n      if (observerStack.length) {\n        observerStack[observerStack.length - 1].observe();\n      }\n    } else {\n      observerStack.splice(observerStack.indexOf(observerWrapper), 1);\n    }\n  };\n}\n\nexport function keepVisible(element: Element) {\n  let observer = observerStack[observerStack.length - 1];\n  if (observer && !observer.visibleNodes.has(element)) {\n    observer.visibleNodes.add(element);\n    return () => {\n      observer.visibleNodes.delete(element);\n    };\n  }\n}\n"], "names": [], "version": 3, "file": "ariaHideOutside.main.js.map"}