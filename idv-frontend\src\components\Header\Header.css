.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  height: 80px;
  background-color: #ffffff;
  color: #2c3e50;
  border-bottom: 2px solid #34495e;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.org-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.org-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 0.5px;
}

.system-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.system-name {
  font-size: 13px;
  color: #7f8c8d;
  font-weight: 500;
}

.version {
  font-size: 11px;
  color: #95a5a6;
  background-color: #ecf0f1;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #bdc3c7;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.status-item {
  font-size: 12px;
  color: #7f8c8d;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.user-session {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.user-role {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.session-id {
  font-size: 11px;
  color: #95a5a6;
}

.logout-btn {
  margin-top: 4px;
  padding: 4px 12px;
  font-size: 11px;
  background-color: #ecf0f1;
  border: 1px solid #bdc3c7;
  color: #7f8c8d;
  cursor: pointer;
  border-radius: 3px;
}

.logout-btn:hover {
  background-color: #d5dbdb;
}
