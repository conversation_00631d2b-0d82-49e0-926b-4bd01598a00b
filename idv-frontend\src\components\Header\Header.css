.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
}

.header-left {
  flex: 1;
}

.org-info {
  display: flex;
  flex-direction: column;
}

.org-title {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  color: #333;
}

.system-info {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.system-name {
  font-size: 12px;
  color: #666;
}

.version {
  font-size: 11px;
  color: #888;
  background-color: #f5f5f5;
  padding: 2px 5px;
  border: 1px solid #ccc;
}

.header-center {
  flex: 1;
  text-align: center;
}

.status-indicators {
  display: flex;
  flex-direction: column;
}

.status-item {
  font-size: 11px;
  color: #666;
}

.header-right {
  flex: 1;
  text-align: right;
}

.user-session {
  display: flex;
  flex-direction: column;
}

.user-role {
  font-size: 12px;
  color: #333;
}

.session-id {
  font-size: 11px;
  color: #888;
}
