var $83fe1a57d631223b$exports = require("./Color.main.js");
var $bTjYY$react = require("react");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useColor", () => $f8b3be23ba4462b1$export$5aadd9c0606af5c2);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 

function $f8b3be23ba4462b1$export$5aadd9c0606af5c2(value) {
    return (0, $bTjYY$react.useMemo)(()=>{
        if (typeof value === 'string') try {
            return (0, $83fe1a57d631223b$exports.parseColor)(value);
        } catch  {
            return undefined;
        }
        return value;
    }, [
        value
    ]);
}


//# sourceMappingURL=useColor.main.js.map
