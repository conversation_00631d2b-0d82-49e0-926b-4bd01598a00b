{"mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;;;AA4CM,SAAS,0CAAY,KAAqB,EAAE,KAAuC,EAAE,GAAkC;IAC5H,IAAI,oBACF,gBAAgB,iBAChB,aAAa,kBACb,cAAc,UACd,MAAM,EACP,GAAG;IAEJ,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,GAAA,kBAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAC1B,IAAI,mBAAmB,MAAM,gBAAgB,CAAC,gBAAgB;IAC9D,IAAI,WAAW,CAAA,GAAA,cAAM,EAAE,IAAM,oBAAoB,IAAI,CAAA,GAAA,yCAAoB,EAAE;YACzE,YAAY,MAAM,UAAU;YAC5B,cAAc,MAAM,YAAY;8BAChC;iBACA;uBACA;sBACA;4BACA;oBACA;QACF,IAAI;QAAC;QAAkB,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;QAAkB;QAAK;QAAW;QAAU;QAAgB;KAAO;IAChI,IAAI,KAAK,CAAA,GAAA,YAAI,EAAE,MAAM,EAAE;IACvB,CAAA,GAAA,yCAAM,EAAE,GAAG,CAAC,OAAO;IAEnB,IAAI,aAAC,SAAS,EAAC,GAAG,CAAA,GAAA,cAAM,EAAE;QACxB,GAAG,KAAK;YACR;QACA,kBAAkB;IACpB,GAAG,OAAO;IAEV,kCAAkC;IAClC,IAAI,eACF,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,IAAI,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM;IAGzF,IAAI,CAAA,GAAA,sBAAc,OAAO,kBAAkB,OACzC,UAAU,IAAI,GAAG;IAGnB,IAAI,UAAC,MAAM,EAAE,WAAW,aAAa,EAAC,GAAG,MAAM,cAAc,IAAI,CAAC;IAClE,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAChE,IAAI,kBAAkB,CAAA,GAAA,cAAM,EAAE;YACX;YAAA;QAAjB,IAAI,aAAa,CAAA,4CAAA,iCAAA,MAAM,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,qBAA7C,qDAAA,+BAAsD,SAAS,cAA/D,sDAAA,2CAAmE;QACpF,OAAO,iBAAiB,SAAS,gBAAgB,MAAM,CAAC,GAAG,cAAc,IAAI,CAAC,EAAE;wBAAC;QAAU,KAAK;IAClG,uDAAuD;IACvD,GAAG;QAAC;QAAe;QAAQ,MAAM,UAAU,CAAC,OAAO;KAAC;IAEpD,IAAI,mBAAmB,CAAA,GAAA,qBAAa,EAAE;IAEtC,uGAAuG;IACvG,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,iBACF,CAAA,GAAA,eAAO,EAAE,iBAAiB,aAAa;IAE3C,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,WAAW,CAAA,GAAA,iBAAS,EAClB,WACA,kBACA;YACE,qDAAqD;YACrD,oBAAoB;gBAAC,gBAAgB,CAAC,mBAAmB;gBAAE,SAAS,CAAC,mBAAmB;aAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACjH;IAEJ;AACF", "sources": ["packages/@react-aria/table/src/useTable.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce} from '@react-aria/live-announcer';\nimport {GridAria, GridProps, useGrid} from '@react-aria/grid';\nimport {gridIds} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key, LayoutDelegate, Rect, RefObject, Size} from '@react-types/shared';\nimport {mergeProps, useDescription, useId, useUpdateEffect} from '@react-aria/utils';\nimport {TableKeyboardDelegate} from './TableKeyboardDelegate';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState, TreeGridState} from '@react-stately/table';\nimport {useCollator, useLocale, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface AriaTableProps extends GridProps {\n  /** The layout object for the table. Computes what content is visible and how to position and style them. */\n  layoutDelegate?: LayoutDelegate,\n  /** @deprecated - Use layoutDelegate instead. */\n  layout?: DeprecatedLayout\n}\n\ninterface DeprecatedLayout {\n  getLayoutInfo(key: Key): DeprecatedLayoutInfo,\n  getContentSize(): Size,\n  virtualizer: DeprecatedVirtualizer\n}\n\ninterface DeprecatedLayoutInfo {\n  rect: Rect\n}\n\ninterface DeprecatedVirtualizer {\n  visibleRect: Rect\n}\n\n/**\n * Provides the behavior and accessibility implementation for a table component.\n * A table displays data in rows and columns and enables a user to navigate its contents via directional navigation keys,\n * and optionally supports row selection and sorting.\n * @param props - Props for the table.\n * @param state - State for the table, as returned by `useTableState`.\n * @param ref - The ref attached to the table element.\n */\nexport function useTable<T>(props: AriaTableProps, state: TableState<T> | TreeGridState<T>, ref: RefObject<HTMLElement | null>): GridAria {\n  let {\n    keyboardDelegate,\n    isVirtualized,\n    layoutDelegate,\n    layout\n  } = props;\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let {direction} = useLocale();\n  let disabledBehavior = state.selectionManager.disabledBehavior;\n  let delegate = useMemo(() => keyboardDelegate || new TableKeyboardDelegate({\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    disabledBehavior,\n    ref,\n    direction,\n    collator,\n    layoutDelegate,\n    layout\n  }), [keyboardDelegate, state.collection, state.disabledKeys, disabledBehavior, ref, direction, collator, layoutDelegate, layout]);\n  let id = useId(props.id);\n  gridIds.set(state, id);\n\n  let {gridProps} = useGrid({\n    ...props,\n    id,\n    keyboardDelegate: delegate\n  }, state, ref);\n\n  // Override to include header rows\n  if (isVirtualized) {\n    gridProps['aria-rowcount'] = state.collection.size + state.collection.headerRows.length;\n  }\n\n  if (tableNestedRows() && 'expandedKeys' in state) {\n    gridProps.role = 'treegrid';\n  }\n\n  let {column, direction: sortDirection} = state.sortDescriptor || {};\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n  let sortDescription = useMemo(() => {\n    let columnName = state.collection.columns.find(c => c.key === column)?.textValue ?? '';\n    return sortDirection && column ? stringFormatter.format(`${sortDirection}Sort`, {columnName}) : undefined;\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [sortDirection, column, state.collection.columns]);\n\n  let descriptionProps = useDescription(sortDescription);\n\n  // Only announce after initial render, tabbing to the table will tell you the initial sort info already\n  useUpdateEffect(() => {\n    if (sortDescription) {\n      announce(sortDescription, 'assertive', 500);\n    }\n  }, [sortDescription]);\n\n  return {\n    gridProps: mergeProps(\n      gridProps,\n      descriptionProps,\n      {\n        // merge sort description with long press information\n        'aria-describedby': [descriptionProps['aria-describedby'], gridProps['aria-describedby']].filter(Boolean).join(' ')\n      }\n    )\n  };\n}\n"], "names": [], "version": 3, "file": "useTable.module.js.map"}