import React, { useState } from 'react'
import DataUpload from '../../components/DataInput/DataUpload'
import ManualDataEntry from '../../components/DataInput/ManualDataEntry'
import EquipmentHealthChart from '../../components/Charts/EquipmentHealthChart'
import TrajectoryPlot3D from '../../components/Charts/TrajectoryPlot3D'
import KPIDashboard from '../../components/Charts/KPIDashboard'
import DataExportModal from '../../components/DataSharing/DataExportModal'
import AdvancedEquipmentStatus from '../../components/EquipmentStatus/AdvancedEquipmentStatus'
import './Dashboard.css'

const Dashboard = () => {
  const [uploadedData, setUploadedData] = useState([])
  const [manualData, setManualData] = useState([])
  const [showDataInput, setShowDataInput] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [activeView, setActiveView] = useState('overview')

  const handleDataUpload = (files) => {
    setUploadedData(files)
  }

  const handleManualDataSubmit = (data) => {
    setManualData(prev => [...prev, data])
  }

  const allData = [...uploadedData.flatMap(f => f.data), ...manualData]

  return (
    <div className="dashboard-container">
      <div className="page-header">
        <h2 className="page-title">IDX - Equipment Status Monitor</h2>
        <p className="page-subtitle">Stage 1: Advanced Data Visualization & Input System - Version 1.2</p>
        <div className="header-controls">
          <button
            className="control-btn"
            onClick={() => setShowDataInput(!showDataInput)}
          >
            📊 Data Input
          </button>
          <button
            className="control-btn"
            onClick={() => setShowExportModal(true)}
          >
            📤 Export Data
          </button>
          <div className="view-selector">
            <select
              value={activeView}
              onChange={(e) => setActiveView(e.target.value)}
            >
              <option value="overview">Overview</option>
              <option value="detailed">Detailed Analysis</option>
              <option value="technical">Technical Data</option>
            </select>
          </div>
        </div>
      </div>

      {/* Data Input Section */}
      {showDataInput && (
        <div className="data-input-section">
          <div className="input-tabs">
            <h3>Data Input & Management</h3>
          </div>
          <div className="input-grid">
            <DataUpload onDataLoad={handleDataUpload} />
            <ManualDataEntry onDataSubmit={handleManualDataSubmit} dataType="equipment" />
          </div>
        </div>
      )}

      {/* Progressive Disclosure Views */}
      {activeView === 'overview' && (
        <div className="overview-section">
          <KPIDashboard />
          <div className="quick-stats">
            <div className="stat-card">
              <h4>Data Sources</h4>
              <p>{uploadedData.length} files uploaded</p>
              <p>{manualData.length} manual entries</p>
            </div>
            <div className="stat-card">
              <h4>System Status</h4>
              <p>95% operational</p>
              <p>52 labs active</p>
            </div>
          </div>
        </div>
      )}

      {activeView === 'detailed' && (
        <div className="detailed-section">
          <div className="chart-grid">
            <EquipmentHealthChart data={allData} />
            <TrajectoryPlot3D />
          </div>
        </div>
      )}

      {activeView === 'technical' && (
        <div className="technical-section">
          <AdvancedEquipmentStatus />
        </div>
      )}

          {/* Laboratory Network */}
          <div className="content-section">
            <div className="section-header">
              <h3 className="section-title">DRDO Laboratory Network</h3>
              <span className="section-badge">52 Labs</span>
            </div>
            <div className="section-content">
              <div className="status-grid">
                <div className="status-item">
                  <span className="system-name">Active Labs</span>
                  <span className="status-value">52/52</span>
                </div>
                <div className="status-item">
                  <span className="system-name">Test Ranges</span>
                  <span className="status-value">8 Active</span>
                </div>
                <div className="status-item">
                  <span className="system-name">Research Centers</span>
                  <span className="status-value">15 Active</span>
                </div>
                <div className="status-item">
                  <span className="system-name">Field Stations</span>
                  <span className="status-value">12 Active</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Export Modal */}
      {showExportModal && (
        <DataExportModal
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
          data={allData}
          title="DRDO Equipment Status Data"
        />
      )}
    </div>
  )
}

export default Dashboard
