import React from 'react'
import ChartWidget from '../../components/Charts/ChartWidget'
import DataTable from '../../components/DataTable/DataTable'
import StatsWidget from '../../components/Widgets/StatsWidget'
import './Dashboard.css'

const Dashboard = () => {
  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>Dashboard Overview</h2>
        <p>Welcome to the Interactive Data Visualization Dashboard</p>
      </div>
      
      <div className="stats-grid">
        <StatsWidget title="Total Users" value="1,234" trend="+12%" />
        <StatsWidget title="Revenue" value="$45,678" trend="+8%" />
        <StatsWidget title="Orders" value="567" trend="-3%" />
        <StatsWidget title="Conversion Rate" value="3.2%" trend="+15%" />
      </div>

      <div className="charts-grid">
        <div className="chart-container">
          <ChartWidget title="Sales Trend" type="line" />
        </div>
        <div className="chart-container">
          <ChartWidget title="Revenue Distribution" type="pie" />
        </div>
        <div className="chart-container">
          <ChartWidget title="Monthly Performance" type="bar" />
        </div>
        <div className="chart-container">
          <ChartWidget title="User Activity" type="area" />
        </div>
      </div>

      <div className="data-section">
        <DataTable title="Recent Transactions" />
      </div>
    </div>
  )
}

export default Dashboard
