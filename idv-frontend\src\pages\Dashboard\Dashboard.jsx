import React from 'react'
import './Dashboard.css'

const Dashboard = () => {
  return (
    <div className="dashboard-container">
      <div className="page-header">
        <h2 className="page-title">IDX - Equipment Status Monitor</h2>
        <p className="page-subtitle">Stage 1: Layout Design & Structure Testing - Version 1.05</p>
      </div>

      <div className="content-grid">
        {/* Indigenous Defense Systems */}
        <div className="content-section">
          <div className="section-header">
            <h3 className="section-title">Indigenous Defense Systems</h3>
            <span className="section-badge">Atmanirbhar Bharat</span>
          </div>
          <div className="section-content">
            <div className="status-grid">
              <div className="status-item">
                <span className="system-name">BrahMos Missile System</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Agni Missile Series</span>
                <span className="status-value">Testing 2</span>
              </div>
              <div className="status-item">
                <span className="system-name">Tejas Fighter Aircraft</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Arjun Main Battle Tank</span>
                <span className="status-value">Testing 2</span>
              </div>
            </div>
          </div>
        </div>

        {/* Laboratory Network */}
        <div className="content-section">
          <div className="section-header">
            <h3 className="section-title">DRDO Laboratory Network</h3>
            <span className="section-badge">52 Labs</span>
          </div>
          <div className="section-content">
            <div className="status-grid">
              <div className="status-item">
                <span className="system-name">Active Labs</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Test Ranges</span>
                <span className="status-value">Testing 2</span>
              </div>
              <div className="status-item">
                <span className="system-name">Research Centers</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Field Stations</span>
                <span className="status-value">Testing 2</span>
              </div>
            </div>
          </div>
        </div>

        {/* Multi-Domain Operations */}
        <div className="content-section">
          <div className="section-header">
            <h3 className="section-title">Multi-Domain Operations</h3>
            <span className="section-badge">Strategic</span>
          </div>
          <div className="section-content">
            <div className="status-grid">
              <div className="status-item">
                <span className="system-name">Land Systems</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Air Defense</span>
                <span className="status-value">Testing 2</span>
              </div>
              <div className="status-item">
                <span className="system-name">Naval Systems</span>
                <span className="status-value">Testing 1</span>
              </div>
              <div className="status-item">
                <span className="system-name">Space & Cyber</span>
                <span className="status-value">Testing 2</span>
              </div>
            </div>
          </div>
        </div>

        {/* Data Visualization Areas */}
        <div className="content-section full-width">
          <div className="section-header">
            <h3 className="section-title">Visualization Components (Stage 1)</h3>
            <span className="section-badge">Layout Ready</span>
          </div>
          <div className="section-content">
            <div className="visualization-grid">
              <div className="viz-placeholder">
                <h4>Equipment Health Charts</h4>
                <p>Layout Area: D3.js Line Charts</p>
              </div>
              <div className="viz-placeholder">
                <h4>3D Trajectory Plots</h4>
                <p>Layout Area: Plotly.js 3D Visualization</p>
              </div>
              <div className="viz-placeholder">
                <h4>Mission Gantt Charts</h4>
                <p>Layout Area: Timeline Visualization</p>
              </div>
              <div className="viz-placeholder">
                <h4>Resource Flow Diagrams</h4>
                <p>Layout Area: Sankey Diagrams</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
