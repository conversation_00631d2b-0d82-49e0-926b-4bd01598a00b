import React, { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { useRealTime } from '../../context/RealTimeContext'
import { logAuditEvent } from '../../utils/audit'
import './DashboardV17.css'

const Dashboard = () => {
  const { user } = useAuth()
  const {
    equipmentData,
    missionData,
    connectionStatus,
    autoRefresh,
    toggleAutoRefresh,
    addAlert,
    getStatistics
  } = useRealTime()
  const [missionStatus, setMissionStatus] = useState([])
  const [lastUpdate, setLastUpdate] = useState(new Date())

  // Mock mission status data
  const mockMissionData = [
    {
      id: 'M001',
      name: 'Operation Thunder',
      status: 'Active',
      priority: 'High',
      progress: 85,
      location: 'Rajasthan',
      commander: 'Col<PERSON> Sharma',
      equipment: ['BrahMos', 'Radar System'],
      lastUpdate: '2 min ago'
    },
    {
      id: 'M002',
      name: 'Defense Grid Alpha',
      status: 'Standby',
      priority: 'Medium',
      progress: 100,
      location: 'Tamil Nadu',
      commander: 'Dr. <PERSON><PERSON>',
      equipment: ['Agni-V', 'Communication Array'],
      lastUpdate: '5 min ago'
    },
    {
      id: 'M003',
      name: 'Surveillance Beta',
      status: 'Planning',
      priority: 'Low',
      progress: 45,
      location: 'Karnataka',
      commander: 'Rajesh Kumar',
      equipment: ['Tejas', 'Satellite Uplink'],
      lastUpdate: '10 min ago'
    }
  ]

  // Mock equipment heatmap data
  const mockEquipmentData = [
    { name: 'BrahMos', status: 'Operational', health: 98, location: 'Rajasthan', temp: 45 },
    { name: 'Agni-V', status: 'Operational', health: 95, location: 'Tamil Nadu', temp: 42 },
    { name: 'Tejas', status: 'Maintenance', health: 87, location: 'Karnataka', temp: 38 },
    { name: 'Radar-1', status: 'Operational', health: 92, location: 'Punjab', temp: 41 },
    { name: 'Comm-Array', status: 'Warning', health: 89, location: 'Gujarat', temp: 47 },
    { name: 'Satellite-1', status: 'Operational', health: 96, location: 'Andhra Pradesh', temp: 39 }
  ]

  // Initialize dashboard data
  useEffect(() => {
    setMissionStatus(mockMissionData)
    logAuditEvent('DATA_VIEW', 'Dashboard accessed', 'DASHBOARD', 'PUBLIC', user?.id, user?.name)

    // Update last update time when real-time data changes
    const interval = setInterval(() => {
      setLastUpdate(new Date())
    }, 1000)

    return () => clearInterval(interval)
  }, [user])

  // Merge real-time equipment data with mock data
  const mergedEquipmentData = mockEquipmentData.map(mockItem => {
    const realTimeItem = equipmentData.find(rt => rt.id === mockItem.name.toLowerCase().replace(/[^a-z0-9]/g, '_'))
    if (realTimeItem) {
      return {
        ...mockItem,
        health: realTimeItem.health || mockItem.health,
        temp: realTimeItem.temperature || mockItem.temp,
        status: realTimeItem.status || mockItem.status
      }
    }
    return mockItem
  })

  return (
    <div className="dashboard-v17">
      {/* Professional Dashboard Header */}
      <div className="dashboard-header-section">
        <div className="dashboard-title-area">
          <h1 className="main-title">DRDO Command Center</h1>
          <p className="main-subtitle">Defence Research and Development Organisation - Mission Control Dashboard</p>
        </div>
        <div className="dashboard-status-area">
          <div className="status-grid">
            <div className="status-card">
              <span className="status-label">System Status</span>
              <span className="status-value operational">OPERATIONAL</span>
            </div>
            <div className="status-card">
              <span className="status-label">Security Level</span>
              <span className="status-value secure">{user?.clearanceLevel || 'RESTRICTED'}</span>
            </div>
            <div className="status-card">
              <span className="status-label">Last Updated</span>
              <span className="status-value">{lastUpdate.toLocaleTimeString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Mission Status Cards */}
      <div className="mission-status-section">
        <div className="section-header">
          <h2 className="section-title">🎯 Active Missions</h2>
          <div className="section-controls">
            <span className="mission-count">{missionStatus.length} Active</span>
          </div>
        </div>
        <div className="mission-cards-grid">
          {missionStatus.map(mission => (
            <div key={mission.id} className={`mission-card ${mission.status.toLowerCase()}`}>
              <div className="mission-header">
                <div className="mission-info">
                  <h3 className="mission-name">{mission.name}</h3>
                  <span className="mission-id">{mission.id}</span>
                </div>
                <div className={`mission-status ${mission.status.toLowerCase()}`}>
                  {mission.status}
                </div>
              </div>
              <div className="mission-details">
                <div className="detail-row">
                  <span className="detail-label">Commander:</span>
                  <span className="detail-value">{mission.commander}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Location:</span>
                  <span className="detail-value">{mission.location}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Priority:</span>
                  <span className={`priority ${mission.priority.toLowerCase()}`}>{mission.priority}</span>
                </div>
              </div>
              <div className="mission-progress">
                <div className="progress-header">
                  <span>Progress</span>
                  <span>{mission.progress}%</span>
                </div>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${mission.progress}%` }}
                  ></div>
                </div>
              </div>
              <div className="mission-equipment">
                <span className="equipment-label">Equipment:</span>
                <div className="equipment-tags">
                  {mission.equipment.map((item, idx) => (
                    <span key={idx} className="equipment-tag">{item}</span>
                  ))}
                </div>
              </div>
              <div className="mission-footer">
                <span className="last-update">Updated {mission.lastUpdate}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Equipment Heatmap */}
      <div className="equipment-heatmap-section">
        <div className="section-header">
          <h2 className="section-title">🔧 Equipment Health Monitor</h2>
          <div className="section-controls">
            <span className="equipment-count">{equipmentData.length} Systems</span>
          </div>
        </div>
        <div className="heatmap-grid">
          {mergedEquipmentData.map((equipment, idx) => (
            <div key={idx} className={`equipment-tile ${equipment.status.toLowerCase()}`}>
              <div className="equipment-header">
                <h4 className="equipment-name">{equipment.name}</h4>
                <div className={`status-badge ${equipment.status.toLowerCase()}`}>
                  {equipment.status}
                </div>
              </div>
              <div className="equipment-metrics">
                <div className="metric">
                  <span className="metric-label">Health</span>
                  <div className="metric-bar">
                    <div
                      className="metric-fill health"
                      style={{ width: `${equipment.health}%` }}
                    ></div>
                  </div>
                  <span className="metric-value">{equipment.health}%</span>
                </div>
                <div className="metric">
                  <span className="metric-label">Temp</span>
                  <span className="metric-value">{equipment.temp}°C</span>
                </div>
              </div>
              <div className="equipment-location">
                📍 {equipment.location}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* India Map with Lab Locations */}
      <div className="india-map-section">
        <div className="section-header">
          <h2 className="section-title">🗺️ DRDO Lab Network</h2>
        </div>
        <div className="map-container">
          <div className="india-map-placeholder">
            <div className="map-info">
              <h3>DRDO Laboratory Network</h3>
              <p>52 Active Laboratories across India</p>
              <div className="lab-locations">
                <div className="location-item">🔴 Delhi - HQ & 8 Labs</div>
                <div className="location-item">🟡 Bangalore - 12 Labs</div>
                <div className="location-item">🟢 Hyderabad - 9 Labs</div>
                <div className="location-item">🔵 Pune - 7 Labs</div>
                <div className="location-item">🟣 Chennai - 6 Labs</div>
                <div className="location-item">🟠 Others - 10 Labs</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="dashboard-footer">
        <div className="footer-content">
          <span>© 2025 DRDO | IDX v1.7 | Frontend Dev Mode</span>
          <span>Logged in as: {user?.name} ({user?.role})</span>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
