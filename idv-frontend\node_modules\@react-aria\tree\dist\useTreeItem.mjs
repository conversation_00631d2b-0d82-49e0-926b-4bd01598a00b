import $f4zHe$intlStringsmodulejs from "./intlStrings.mjs";
import {useGridListItem as $f4zHe$useGridListItem} from "@react-aria/gridlist";
import {useLabels as $f4zHe$useLabels} from "@react-aria/utils";
import {useLocalizedStringFormatter as $f4zHe$useLocalizedStringFormatter} from "@react-aria/i18n";


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}
/*
 * Copyright 2024 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 



function $b41e0bc5ca49db3d$export$e4bcc4675b2b123a(props, state, ref) {
    let { node: node } = props;
    let gridListAria = (0, $f4zHe$useGridListItem)(props, state, ref);
    let isExpanded = gridListAria.rowProps['aria-expanded'] === true;
    let stringFormatter = (0, $f4zHe$useLocalizedStringFormatter)((0, ($parcel$interopDefault($f4zHe$intlStringsmodulejs))), '@react-aria/tree');
    let labelProps = (0, $f4zHe$useLabels)({
        'aria-label': isExpanded ? stringFormatter.format('collapse') : stringFormatter.format('expand'),
        'aria-labelledby': gridListAria.rowProps.id
    });
    let expandButtonProps = {
        onPress: ()=>{
            if (!gridListAria.isDisabled) {
                state.toggleKey(node.key);
                state.selectionManager.setFocused(true);
                state.selectionManager.setFocusedKey(node.key);
            }
        },
        excludeFromTabOrder: true,
        preventFocusOnPress: true,
        'data-react-aria-prevent-focus': true,
        ...labelProps
    };
    // TODO: should it return a state specifically for isExpanded? Or is aria attribute sufficient?
    return {
        ...gridListAria,
        expandButtonProps: expandButtonProps
    };
}


export {$b41e0bc5ca49db3d$export$e4bcc4675b2b123a as useTreeItem};
//# sourceMappingURL=useTreeItem.module.js.map
