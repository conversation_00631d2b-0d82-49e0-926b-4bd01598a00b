.kpi-dashboard {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.dashboard-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.chart-tabs {
  display: flex;
  gap: 5px;
}

.tab-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background-color: #e9ecef;
}

.tab-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.kpi-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.kpi-card {
  background-color: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.kpi-card.updating {
  animation: pulse 0.5s ease-in-out;
}

.kpi-card.updating::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
  animation: shimmer 1s ease-in-out;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.kpi-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.kpi-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-trend {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 3px;
}

.kpi-trend.positive {
  background-color: #d4edda;
  color: #155724;
}

.kpi-trend.negative {
  background-color: #f8d7da;
  color: #721c24;
}

.kpi-trend.neutral {
  background-color: #fff3cd;
  color: #856404;
}

.kpi-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-top: 12px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.indicator-dot.success {
  background-color: #059669;
  box-shadow: 0 0 8px rgba(5, 150, 105, 0.3);
}

.indicator-dot.active {
  background-color: #4A90E2;
  box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
}

.indicator-dot.warning {
  background-color: #F5A623;
  box-shadow: 0 0 8px rgba(245, 166, 35, 0.3);
}

.indicator-text {
  font-size: 11px;
  font-weight: 500;
  color: #6B7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-content {
  margin-bottom: 20px;
}

.chart-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-item {
  background-color: #fafafa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
}

.chart-item h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.chart-single {
  background-color: #fafafa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
}

.chart-single h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.custom-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  border: none;
}

.tooltip-label {
  margin: 0 0 5px 0;
  font-weight: 500;
}

.dashboard-footer {
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #666;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .chart-tabs {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-grid {
    grid-template-columns: 1fr;
  }
  
  .kpi-summary {
    grid-template-columns: 1fr 1fr;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 5px;
  }
}
