import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'

// Import all page components
import Dashboard from '../pages/Dashboard/Dashboard'
import Settings from '../pages/Settings/Settings'
import Profile from '../pages/Profile/Profile'
import Users from '../pages/Users/<USER>'
import Roles from '../pages/Roles/Roles'
import DataTransfer from '../pages/DataTransfer/DataTransfer'
import VisualSandbox from '../pages/VisualSandbox/VisualSandbox'
import AuditLog from '../pages/AuditLog/AuditLog'

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole = 'guest' }) => {
  const { isAuthenticated, hasPermission } = useAuth()

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  if (!hasPermission(requiredRole)) {
    return (
      <div className="unauthorized-access">
        <div className="unauthorized-content">
          <h2>🔒 Access Denied</h2>
          <p>You don't have sufficient permissions to access this page.</p>
          <p>Required role: <strong>{requiredRole}</strong></p>
        </div>
      </div>
    )
  }

  return children
}

const AppRouter = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<div>Login Page</div>} />
      
      {/* Protected Routes */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <Navigate to="/dashboard" replace />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Dashboard />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Settings />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/profile" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Profile />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/users" 
        element={
          <ProtectedRoute requiredRole="admin">
            <Users />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/roles" 
        element={
          <ProtectedRoute requiredRole="admin">
            <Roles />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/data-transfer" 
        element={
          <ProtectedRoute requiredRole="engineer">
            <DataTransfer />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/visual-sandbox" 
        element={
          <ProtectedRoute requiredRole="scientist">
            <VisualSandbox />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/audit-log" 
        element={
          <ProtectedRoute requiredRole="admin">
            <AuditLog />
          </ProtectedRoute>
        } 
      />

      {/* Catch all route */}
      <Route 
        path="*" 
        element={
          <div className="not-found">
            <h2>404 - Page Not Found</h2>
            <p>The requested page could not be found.</p>
          </div>
        } 
      />
    </Routes>
  )
}

export default AppRouter
