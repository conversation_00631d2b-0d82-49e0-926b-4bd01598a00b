import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'

// Import all page components
import Login from '../pages/Login/Login'
import Dashboard from '../pages/Dashboard/Dashboard'
import Settings from '../pages/Settings/Settings'
import Profile from '../pages/Profile/Profile'
import Users from '../pages/Users/<USER>'
import Roles from '../pages/Roles/Roles'
import DataTransfer from '../pages/DataTransfer/DataTransfer'
import VisualSandbox from '../pages/VisualSandbox/VisualSandbox'
import AuditLog from '../pages/AuditLog/AuditLog'

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole = 'guest' }) => {
  const { isAuthenticated, hasPermission, isDevelopmentMode } = useAuth()

  // In development mode, allow access even if not authenticated (auto-login will handle it)
  if (!isAuthenticated && !isDevelopmentMode) {
    return <Navigate to="/login" replace />
  }

  // If authenticated but doesn't have permission
  if (isAuthenticated && !hasPermission(requiredRole)) {
    return (
      <div className="unauthorized-access" style={{
        padding: '40px',
        textAlign: 'center',
        background: '#FEE2E2',
        color: '#DC2626',
        margin: '20px',
        borderRadius: '8px',
        border: '1px solid #FECACA'
      }}>
        <div className="unauthorized-content">
          <h2>🔒 Access Denied</h2>
          <p>You don't have sufficient permissions to access this page.</p>
          <p>Required role: <strong>{requiredRole}</strong></p>
        </div>
      </div>
    )
  }

  return children
}

const AppRouter = () => {
  const { isAuthenticated } = useAuth()

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />
      
      {/* Protected Routes */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <Navigate to="/dashboard" replace />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Dashboard />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Settings />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/profile" 
        element={
          <ProtectedRoute requiredRole="guest">
            <Profile />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/users" 
        element={
          <ProtectedRoute requiredRole="admin">
            <Users />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/roles" 
        element={
          <ProtectedRoute requiredRole="admin">
            <Roles />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/data-transfer" 
        element={
          <ProtectedRoute requiredRole="engineer">
            <DataTransfer />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/visual-sandbox" 
        element={
          <ProtectedRoute requiredRole="scientist">
            <VisualSandbox />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/audit-log" 
        element={
          <ProtectedRoute requiredRole="admin">
            <AuditLog />
          </ProtectedRoute>
        } 
      />

      {/* Catch all route */}
      <Route
        path="*"
        element={
          <div className="not-found" style={{
            padding: '40px',
            textAlign: 'center',
            background: '#F8FAFC',
            color: '#1A3C5E',
            margin: '20px',
            borderRadius: '8px',
            border: '1px solid #E2E8F0'
          }}>
            <h2>404 - Page Not Found</h2>
            <p>The requested page could not be found.</p>
            <p><a href="/dashboard" style={{ color: '#FF9933' }}>Return to Dashboard</a></p>
          </div>
        }
      />
    </Routes>
  )
}

export default AppRouter
