import React from 'react'
import './ChartWidget.css'

const ChartWidget = ({ title, type }) => {
  const getChartPlaceholder = () => {
    switch (type) {
      case 'line':
        return (
          <div className="chart-placeholder line-chart">
            <div className="chart-line"></div>
            <div className="chart-axes">
              <div className="y-axis"></div>
              <div className="x-axis"></div>
            </div>
          </div>
        )
      case 'pie':
        return (
          <div className="chart-placeholder pie-chart">
            <div className="pie-slice slice-1"></div>
            <div className="pie-slice slice-2"></div>
            <div className="pie-slice slice-3"></div>
            <div className="pie-slice slice-4"></div>
          </div>
        )
      case 'bar':
        return (
          <div className="chart-placeholder bar-chart">
            <div className="bar" style={{ height: '60%' }}></div>
            <div className="bar" style={{ height: '80%' }}></div>
            <div className="bar" style={{ height: '45%' }}></div>
            <div className="bar" style={{ height: '90%' }}></div>
            <div className="bar" style={{ height: '70%' }}></div>
          </div>
        )
      case 'area':
        return (
          <div className="chart-placeholder area-chart">
            <div className="area-fill"></div>
            <div className="area-line"></div>
          </div>
        )
      default:
        return <div className="chart-placeholder">Chart Placeholder</div>
    }
  }

  return (
    <div className="chart-widget">
      <div className="chart-header">
        <h3>{title}</h3>
        <span className="chart-type">{type.toUpperCase()}</span>
      </div>
      <div className="chart-content">
        {getChartPlaceholder()}
      </div>
    </div>
  )
}

export default ChartWidget
