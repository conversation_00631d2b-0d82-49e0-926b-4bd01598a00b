{"name": "idv-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@reduxjs/toolkit": "^2.8.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "autoprefixer": "^10.4.21", "axe-core": "^4.10.3", "crypto-js": "^4.2.0", "d3": "^7.9.0", "file-saver": "^2.0.5", "i18next": "^25.3.1", "jsdom": "^26.1.0", "jspdf": "^3.0.1", "papaparse": "^5.5.3", "plotly.js": "^3.0.1", "postcss": "^8.5.6", "react": "^18.2.0", "react-aria": "^3.41.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.5.2", "react-i18next": "^15.6.0", "react-redux": "^9.2.0", "react-router-dom": "^6.8.0", "recharts": "^3.0.2", "tailwindcss": "^4.1.11", "vitest": "^3.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^7.0.0"}}