import React, { useState, useCallback, useMemo } from 'react'
import { useDropzone } from 'react-dropzone'
import Papa from 'papaparse'
import './DataUpload.css'

const DataUpload = ({ onDataLoad, acceptedTypes = ['.csv', '.json', '.xlsx'] }) => {
  const [uploadStatus, setUploadStatus] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState([])

  const processCSV = useCallback((file) => {
    return new Promise((resolve, reject) => {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        reject(new Error('File size exceeds 10MB limit'))
        return
      }

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            const criticalErrors = results.errors.filter(err => err.type === 'Delimiter')
            if (criticalErrors.length > 0) {
              reject(new Error(`CSV parsing error: ${criticalErrors[0].message}`))
              return
            }
          }

          // Validate data structure
          if (!results.data || results.data.length === 0) {
            reject(new Error('CSV file is empty or contains no valid data'))
            return
          }

          resolve({
            data: results.data,
            meta: {
              ...results.meta,
              rowCount: results.data.length,
              columnCount: results.meta.fields?.length || 0
            },
            type: 'csv',
            filename: file.name,
            size: file.size
          })
        },
        error: (error) => reject(new Error(`File processing failed: ${error.message}`))
      })
    })
  }, [])

  const processJSON = useCallback((file) => {
    return new Promise((resolve, reject) => {
      // Validate file size
      if (file.size > 10 * 1024 * 1024) {
        reject(new Error('File size exceeds 10MB limit'))
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const content = e.target.result
          if (!content || content.trim().length === 0) {
            reject(new Error('JSON file is empty'))
            return
          }

          const jsonData = JSON.parse(content)

          // Validate JSON structure
          if (jsonData === null || jsonData === undefined) {
            reject(new Error('JSON file contains no valid data'))
            return
          }

          resolve({
            data: jsonData,
            meta: {
              type: Array.isArray(jsonData) ? 'array' : 'object',
              itemCount: Array.isArray(jsonData) ? jsonData.length : Object.keys(jsonData).length
            },
            type: 'json',
            filename: file.name,
            size: file.size
          })
        } catch (error) {
          reject(new Error(`JSON parsing error: ${error.message}`))
        }
      }
      reader.onerror = () => reject(new Error('File reading error'))
      reader.readAsText(file)
    })
  }, [])

  const onDrop = useCallback(async (acceptedFiles) => {
    setIsLoading(true)
    setUploadStatus('Processing files...')

    try {
      const processedFiles = []
      
      for (const file of acceptedFiles) {
        const fileExtension = file.name.toLowerCase().split('.').pop()
        let processedData

        switch (fileExtension) {
          case 'csv':
            processedData = await processCSV(file)
            break
          case 'json':
            processedData = await processJSON(file)
            break
          default:
            throw new Error(`Unsupported file type: ${fileExtension}`)
        }

        processedFiles.push(processedData)
      }

      setUploadedFiles(prev => [...prev, ...processedFiles])
      setUploadStatus(`Successfully processed ${processedFiles.length} file(s)`)
      
      // Pass data to parent component
      if (onDataLoad) {
        onDataLoad(processedFiles)
      }

    } catch (error) {
      setUploadStatus(`Error: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }, [onDataLoad])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxSize: 10 * 1024 * 1024, // 10MB limit
    multiple: true
  })

  const removeFile = (index) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index)
    setUploadedFiles(newFiles)
    if (onDataLoad) {
      onDataLoad(newFiles)
    }
  }

  return (
    <div className="data-upload-container">
      <div className="upload-header">
        <h3>Data Upload System</h3>
        <span className="security-badge">DRDO Secure Upload</span>
      </div>

      <div
        {...getRootProps()}
        className={`dropzone ${isDragActive ? 'active' : ''} ${isLoading ? 'loading' : ''}`}
        role="button"
        tabIndex={0}
        aria-label="File upload area. Drag and drop files here or click to select files"
        aria-describedby="upload-instructions"
      >
        <input {...getInputProps()} aria-label="File input" />
        <div className="dropzone-content">
          <div className="upload-icon" aria-hidden="true">📁</div>
          {isDragActive ? (
            <p id="upload-instructions">Drop files here...</p>
          ) : (
            <div id="upload-instructions">
              <p>Drag & drop data files here, or click to select</p>
              <p className="file-types">Supported: CSV, JSON, Excel (.xlsx)</p>
              <p className="file-limit">Max size: 10MB per file</p>
            </div>
          )}
        </div>
      </div>

      {isLoading && (
        <div className="loading-indicator">
          <div className="spinner"></div>
          <span>Processing data...</span>
        </div>
      )}

      {uploadStatus && (
        <div className={`upload-status ${uploadStatus.includes('Error') ? 'error' : 'success'}`}>
          {uploadStatus}
        </div>
      )}

      {uploadedFiles.length > 0 && (
        <div className="uploaded-files">
          <h4>Uploaded Files ({uploadedFiles.length})</h4>
          <div className="file-list">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="file-item">
                <div className="file-info">
                  <span className="file-name">{file.filename}</span>
                  <span className="file-type">{file.type.toUpperCase()}</span>
                  <span className="file-records">
                    {Array.isArray(file.data) ? file.data.length : 'N/A'} records
                  </span>
                </div>
                <button 
                  className="remove-file"
                  onClick={() => removeFile(index)}
                  title="Remove file"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default DataUpload
