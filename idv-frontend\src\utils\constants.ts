// Constants for IDX Dashboard v1.5

export const APP_VERSION = '1.5';
export const APP_NAME = 'IDX Dashboard';
export const APP_DESCRIPTION = 'Interactive Dashboard for eXcellence';

export const DRDO_COLORS = {
  navy: '#1A3C5E',
  blue: '#4A90E2',
  orange: '#F5A623',
  gray: '#F5F7FA',
  white: '#FFFFFF',
} as const;

export const SECURITY_LEVELS = {
  PUBLIC: 'PUBLIC',
  RESTRICTED: 'RESTRICTED',
  CONFIDENTIAL: 'CONFIDENTIAL',
  SECRET: 'SECRET',
} as const;

export const USER_ROLES = {
  GUEST: 'guest',
  ANALYST: 'analyst',
  ENGINEER: 'engineer',
  SCIENTIST: 'scientist',
  ADMIN: 'admin',
} as const;

export const THEMES = {
  FORMAL_WHITE: 'formal-white',
  MINIMAL: 'minimal',
  DARK: 'dark',
  DRACULA: 'dracula',
} as const;

export const LANGUAGES = {
  ENGLISH: 'en',
  HINDI: 'hi',
  TAMIL: 'ta',
  TELUGU: 'te',
} as const;

export const AUDIT_ACTIONS = {
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  DATA_VIEW: 'DATA_VIEW',
  DATA_EXPORT: 'DATA_EXPORT',
  DATA_IMPORT: 'DATA_IMPORT',
  SETTINGS_CHANGE: 'SETTINGS_CHANGE',
  ROLE_SWITCH: 'ROLE_SWITCH',
  FILE_UPLOAD: 'FILE_UPLOAD',
  FILE_DOWNLOAD: 'FILE_DOWNLOAD',
  CHART_VIEW: 'CHART_VIEW',
  FILTER_APPLY: 'FILTER_APPLY',
  SEARCH_PERFORM: 'SEARCH_PERFORM',
  THEME_CHANGE: 'THEME_CHANGE',
  SECURITY_VIOLATION: 'SECURITY_VIOLATION',
  SESSION_TIMEOUT: 'SESSION_TIMEOUT',
  NAVIGATION: 'NAVIGATION',
} as const;

export const SESSION_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds
export const MAX_AUDIT_LOGS = 5000;
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const API_ENDPOINTS = {
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  USER_PROFILE: '/api/user/profile',
  AUDIT_LOGS: '/api/audit/logs',
  SYSTEM_STATUS: '/api/system/status',
  EXPORT_DATA: '/api/data/export',
} as const;

export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  AREA: 'area',
  RADAR: 'radar',
  SANKEY: 'sankey',
  HEATMAP: 'heatmap',
  BUBBLE: 'bubble',
  TREE: 'tree',
  FORCE: 'force',
  GEO: 'geo',
  TRAJECTORY_3D: 'trajectory3d',
} as const;

export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: '📊',
    description: 'System overview and key metrics',
    requiredRole: 'guest',
    path: '/dashboard'
  },
  {
    id: 'simulation',
    label: 'Simulation',
    icon: '🚀',
    description: 'Simulation testing and analysis',
    requiredRole: 'engineer',
    path: '/simulation-testing'
  },
  {
    id: 'systems',
    label: 'Systems',
    icon: '⚙️',
    description: 'Weapons and equipment monitoring',
    requiredRole: 'engineer',
    path: '/weapons-systems'
  },
  {
    id: 'mission',
    label: 'Mission',
    icon: '🎯',
    description: 'Mission planning and coordination',
    requiredRole: 'scientist',
    path: '/mission-planning'
  },
  {
    id: 'cyber',
    label: 'Cyber Defense',
    icon: '🛡️',
    description: 'Cybersecurity monitoring',
    requiredRole: 'analyst',
    path: '/cyber-defense'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: '📈',
    description: 'Intelligence and analytics',
    requiredRole: 'scientist',
    path: '/analytics-intel'
  },
  {
    id: 'transfer',
    label: 'Transfer',
    icon: '📤',
    description: 'Secure data transfer',
    requiredRole: 'engineer',
    path: '/transfer'
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: '👤',
    description: 'User profile and settings',
    requiredRole: 'guest',
    path: '/profile'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: '⚙️',
    description: 'System configuration',
    requiredRole: 'guest',
    path: '/settings'
  }
] as const;
